// 核心：统一数据处理 - inject.js通过injector.js专门注入
(function () {
    console.log('[JD-HELPER] Content script started - API interceptor handled by injector.js');

    // --- 数据暂存队列和就绪状态标志 ---
    let apiDataQueue = [];
    let isExtractorReady = false;

    // --- 处理队列中的数据 ---
    function processApiDataQueue() {
        console.log(`[JD-HELPER] Processing unified data queue. Queue length: ${apiDataQueue.length}`);
        while (apiDataQueue.length > 0) {
            const eventDetail = apiDataQueue.shift();
            if (window.unifiedElementExtractor && typeof window.unifiedElementExtractor.processUnifiedData === 'function') {
                console.log('[JD-HELPER] Forwarding queued unified data to extractor:', eventDetail);
                window.unifiedElementExtractor.processUnifiedData(eventDetail);
            } else {
                console.error('[JD-HELPER] FATAL: unifiedElementExtractor.processUnifiedData not available. Re-queuing data.');
                apiDataQueue.unshift(eventDetail);
                break;
            }
        }
    }

    // --- 监听提取器就绪事件 ---
    window.addEventListener('UnifiedExtractorReady', () => {
        console.log('[JD-HELPER] Received UnifiedExtractorReady signal.');
        isExtractorReady = true;
        processApiDataQueue();
    });

    // --- 检查提取器就绪状态（兜底机制） ---
    const checkExtractorReady = () => {
        const extractorStatus = {
            unifiedExtractor: !!window.unifiedExtractor,
            unifiedElementExtractor: !!window.unifiedElementExtractor,
            hasMethod: !!(window.unifiedElementExtractor && typeof window.unifiedElementExtractor.processUnifiedData === 'function')
        };

        console.log('[JD-HELPER] Checking extractor status:', extractorStatus);

        if (!isExtractorReady && extractorStatus.hasMethod) {
            console.log('[JD-HELPER] Detected extractor ready via polling. Processing queued data.');
            isExtractorReady = true;
            processApiDataQueue();
            return true;
        }
        return false;
    };

    // 兜底检查机制
    setTimeout(checkExtractorReady, 50);
    setTimeout(checkExtractorReady, 200);
    setTimeout(checkExtractorReady, 500);

    // 监听来自 inject.js 的统一数据事件
    window.addEventListener('JdUnifiedData', (event) => {
        console.log('[JD-HELPER] Received unified data from inject.js:', event.detail);

        if (isExtractorReady && window.unifiedElementExtractor && typeof window.unifiedElementExtractor.processUnifiedData === 'function') {
            // 如果提取器已就绪，直接处理
            console.log('[JD-HELPER] Extractor ready, processing unified data immediately.');
            window.unifiedElementExtractor.processUnifiedData(event.detail);
        } else {
            // 如果提取器未就绪，将数据推入队列
            console.log('[JD-HELPER] unifiedElementExtractor is not ready yet. Queuing unified data. Current ready status:', {
                isExtractorReady,
                hasExtractor: !!window.unifiedElementExtractor,
                hasMethod: !!(window.unifiedElementExtractor && typeof window.unifiedElementExtractor.processUnifiedData === 'function')
            });
            apiDataQueue.push(event.detail);
        }
    });
})();

// 京东购物助手，可以快捷操作一些功能，提升购物体验。
// v.001 适配了京东商品页新版页面，常用功能都可以使用。

// 声明全局变量
let convertedLink = null;
let userInputQuantity = 1; // 用户输入数量缓存

// 构建分析和复制区域的整体HTML
function buildContentBox(skuId, pcUrl, mobileUrl) {
    return `
    <div class="ltby-content-box">
        <div class="ltby-link-group">
            <div class="ltby-link-group-title">市场分析</div>
            <ul>
                <li data-id="market-price">市价:¥10000</li>
                <li data-id="estimated-profit">毛利:¥-5000</li>
                <li style="display: flex; align-items: center; gap: 4px;">
                    <button id="update-market-price-btn" type="button">更新:</button>
                    <input type="text" id="new-market-price" placeholder="输入新价格" />
                 </li>
                <li style="display: flex; align-items: center; height: 26px; padding: 2px 0;">
                    <span id="update-time" style="margin-right: 8px;">无数据</span>
                    <button id="focus-product-btn" type="button" class="focus-btn">关注</button>
                 </li>
            </ul>
        </div>
        <div class="ltby-link-group no-bg">
            <div class="ltby-link-group-title">快速复制</div>
            <ul class="ltby-copy-list no-bg">
                <li class="copy-item"><span class="ltby-copy-link" data-copy="${skuId}">编号:${skuId}</span></li>
                <li class="copy-item"><span class="ltby-copy-link" data-copy="${pcUrl}">商品链接</span></li>
                <li class="copy-item"><span class="ltby-copy-link" data-copy="${mobileUrl}">触屏链接</span></li>
                <li class="copy-item"><span id="convert-link" class="ltby-copy-link">复制转链</span></li>
                <li class="copy-item"><span id="formatted-copy" class="ltby-copy-link">格式文案</span></li>
                <li class="copy-item"><span id="cloud-upload" class="ltby-copy-link">发送</span></li>
            </ul>
        </div>
    </div>
    `;
}

// 显示复制提示
function showCopyTip(element, text = '复制成功') {
    const tip = document.createElement('div');
    tip.className = 'ltby-copy-tip';
    tip.textContent = text;

    const rect = element.getBoundingClientRect();
    tip.style.position = 'fixed';
    tip.style.left = rect.left + 'px';
    tip.style.top = (rect.top - 30) + 'px';

    document.body.appendChild(tip);
    setTimeout(() => tip.remove(), 1000);
}

// 初始化数量监听器
function initQuantityListener() {
    document.addEventListener('keydown', (event) => {
        const target = event.target;
        const isQuantityInput = target.matches('#buy-num') ||
            target.matches('#choose-amount input') ||
            target.matches('.quantity-form input') ||
            target.matches('#select-amount input') ||
            target.matches('input[name="quantity"]') ||
            target.matches('#quantity');

        if (isQuantityInput && /^\d$/.test(event.key)) {
            setTimeout(() => {
                const value = parseInt(target.value);
                if (value && value > 0) {
                    userInputQuantity = value;
                    console.log(`获取商品数量: 用户输入数量更新为: ${userInputQuantity}`);
                }
            }, 10);
        }

        if (isQuantityInput && (event.key === 'Backspace' || event.key === 'Delete')) {
            setTimeout(() => {
                const value = parseInt(target.value);
                userInputQuantity = value && value > 0 ? value : 1;
                console.log(`获取商品数量: 用户删除后数量更新为: ${userInputQuantity}`);
            }, 10);
        }
    });

    document.addEventListener('input', (event) => {
        const target = event.target;
        const isQuantityInput = target.matches('#buy-num') ||
            target.matches('#choose-amount input') ||
            target.matches('.quantity-form input') ||
            target.matches('#select-amount input') ||
            target.matches('input[name="quantity"]') ||
            target.matches('#quantity');

        if (isQuantityInput) {
            const value = parseInt(target.value);
            userInputQuantity = value && value > 0 ? value : 1;
            console.log(`获取商品数量: 输入事件数量更新为: ${userInputQuantity}`);
        }
    });
}

// 获取商品数量函数
function getProductQuantity() {
    console.log(`获取商品数量: 使用缓存的用户输入数量: ${userInputQuantity}`);
    return userInputQuantity;
}

// 获取商品相关信息 - 使用统一元素提取器
async function extractAndProcessProduct() {
    // 定义默认配置
    const defaultConfig = {
        autoCopyMobileLink: true, // 默认启用自动复制触屏链接
    };
    let config = defaultConfig;

    try {
        // 尝试从 chrome.storage.sync 加载用户配置
        const items = await new Promise((resolve) => {
            if (chrome.storage && chrome.storage.sync) {
                chrome.storage.sync.get('ltbySettings', resolve);
            } else {
                console.warn('[Content] chrome.storage.sync API 不可用，将使用默认配置。');
                resolve({});
            }
        });

        if (items && items.ltbySettings) {
            config = { ...defaultConfig, ...items.ltbySettings };
            console.log('[Content] 用户配置已加载:', config);
        } else {
            console.log('[Content] 未找到用户配置，使用默认配置:', config);
        }
    } catch (error) {
        console.error('[Content] 加载配置失败，将使用默认配置:', error);
        config = defaultConfig;
    }

    try {
        console.log('[Content] 开始提取商品信息...');

        // 等待统一元素提取器加载
        if (!window.unifiedExtractor) {
            console.log('[Content] 等待统一元素提取器初始化...');
            await new Promise(resolve => {
                const checkInterval = setInterval(() => {
                    if (window.unifiedExtractor) {
                        clearInterval(checkInterval);
                        resolve();
                    }
                }, 100);
            });
        }

        // 等待基础信息提取完成（优先级最高）
        let productInfo = null;
        if (window.unifiedExtractor.isBasicInfoReady) {
            productInfo = window.unifiedExtractor.getProductInfo();
        } else {
            console.log('[Content] 等待基础信息提取完成...');
            await new Promise(resolve => {
                // 检查兼容性方法是否存在
                if (window.unifiedExtractor && typeof window.unifiedExtractor.onBasicInfoReady === 'function') {
                    window.unifiedExtractor.onBasicInfoReady(() => {
                        productInfo = window.unifiedExtractor.getProductInfo();
                        resolve();
                    });
                } else {
                    // 如果兼容性方法不存在，直接监听事件
                    document.addEventListener('basicInfoReady', () => {
                        productInfo = window.unifiedExtractor.getProductInfo();
                        resolve();
                    }, { once: true });

                    // 如果基础信息已经准备就绪，立即解析
                    setTimeout(() => {
                        if (window.unifiedExtractor && window.unifiedExtractor.getProductInfo) {
                            const currentInfo = window.unifiedExtractor.getProductInfo();
                            if (currentInfo && currentInfo.basic && currentInfo.basic.skuId) {
                                productInfo = currentInfo;
                                resolve();
                            }
                        }
                    }, 100);
                }
            });
        }

        console.log('[Content] 提取的商品信息:', productInfo);

        // ==================== 阶段1：同步执行，并行处理 ====================
        // 立即插入主插件UI（优先级最高）
        if (productInfo && productInfo.basic && productInfo.basic.skuId) {
            console.log('[Content] 开始插入主插件UI...');
            await insertMainPluginUI(productInfo);

            // 自动复制触屏链接
            if (config.autoCopyMobileLink && productInfo.basic.mobileUrl) {
                try {
                    await navigator.clipboard.writeText(productInfo.basic.mobileUrl);
                    console.log('[Content] 已自动复制触屏链接到剪贴板:', productInfo.basic.mobileUrl);

                    // 尝试为复制操作显示提示
                    const mobileLinkElement = document.querySelector(`.ltby-copy-link[data-copy="${productInfo.basic.mobileUrl}"]`);
                    if (mobileLinkElement) {
                        showCopyTip(mobileLinkElement, '已自动复制触屏链接');
                    } else {
                        // 如果特定链接元素未找到，尝试在插件主容器上显示提示
                        const mainPluginContainer = document.getElementById('ltby-main-container');
                        if (mainPluginContainer) {
                            showCopyTip(mainPluginContainer, '已自动复制触屏链接');
                        }
                        console.warn('[Content] 未找到触屏链接UI元素进行提示定位，但链接已复制。');
                    }
                } catch (err) {
                    console.error('[Content] 自动复制触屏链接失败:', err);
                    // 如果是权限问题，可以提示用户
                    if (err.name === 'NotAllowedError' || (err.message && err.message.toLowerCase().includes('clipboard write failed'))) {
                        const mainPluginContainer = document.getElementById('ltby-main-container');
                        if (mainPluginContainer) {
                            showCopyTip(mainPluginContainer, '自动复制失败:请授予剪贴板权限');
                        }
                    }
                }
            } else if (config.autoCopyMobileLink) {
                console.log('[Content] 未找到触屏链接，无法自动复制');
            }

            // 初始化数量监听器
            initQuantityListener();
            console.log('[Content] 数量监听器已初始化');

            // 初始化重点关注模块
            if (window.FocusProductManager && productInfo.basic.skuId) {
                window.FocusProductManager.init(productInfo.basic.skuId);
                const focusBtn = document.getElementById('focus-product-btn');
                if (focusBtn) {
                    window.FocusProductManager.updateButtonUI(focusBtn);
                }
                console.log('[Content] 重点关注模块已初始化');
            }

            // 立即初始化格式化文案模块
            if (window.FormattedCopyManager) {
                window.FormattedCopyManager.init();
                console.log('[Content] 格式化文案模块已初始化');
            }

            // 处理转链
            console.log('[Content] 开始处理转链, URL:', productInfo.basic.pcUrl);
            await initializeLinkConverter(productInfo.basic);
        }

        // ==================== 阶段2：异步完成后同步处理 ====================
        // 数据库交互等待统一元素提取完成
        if (productInfo && productInfo.basic && productInfo.basic.skuId) {
            console.log('[Content] 商品信息提取完成，数据库交互等待统一元素提取完成...');

            // 设置数据库响应监听
            document.addEventListener('databaseFullUpdateComplete', (event) => {
                console.log('[Content] 数据库更新完成，通知各模块处理:', event.detail);

                // 通知各模块数据库数据已准备好
                window.dispatchEvent(new CustomEvent('ltby-database-ready', {
                    detail: { productInfo, dbResult: event.detail }
                }));
            });

            // 等待数据库管理器初始化
            if (!window.databaseManager) {
                console.log('[Content] 等待数据库管理器初始化...');
                await new Promise(resolve => {
                    const checkInterval = setInterval(() => {
                        if (window.databaseManager) {
                            clearInterval(checkInterval);
                            resolve();
                        }
                    }, 100);
                });
            }

            try {
                // 数据库交互会等待统一元素提取完成
                console.log('[Content] 开始数据库交互（等待统一元素提取完成）...');
                const databaseResult = await window.databaseManager.checkProductPage();
                console.log('[Content] 数据库交互完成:', databaseResult);

                // 记录用户访问日志
                if (databaseResult.success && databaseResult.skuId) {
                    console.log('[Content] 用户访问商品页面:', {
                        sku: databaseResult.skuId,
                        title: productInfo.basic.title,
                        timestamp: new Date().toISOString()
                    });
                }

            } catch (error) {
                console.error('[Content] 数据库交互失败:', error);
            }

        } else {
            console.warn('[Content] 商品信息提取失败或不完整');
        }

    } catch (error) {
        console.error('[Content] 商品信息处理过程中发生错误:', error);
    }
}

// 初始化转链功能
async function initializeLinkConverter(basicInfo) {
    try {
        console.log('[Content] 🔗 初始化转链功能...');

        if (window.LinkConverter) {
            try {
                await window.LinkConverter.init(basicInfo.pcUrl);
                const convertedLink = window.LinkConverter.getCurrentLink();
                console.log('[Content] 转链完成，结果:', convertedLink);
                window.convertedLink = convertedLink;

                // 更新转链按钮
                const convertBtn = document.getElementById('convert-link');
                if (convertBtn) {
                    convertBtn.setAttribute('data-copy', convertedLink);
                }
            } catch (error) {
                console.error('[Content] 转链处理失败:', error);
                window.convertedLink = basicInfo.pcUrl;
            }
        } else {
            console.log('[Content] LinkConverter模块未加载，使用原链接');
            window.convertedLink = basicInfo.pcUrl;
        }

        console.log('[Content] ✅ 转链功能初始化完成');
    } catch (error) {
        console.error('[Content] 💥 转链功能初始化失败:', error);
        throw error;
    }
}

// 插入主插件UI
async function insertMainPluginUI(productInfo) {
    try {
        console.log('[Content] 开始插入主插件UI...');

        // 检查是否已经插入过
        if (document.getElementById('ltby-main-container')) {
            console.log('[Content] 主插件UI已存在，跳过插入');
            return;
        }

        const skuId = productInfo.basic.skuId;
        const pcUrl = productInfo.basic.pcUrl;
        const mobileUrl = productInfo.basic.mobileUrl;

        // 创建主插件容器
        const mainContainer = document.createElement('div');
        mainContainer.id = 'ltby-main-container';
        mainContainer.innerHTML = buildContentBox(skuId, pcUrl, mobileUrl);

        // 查找插入位置
        const insertTarget = findInsertTarget();
        if (insertTarget.element) {
            console.log('[Content] 找到插入位置:', insertTarget.description);

            if (insertTarget.position === 'before') {
                insertTarget.element.parentNode.insertBefore(mainContainer, insertTarget.element);
            } else if (insertTarget.position === 'after') {
                if (insertTarget.element.nextSibling) {
                    insertTarget.element.parentNode.insertBefore(mainContainer, insertTarget.element.nextSibling);
                } else {
                    insertTarget.element.parentNode.appendChild(mainContainer);
                }
            } else {
                insertTarget.element.appendChild(mainContainer);
            }

            console.log('[Content] 主插件UI插入成功');

            // 绑定事件监听器
            bindMainPluginEvents(mainContainer);

        } else {
            console.error('[Content] 未找到合适的插入位置');
        }

    } catch (error) {
        console.error('[Content] 插入主插件UI失败:', error);
    }
}

// 查找插入位置
function findInsertTarget() {
    // 优先检测商品下柜提示
    const delistedTip = document.querySelector('.itemover-tip');
    if (delistedTip && delistedTip.textContent.includes('该商品已下柜')) {
        console.log('[Content] 检测到商品已下柜，插入到下柜提示下方');
        return {
            element: delistedTip,
            position: 'after',
            description: '商品下柜提示下方'
        };
    }

    // 查找库存信息区域
    const stockElement = document.querySelector('.summary-stock[clstag="shangpin|keycount|product|quyuxuanze_1"]');
    if (stockElement) {
        return {
            element: stockElement,
            position: 'before',
            description: '库存信息区域前'
        };
    }

    // 查找供应信息区域
    const supplyElement = document.querySelector('#summary-supply');
    if (supplyElement) {
        return {
            element: supplyElement,
            position: 'before',
            description: '供应信息区域前'
        };
    }

    // 查找商品介绍区域
    const introElement = document.querySelector('.product-intro');
    if (introElement) {
        return {
            element: introElement,
            position: 'append',
            description: '商品介绍区域内'
        };
    }

    return { element: null, position: null, description: '未找到插入位置' };
}

// 绑定主插件事件
function bindMainPluginEvents(container) {
    console.log('[Content] 绑定主插件事件监听器...');

    // 复制功能事件监听
    container.addEventListener('click', async function (e) {
        if (e.target.classList.contains('ltby-copy-link')) {
            const text = e.target.getAttribute('data-copy');
            if (text) {
                await navigator.clipboard.writeText(text);
                showCopyTip(e.target);
                console.log('[Content] 复制内容:', text);
            }
        }

        // 处理重点关注按钮点击 - 由简化版模块处理
        if (e.target.id === 'focus-product-btn') {
            console.log('关注按钮点击事件被简化版模块处理');
            return;
        }

        // 处理格式化文案复制
        if (e.target.id === 'formatted-copy' && window.FormattedCopyManager) {
            const result = await window.FormattedCopyManager.copyFormattedContent();
            if (result.success) {
                showCopyTip(e.target, result.message);
                console.log('复制的格式化文案:', result.content);
            } else {
                showCopyTip(e.target, result.message);
            }
        }

        // 处理直接发送功能到wx自动化程序
        if (e.target.id === 'cloud-upload') {
            try {
                showCopyTip(e.target, '正在发送...');

                // 获取格式化文案
                if (!window.FormattedCopyManager) {
                    showCopyTip(e.target, '格式化文案模块未加载');
                    return;
                }

                // 生成格式化文案（文本格式，不是JSON）
                const formattedText = window.FormattedCopyManager.generateFormattedContent();
                if (!formattedText) {
                    showCopyTip(e.target, '获取格式化文案失败');
                    return;
                }

                // 获取主图URL
                let mainImageUrl = '';
                if (window.unifiedExtractor) {
                    mainImageUrl = window.unifiedExtractor.getImageUrl();
                    console.log('提取到的主图URL:', mainImageUrl);
                }

                console.log('准备发送的格式化文案:', formattedText);
                console.log('准备发送的主图URL:', mainImageUrl);

                // 使用中间件版WebSocket发送
                if (window.middlewareWebSocketSender) {
                    try {
                        const result = await window.middlewareWebSocketSender.sendFormattedContent(formattedText, mainImageUrl || '');
                        if (result && result.success) {
                            const tipMessage = mainImageUrl ? '发送成功（含主图）！' : '发送成功！';
                            showCopyTip(e.target, tipMessage);
                            console.log('中间件WebSocket发送成功');
                            return;
                        }
                    } catch (error) {
                        console.error('中间件WebSocket发送失败:', error);
                        showCopyTip(e.target, '发送失败：' + error.message);
                    }
                } else {
                    console.error('中间件WebSocket发送模块未加载');
                    showCopyTip(e.target, '发送模块未加载');
                }

            } catch (error) {
                console.error('发送失败:', error);
                showCopyTip(e.target, '发送失败: ' + error.message);
            }
        }
    });

    console.log('[Content] 主插件事件监听器绑定完成');
}

// 页面加载完成后执行
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        console.log('[Content] DOM加载完成，开始初始化...');
        setTimeout(extractAndProcessProduct, 1000);
    });
} else {
    console.log('[Content] DOM已加载，立即初始化...');
    setTimeout(extractAndProcessProduct, 1000);
}

// 监听页面变化（适用于SPA应用）
let lastUrl = location.href;
const checkUrlChange = () => {
    if (location.href !== lastUrl) {
        lastUrl = location.href;
        console.log('[Content] 页面URL变化，重新处理商品信息');
        setTimeout(extractAndProcessProduct, 500);
    }
};

// 定期检查URL变化
setInterval(checkUrlChange, 2000);

// 监听来自popup的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    try {
        console.log('[Content] 收到消息:', request);

        switch (request.action) {
            default:
                console.warn('[Content] 未知操作:', request.action);
                sendResponse({ error: '未知操作' });
        }

    } catch (error) {
        console.error('[Content] 处理消息时出错:', error);
        sendResponse({ success: false, error: error.message });
    }

    return true; // 保持消息通道开放
});

console.log('[Content] 内容脚本加载完成');
