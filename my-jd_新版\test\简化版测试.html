<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化版悬浮窗测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .mock-jd-page {
            background: #fff;
            border: 2px solid #e3e3e3;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .mock-price {
            font-size: 24px;
            color: #e4393c;
            font-weight: bold;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>简化版悬浮窗测试</h1>
        
        <div class="test-section">
            <h3>1. 模拟京东商品页面</h3>
            <div class="mock-jd-page">
                <h4>测试商品</h4>
                <div class="p-price">
                    <span class="price">23.50</span>
                </div>
                <p>这是一个模拟的京东商品页面，用于测试价格提取功能。</p>
            </div>
            <button onclick="changePrice()">更改价格</button>
            <button onclick="addPromotions()">添加促销信息</button>
        </div>

        <div class="test-section">
            <h3>2. 模块测试</h3>
            <button onclick="testCalculatorModule()">测试计算器模块</button>
            <button onclick="testSimplePanel()">测试简化面板</button>
            <button onclick="showFloatingPanel()">显示悬浮窗</button>
            <div id="module-status" class="status info">等待测试...</div>
        </div>

        <div class="test-section">
            <h3>3. 功能测试</h3>
            <button onclick="testPriceDetection()">测试价格检测</button>
            <button onclick="testCalculation()">测试优惠计算</button>
            <button onclick="testDragFunction()">测试拖拽功能</button>
            <div id="function-status" class="status info">等待测试...</div>
        </div>

        <div class="test-section">
            <h3>4. 测试日志</h3>
            <button onclick="clearLog()">清空日志</button>
            <div id="test-log" class="log-area">测试日志将显示在这里...\n</div>
        </div>
    </div>

    <!-- 模拟chrome.runtime -->
    <script>
        // 模拟chrome扩展API
        if (typeof chrome === 'undefined') {
            window.chrome = {
                runtime: {
                    getURL: function(path) {
                        return '../' + path;
                    }
                }
            };
        }
    </script>

    <!-- 引入必要的模块 -->
    <script src="../js/优惠算法模块.js"></script>
    <script src="../js/优惠分析悬浮窗_简化版.js"></script>

    <script>
        // 测试日志函数
        function log(message, type = 'info') {
            const logArea = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logArea.textContent += logEntry;
            logArea.scrollTop = logArea.scrollHeight;
            console.log(`[测试] ${message}`);
        }

        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }

        function clearLog() {
            document.getElementById('test-log').textContent = '日志已清空...\n';
        }

        // 1. 模拟京东页面功能
        function changePrice() {
            const prices = ['23.50', '45.80', '67.90', '12.30', '89.99'];
            const randomPrice = prices[Math.floor(Math.random() * prices.length)];
            
            const priceElement = document.querySelector('.price');
            if (priceElement) {
                priceElement.textContent = randomPrice;
                log(`价格已更改为: ¥${randomPrice}`);
            }
        }

        function addPromotions() {
            log('添加促销信息功能开发中...');
        }

        // 2. 模块测试
        function testCalculatorModule() {
            log('开始测试计算器模块...');
            
            if (typeof JDPromotionCalculator !== 'undefined') {
                log('✅ JDPromotionCalculator 类已加载', 'success');
                
                try {
                    const calculator = new JDPromotionCalculator();
                    log('✅ 计算器实例创建成功');
                    
                    // 测试基本计算
                    const testData = {
                        price: { p: 23.5 },
                        preferenceInfo: {
                            coupons: [],
                            promotions: []
                        }
                    };
                    
                    const result = calculator.calculateOptimalDiscount(testData, 1);
                    if (result && typeof result.finalUnitPrice === 'number') {
                        log(`✅ 基本计算成功: ¥${result.finalUnitPrice.toFixed(2)}`);
                        updateStatus('module-status', '计算器模块正常', 'success');
                    } else {
                        log('❌ 计算结果异常', 'error');
                        updateStatus('module-status', '计算结果异常', 'error');
                    }
                } catch (error) {
                    log(`❌ 计算器测试失败: ${error.message}`, 'error');
                    updateStatus('module-status', '计算器测试失败', 'error');
                }
            } else {
                log('❌ JDPromotionCalculator 类未找到', 'error');
                updateStatus('module-status', '模块未加载', 'error');
            }
        }

        function testSimplePanel() {
            log('开始测试简化面板...');
            
            if (window.simpleJdPromotionPanel) {
                log('✅ 简化面板实例已存在');
                updateStatus('module-status', '简化面板已就绪', 'success');
            } else {
                log('❌ 简化面板实例未找到', 'error');
                updateStatus('module-status', '简化面板未就绪', 'error');
            }
        }

        function showFloatingPanel() {
            log('尝试显示悬浮窗...');
            
            const container = document.getElementById('purchase-analysis-root-container');
            if (container) {
                container.style.display = 'block';
                log('✅ 悬浮窗已显示');
                updateStatus('module-status', '悬浮窗已显示', 'success');
            } else {
                log('❌ 悬浮窗容器未找到', 'error');
                updateStatus('module-status', '悬浮窗未找到', 'error');
            }
        }

        // 3. 功能测试
        function testPriceDetection() {
            log('开始测试价格检测...');
            
            const priceElement = document.querySelector('.price');
            if (priceElement) {
                const price = parseFloat(priceElement.textContent);
                log(`✅ 检测到价格: ¥${price}`);
                
                // 触发简化面板的价格检测
                if (window.simpleJdPromotionPanel) {
                    window.simpleJdPromotionPanel.detectProductData();
                    log('✅ 触发了面板价格检测');
                    updateStatus('function-status', '价格检测正常', 'success');
                } else {
                    log('⚠️ 简化面板未就绪');
                    updateStatus('function-status', '面板未就绪', 'info');
                }
            } else {
                log('❌ 未找到价格元素', 'error');
                updateStatus('function-status', '价格元素缺失', 'error');
            }
        }

        function testCalculation() {
            log('开始测试优惠计算...');
            
            if (window.simpleJdPromotionPanel && window.simpleJdPromotionPanel.calculator) {
                const price = 23.5;
                window.simpleJdPromotionPanel.calculateOptimal(price);
                log('✅ 触发了优惠计算');
                updateStatus('function-status', '计算功能正常', 'success');
            } else {
                log('❌ 计算器未就绪', 'error');
                updateStatus('function-status', '计算器未就绪', 'error');
            }
        }

        function testDragFunction() {
            log('开始测试拖拽功能...');
            
            const header = document.querySelector('.dashboard-header');
            if (header) {
                // 模拟拖拽事件
                const mouseDownEvent = new MouseEvent('mousedown', {
                    clientX: 100,
                    clientY: 100,
                    bubbles: true
                });
                
                header.dispatchEvent(mouseDownEvent);
                log('✅ 拖拽事件已触发');
                updateStatus('function-status', '拖拽功能已测试', 'success');
            } else {
                log('❌ 拖拽元素未找到', 'error');
                updateStatus('function-status', '拖拽元素缺失', 'error');
            }
        }

        // 页面加载完成后自动运行基础测试
        window.addEventListener('load', () => {
            log('页面加载完成，开始自动测试...');
            setTimeout(() => {
                testCalculatorModule();
                setTimeout(() => {
                    testSimplePanel();
                }, 1000);
            }, 500);
        });
    </script>
</body>
</html>
