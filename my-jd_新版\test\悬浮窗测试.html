<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>京东优惠分析悬浮窗测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>京东优惠分析悬浮窗测试</h1>
        
        <div class="test-section">
            <h3>1. 模块加载测试</h3>
            <button onclick="testModuleLoading()">测试模块加载</button>
            <button onclick="testCalculatorInit()">测试计算器初始化</button>
            <div id="module-status" class="status info">等待测试...</div>
        </div>

        <div class="test-section">
            <h3>2. 悬浮窗显示测试</h3>
            <button onclick="testFloatingPanelDisplay()">显示悬浮窗</button>
            <button onclick="testPanelToggle()">测试展开/收起</button>
            <button onclick="testDragFunction()">测试拖拽功能</button>
            <div id="display-status" class="status info">等待测试...</div>
        </div>

        <div class="test-section">
            <h3>3. 数据计算测试</h3>
            <button onclick="testBasicCalculation()">基础计算测试</button>
            <button onclick="testComplexCalculation()">复杂优惠测试</button>
            <button onclick="testBatchCalculation()">批量计算测试</button>
            <div id="calculation-status" class="status info">等待测试...</div>
        </div>

        <div class="test-section">
            <h3>4. 测试日志</h3>
            <button onclick="clearLog()">清空日志</button>
            <div id="test-log" class="log-area">测试日志将显示在这里...\n</div>
        </div>
    </div>

    <!-- 引入必要的模块 -->
    <script src="../js/优惠算法模块.js"></script>
    <link rel="stylesheet" href="../styles/purchase-analysis.css">

    <script>
        // 测试日志函数
        function log(message, type = 'info') {
            const logArea = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logArea.textContent += logEntry;
            logArea.scrollTop = logArea.scrollHeight;
            console.log(`[测试] ${message}`);
        }

        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }

        function clearLog() {
            document.getElementById('test-log').textContent = '日志已清空...\n';
        }

        // 1. 模块加载测试
        function testModuleLoading() {
            log('开始测试模块加载...');
            
            if (typeof JDPromotionCalculator !== 'undefined') {
                log('✅ JDPromotionCalculator 类已加载', 'success');
                updateStatus('module-status', '模块加载成功', 'success');
            } else {
                log('❌ JDPromotionCalculator 类未找到', 'error');
                updateStatus('module-status', '模块加载失败', 'error');
                return false;
            }

            // 检查关键方法
            const calculator = new JDPromotionCalculator();
            const methods = ['calculateOptimalDiscount', 'parseCoupons', 'parsePromotions'];
            let allMethodsExist = true;

            methods.forEach(method => {
                if (typeof calculator[method] === 'function') {
                    log(`✅ 方法 ${method} 存在`);
                } else {
                    log(`❌ 方法 ${method} 不存在`, 'error');
                    allMethodsExist = false;
                }
            });

            if (allMethodsExist) {
                updateStatus('module-status', '所有关键方法都存在', 'success');
            } else {
                updateStatus('module-status', '部分方法缺失', 'error');
            }

            return allMethodsExist;
        }

        function testCalculatorInit() {
            log('开始测试计算器初始化...');
            
            try {
                const calculator = new JDPromotionCalculator();
                log('✅ 计算器实例创建成功');
                
                // 测试基本属性
                if (calculator.hasOwnProperty('coupons') && calculator.hasOwnProperty('promotions')) {
                    log('✅ 计算器基本属性存在');
                    updateStatus('module-status', '计算器初始化成功', 'success');
                } else {
                    log('❌ 计算器基本属性缺失', 'error');
                    updateStatus('module-status', '计算器初始化失败', 'error');
                }
            } catch (error) {
                log(`❌ 计算器初始化失败: ${error.message}`, 'error');
                updateStatus('module-status', '计算器初始化异常', 'error');
            }
        }

        // 2. 悬浮窗显示测试
        function testFloatingPanelDisplay() {
            log('开始测试悬浮窗显示...');
            
            // 创建悬浮窗HTML
            const floatingPanelHTML = `
                <div id="purchase-analysis-root-container">
                    <div id="dashboard-container">
                        <div class="dashboard-header">
                            <span>优惠分析</span>
                            <button id="toggle-dashboard">▼</button>
                        </div>
                        <div class="dashboard-content">
                            <div class="dashboard-item">
                                <span class="dashboard-label">最优单价</span>
                                <span class="dashboard-value" id="dashboard-best-price">¥23.50</span>
                            </div>
                            <div class="dashboard-item">
                                <span class="dashboard-label">最优购买数</span>
                                <span class="dashboard-value" id="dashboard-best-quantity">×1</span>
                            </div>
                            <div class="dashboard-item">
                                <span class="dashboard-label">最优总价</span>
                                <span class="dashboard-value" id="dashboard-total-price">¥23.50</span>
                            </div>
                            <div class="dashboard-item">
                                <span class="dashboard-label">节省金额</span>
                                <span class="dashboard-value" id="dashboard-savings">¥0.00</span>
                            </div>
                        </div>
                    </div>
                    <div id="purchase-analysis-container" class="hidden">
                        <div class="header">
                            <span>购买分析报告</span>
                            <div>
                                <button id="copy-text-btn">复制文案</button>
                                <button id="add-to-cart-btn">加车购买</button>
                            </div>
                            <button id="close-analysis-btn">×</button>
                        </div>
                        <div class="content">
                            <p>详细分析内容...</p>
                        </div>
                    </div>
                </div>
            `;

            // 移除现有的悬浮窗
            const existing = document.getElementById('purchase-analysis-root-container');
            if (existing) {
                existing.remove();
            }

            // 插入新的悬浮窗
            const container = document.createElement('div');
            container.innerHTML = floatingPanelHTML;
            document.body.appendChild(container.firstElementChild);

            log('✅ 悬浮窗HTML已插入');
            updateStatus('display-status', '悬浮窗显示成功', 'success');
        }

        function testPanelToggle() {
            log('开始测试面板展开/收起...');
            
            const dashboard = document.getElementById('dashboard-container');
            const detailPanel = document.getElementById('purchase-analysis-container');
            const toggleBtn = document.getElementById('toggle-dashboard');

            if (!dashboard || !detailPanel || !toggleBtn) {
                log('❌ 悬浮窗元素未找到，请先显示悬浮窗', 'error');
                updateStatus('display-status', '元素未找到', 'error');
                return;
            }

            // 测试展开
            dashboard.click();
            setTimeout(() => {
                if (!detailPanel.classList.contains('hidden')) {
                    log('✅ 面板展开成功');
                    
                    // 测试收起
                    dashboard.click();
                    setTimeout(() => {
                        if (detailPanel.classList.contains('hidden')) {
                            log('✅ 面板收起成功');
                            updateStatus('display-status', '展开/收起功能正常', 'success');
                        } else {
                            log('❌ 面板收起失败', 'error');
                            updateStatus('display-status', '收起功能异常', 'error');
                        }
                    }, 100);
                } else {
                    log('❌ 面板展开失败', 'error');
                    updateStatus('display-status', '展开功能异常', 'error');
                }
            }, 100);
        }

        function testDragFunction() {
            log('开始测试拖拽功能...');
            
            const container = document.getElementById('purchase-analysis-root-container');
            const header = document.querySelector('.dashboard-header');

            if (!container || !header) {
                log('❌ 拖拽元素未找到', 'error');
                updateStatus('display-status', '拖拽元素缺失', 'error');
                return;
            }

            // 检查CSS样式
            const headerStyle = window.getComputedStyle(header);
            if (headerStyle.cursor === 'grab' || headerStyle.cursor === 'pointer') {
                log('✅ 拖拽样式设置正确');
                updateStatus('display-status', '拖拽功能准备就绪', 'success');
            } else {
                log('⚠️ 拖拽样式可能未正确设置');
                updateStatus('display-status', '拖拽样式需要检查', 'info');
            }

            // 模拟拖拽事件
            const mouseDownEvent = new MouseEvent('mousedown', {
                clientX: 100,
                clientY: 100,
                bubbles: true
            });
            
            const mouseMoveEvent = new MouseEvent('mousemove', {
                clientX: 150,
                clientY: 150,
                bubbles: true
            });

            const mouseUpEvent = new MouseEvent('mouseup', {
                bubbles: true
            });

            header.dispatchEvent(mouseDownEvent);
            document.dispatchEvent(mouseMoveEvent);
            document.dispatchEvent(mouseUpEvent);

            log('✅ 拖拽事件模拟完成');
        }

        // 3. 数据计算测试
        function testBasicCalculation() {
            log('开始基础计算测试...');
            
            if (!testModuleLoading()) {
                return;
            }

            try {
                const calculator = new JDPromotionCalculator();
                
                // 测试数据
                const testData = {
                    price: { p: 23.5 },
                    preferenceInfo: {
                        coupons: [],
                        promotions: []
                    }
                };

                const result = calculator.calculateOptimalDiscount(testData, 1);
                
                if (result && typeof result.finalUnitPrice === 'number') {
                    log(`✅ 基础计算成功: 单价 ¥${result.finalUnitPrice.toFixed(2)}`);
                    updateStatus('calculation-status', '基础计算正常', 'success');
                } else {
                    log('❌ 基础计算返回结果异常', 'error');
                    updateStatus('calculation-status', '基础计算异常', 'error');
                }
            } catch (error) {
                log(`❌ 基础计算失败: ${error.message}`, 'error');
                updateStatus('calculation-status', '基础计算失败', 'error');
            }
        }

        function testComplexCalculation() {
            log('开始复杂优惠测试...');
            
            try {
                const calculator = new JDPromotionCalculator();
                
                // 复杂测试数据（包含优惠券和促销）
                const complexData = {
                    price: { p: 23.5 },
                    preferenceInfo: {
                        coupons: [
                            {
                                batchId: "1187806066",
                                desc: "满19减5",
                                discount: "5",
                                quota: "19",
                                businessLabel: "204"
                            }
                        ],
                        promotions: [
                            {
                                text: "PLUS专享立减",
                                shortText: "PLUS专享立减0.23元",
                                promoTags: [1128]
                            }
                        ]
                    }
                };

                const result = calculator.calculateOptimalDiscount(complexData, 1);
                
                if (result && result.finalUnitPrice < complexData.price.p) {
                    log(`✅ 复杂计算成功: 原价 ¥${complexData.price.p}, 优惠后 ¥${result.finalUnitPrice.toFixed(2)}`);
                    log(`   应用优惠券: ${result.appliedCoupons?.length || 0} 个`);
                    log(`   应用促销: ${result.appliedPromotions?.length || 0} 个`);
                    updateStatus('calculation-status', '复杂优惠计算正常', 'success');
                } else {
                    log('❌ 复杂计算未产生预期优惠', 'error');
                    updateStatus('calculation-status', '复杂计算异常', 'error');
                }
            } catch (error) {
                log(`❌ 复杂计算失败: ${error.message}`, 'error');
                updateStatus('calculation-status', '复杂计算失败', 'error');
            }
        }

        function testBatchCalculation() {
            log('开始批量计算测试...');
            
            try {
                const calculator = new JDPromotionCalculator();
                
                const testData = {
                    price: { p: 23.5 },
                    preferenceInfo: {
                        coupons: [
                            {
                                desc: "满19减5",
                                discount: "5",
                                quota: "19"
                            }
                        ],
                        promotions: []
                    }
                };

                // 测试1-10件的计算
                let allSuccess = true;
                for (let quantity = 1; quantity <= 10; quantity++) {
                    const result = calculator.calculateOptimalDiscount(testData, quantity);
                    if (!result || typeof result.finalUnitPrice !== 'number') {
                        log(`❌ 数量 ${quantity} 计算失败`, 'error');
                        allSuccess = false;
                        break;
                    }
                }

                if (allSuccess) {
                    log('✅ 批量计算（1-10件）全部成功');
                    updateStatus('calculation-status', '批量计算正常', 'success');
                } else {
                    updateStatus('calculation-status', '批量计算部分失败', 'error');
                }
            } catch (error) {
                log(`❌ 批量计算失败: ${error.message}`, 'error');
                updateStatus('calculation-status', '批量计算失败', 'error');
            }
        }

        // 页面加载完成后自动运行基础测试
        window.addEventListener('load', () => {
            log('页面加载完成，开始自动测试...');
            setTimeout(() => {
                testModuleLoading();
            }, 500);
        });
    </script>
</body>
</html>
