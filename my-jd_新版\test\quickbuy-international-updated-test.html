<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>国际站一键购买插入测试 - 更新版（#qingguan上方插入）</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .product-details {
            background-color: #f9f9f9;
            padding: 15px;
            margin: 10px 0;
        }
        #qingguan {
            background-color: #ffe6e6;
            padding: 10px;
            margin: 10px 0;
            border: 2px solid #ff9999;
            border-radius: 4px;
        }
        .original-buttons {
            background-color: #e6f3ff;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #99ccff;
            border-radius: 4px;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .log-section {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
        }
        #log {
            background-color: #343a40;
            color: #fff;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>国际站一键购买插入测试 - 更新版（#qingguan上方插入）</h1>
        <p><strong>当前 URL:</strong> <span id="current-url"></span></p>
        <p><strong>当前域名:</strong> <span id="current-domain"></span></p>
        
        <div class="test-section">
            <h3>测试场景说明</h3>
            <p>本页面模拟京东国际站（jd.hk）商品页面结构，测试一键购买按钮是否正确插入到 <code>#qingguan</code> 的上方且与其平级。</p>
            <p><strong>期望结果：</strong>一键购买按钮应该插入到红色边框的"清关信息"区域上方，与其处于同一层级。</p>
        </div>

        <div class="test-section">
            <h3>模拟商品页面结构</h3>
            
            <div class="product-details">
                <h4>商品详情区域</h4>
                <p>商品名称: 测试商品 - 国际站</p>
                <p>商品价格: HK$ 299.00</p>
            </div>

            <div class="original-buttons">
                <h4>原有按钮区域</h4>
                <button class="test-button">立即购买</button>
                <button class="test-button">加入购物车</button>
            </div>

            <!-- 重要：这是国际站的清关信息区域，一键购买按钮应该插入到它的上方 -->
            <div id="qingguan">
                <h4>清关信息 (#qingguan)</h4>
                <p>这是国际站特有的清关信息区域，一键购买按钮应该插入到此区域的上方。</p>
                <p>插入位置：与此元素平级，在此元素之前。</p>
            </div>

            <div class="product-details">
                <h4>其他商品信息</h4>
                <p>商品详情、评价等其他内容...</p>
            </div>
        </div>

        <div class="test-section">
            <h3>测试控制</h3>
            <button class="test-button" onclick="testInsertion()">测试插入逻辑</button>
            <button class="test-button" onclick="testValidation()">测试位置验证</button>
            <button class="test-button" onclick="removeButton()">移除按钮</button>
            <button class="test-button" onclick="clearLog()">清空日志</button>
        </div>

        <div class="status" id="status"></div>

        <div class="log-section">
            <h3>测试日志</h3>
            <div id="log"></div>
        </div>
    </div>

    <script>
        // 模拟扩展环境
        window.chrome = {
            runtime: {
                getURL: (path) => `chrome-extension://test/${path}`
            }
        };

        // 初始化页面信息显示
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('current-domain').textContent = window.location.hostname;

        // 日志功能
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logDiv.textContent += logEntry;
            logDiv.scrollTop = logDiv.scrollHeight;

            console.log(`LTBY测试: ${message}`);
        }

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
            updateStatus('日志已清空', 'info');
        }

        // 移除按钮
        function removeButton() {
            const existingButton = document.querySelector('#ltby-quick-buy-root');
            if (existingButton) {
                existingButton.remove();
                log('一键购买按钮已移除');
                updateStatus('按钮已移除', 'info');
            } else {
                log('未找到需要移除的按钮');
                updateStatus('未找到按钮', 'error');
            }
        }

        // 测试插入逻辑
        function testInsertion() {
            log('开始测试插入逻辑...');
            
            // 检查是否已存在按钮
            const existingButton = document.querySelector('#ltby-quick-buy-root');
            if (existingButton) {
                log('发现已存在的按钮，先移除');
                existingButton.remove();
            }

            // 模拟京东国际站域名
            const originalHostname = window.location.hostname;
            Object.defineProperty(window.location, 'hostname', {
                writable: true,
                value: 'npcitem.jd.hk'
            });

            log(`模拟域名设置为: ${window.location.hostname}`);

            // 执行插入逻辑
            const currentHost = window.location.hostname;
            let insertTarget = null;
            let insertMethod = 'append';

            if (currentHost.includes('jd.hk')) {
                log('检测到国际站环境');
                const qingguanElement = document.querySelector('#qingguan');
                
                if (qingguanElement) {
                    insertTarget = qingguanElement;
                    insertMethod = 'insertBefore';
                    log('找到国际站插入点 #qingguan');
                } else {
                    log('未找到国际站插入点 #qingguan', 'error');
                    updateStatus('未找到插入点', 'error');
                    return;
                }
            }

            if (!insertTarget) {
                log('未找到适合的插入点', 'error');
                updateStatus('插入失败', 'error');
                return;
            }

            try {
                // 创建按钮容器
                const btnContainer = document.createElement('div');
                btnContainer.id = 'ltby-quick-buy-root';
                btnContainer.className = 'ltby-quick-buy-container';
                btnContainer.style.cssText = `
                    margin: 10px 0;
                    padding: 15px;
                    background-color: #e8f5e8;
                    border: 2px solid #28a745;
                    border-radius: 8px;
                    text-align: center;
                `;

                btnContainer.innerHTML = `
                    <div class="ltby-quick-buy-wrapper">
                        <div class="ltby-brand">
                            <span>🚀 LTBY助手</span>
                        </div>
                        <div class="ltby-buttons">
                            <button type="button" id="quick-buy-btn" style="margin: 5px; padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                一键购买
                            </button>
                            <button type="button" id="touch-screen-buy-btn" style="margin: 5px; padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                触屏购买
                            </button>
                            <button type="button" id="activity-buy-btn" style="margin: 5px; padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                活动价购买
                            </button>
                        </div>
                    </div>
                `;

                // 根据插入方法进行插入
                if (insertMethod === 'insertBefore') {
                    // 插入到指定元素前面（与其平级）
                    insertTarget.parentNode.insertBefore(btnContainer, insertTarget);
                    log(`按钮插入成功，使用方法: ${insertMethod}`);
                }

                // 验证插入位置
                const qingguanElement = document.querySelector('#qingguan');
                const insertedButton = document.querySelector('#ltby-quick-buy-root');
                
                if (insertedButton && qingguanElement) {
                    const siblings = Array.from(qingguanElement.parentNode.children);
                    const buttonIndex = siblings.indexOf(insertedButton);
                    const qingguanIndex = siblings.indexOf(qingguanElement);
                    
                    if (buttonIndex < qingguanIndex && insertedButton.parentNode === qingguanElement.parentNode) {
                        log('✅ 位置验证通过：按钮正确插入到 #qingguan 上方且平级');
                        updateStatus('插入成功，位置正确', 'success');
                    } else {
                        log('❌ 位置验证失败：按钮位置不正确');
                        updateStatus('插入成功，但位置可能有误', 'error');
                    }
                }

            } catch (error) {
                log(`按钮插入失败: ${error.message}`, 'error');
                updateStatus('插入失败', 'error');
            }

            // 恢复原始域名
            Object.defineProperty(window.location, 'hostname', {
                writable: true,
                value: originalHostname
            });
        }

        // 测试位置验证
        function testValidation() {
            log('开始测试位置验证逻辑...');
            
            const existingButton = document.querySelector('#ltby-quick-buy-root');
            if (!existingButton) {
                log('未找到按钮，无法测试验证逻辑', 'error');
                updateStatus('请先插入按钮', 'error');
                return;
            }

            // 模拟域名
            const originalHostname = window.location.hostname;
            Object.defineProperty(window.location, 'hostname', {
                writable: true,
                value: 'npcitem.jd.hk'
            });

            const currentHost = window.location.hostname;
            let isPositionCorrect = false;

            if (currentHost.includes('jd.hk')) {
                log('验证国际站位置逻辑');
                const qingguanElement = document.querySelector('#qingguan');
                if (qingguanElement && existingButton.parentNode === qingguanElement.parentNode) {
                    const siblings = Array.from(qingguanElement.parentNode.children);
                    const buttonIndex = siblings.indexOf(existingButton);
                    const qingguanIndex = siblings.indexOf(qingguanElement);
                    isPositionCorrect = buttonIndex < qingguanIndex;
                    
                    log(`按钮索引: ${buttonIndex}, #qingguan索引: ${qingguanIndex}`);
                    log(`父节点相同: ${existingButton.parentNode === qingguanElement.parentNode}`);
                }
            }

            if (isPositionCorrect) {
                log('✅ 位置验证通过');
                updateStatus('位置验证通过', 'success');
            } else {
                log('❌ 位置验证失败');
                updateStatus('位置验证失败', 'error');
            }

            // 恢复原始域名
            Object.defineProperty(window.location, 'hostname', {
                writable: true,
                value: originalHostname
            });
        }

        // 页面加载完成后的初始日志
        document.addEventListener('DOMContentLoaded', function() {
            log('测试页面加载完成');
            log('页面结构已准备就绪，可以开始测试');
            updateStatus('准备就绪，可以开始测试', 'info');
        });
    </script>
</body>
</html>
