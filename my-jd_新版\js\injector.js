// js/injector.js - 核心API拦截器注入脚本
// 专门负责在页面最早阶段注入API拦截器，确保100%拦截成功
// 运行在 document_start 阶段，比所有页面脚本都要早

(function () {
  'use strict';

  // 确保只运行一次
  if (window.hasRunInjector) {
    console.log('[JD-INJECTOR] Already injected, skipping...');
    return;
  }
  window.hasRunInjector = true;

  console.log('[JD-INJECTOR] 🚀 Starting API interceptor injection at earliest stage');

  try {
    // 创建script标签注入API拦截器
    const script = document.createElement('script');
    script.src = chrome.runtime.getURL('js/inject.js'); // 修正文件名

    // 选择最早可用的容器
    const container = document.head || document.documentElement || document.body;

    if (container) {
      container.appendChild(script);

      script.onload = () => {
        console.log('[JD-INJECTOR] ✅ API interceptor injected successfully');
        script.remove(); // 注入后移除，保持DOM整洁
      };

      script.onerror = (error) => {
        console.error('[JD-INJECTOR] ❌ Failed to inject API interceptor:', error);
        script.remove();
      };

      console.log('[JD-INJECTOR] 📦 API interceptor script added to:', container.tagName);
    } else {
      console.error('[JD-INJECTOR] ❌ No container available for injection');
    }

  } catch (error) {
    console.error('[JD-INJECTOR] 💥 Injection failed:', error);
  }
})();
