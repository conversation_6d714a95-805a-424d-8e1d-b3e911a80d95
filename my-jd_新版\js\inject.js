// js/inject.js - 核心API拦截脚本 (优化版)

// 立即执行函数（IIFE），避免污染全局作用域
(function () {
  'use strict';

  // 确保脚本只运行一次，防止被重复注入
  if (window.hasRunInjectScript) {
    return;
  }
  window.hasRunInjectScript = true;

  console.log('[JD-HELPER] 🚀 Inject script loaded at earliest stage - document.readyState:', document.readyState);

  // 全局变量
  let hasDetectedData = false; // 统一标记：是否已检测到任何数据（API或第三方）
  let detectInterval = null; // 存储定时器引用，用于清除

  // 统一数据处理函数 - 不区分数据来源，统一处理
  function processUnifiedData(rawData, source, dataType = 'product') {
    console.log(`[JD-HELPER] 🎯 Processing unified data from ${source}:`, rawData);

    // 标记已获取到数据，停止兜底检测
    hasDetectedData = true;
    if (detectInterval) {
      clearInterval(detectInterval);
      detectInterval = null;
      console.log('[JD-HELPER] 🛑 数据已获取，停止兜底检测');
    }

    // 🔧 数据结构统一处理 - 确保所有数据源格式一致
    let normalizedData = rawData;

    // 处理第三方数据：将 skuObj 内容提升到根级别
    if (source.startsWith('third_party') && rawData.skuObj) {
      console.log(`[JD-HELPER] 🔧 Normalizing third-party data structure`);
      normalizedData = {
        ...rawData.skuObj,  // 将 skuObj 的所有属性提升到根级别
        originalSkuId: rawData.skuId,  // 保留原始 skuId
        dataSource: 'third_party_normalized'  // 标记已处理
      };
      console.log(`[JD-HELPER] ✅ Third-party data normalized, price.p available:`, normalizedData.price?.p);
    }

    // 统一数据格式化
    const unifiedDataBuffer = {
      source: source,
      type: dataType,
      data: normalizedData,  // 使用标准化后的数据
      timestamp: Date.now(),
      sku: getCurrentSKUFromUrl(),
      processed: true // 标记已处理
    };

    // 提取商品ID作为缓存key
    const currentSKU = getCurrentSKUFromUrl();
    let cacheKey = currentSKU ? `jd_unified_data_${currentSKU}` : 'jd_unified_data_default';

    // 存储统一数据到localStorage
    localStorage.setItem(cacheKey, JSON.stringify(unifiedDataBuffer));

    // 分发统一数据事件 - 统一元素提取器只需监听这一个事件
    window.dispatchEvent(new CustomEvent('JdUnifiedData', {
      detail: unifiedDataBuffer
    }));

    console.log(`[JD-HELPER] ✅ 统一数据已处理并分发 (来源: ${source})`);
    return unifiedDataBuffer;
  }

  // 1. 立即多次尝试拦截 XMLHttpRequest（确保100%拦截成功）
  function patchXHR() {
    if (XMLHttpRequest.prototype._jdHelperPatched) {
      console.log('[JD-HELPER] XMLHttpRequest already patched, skipping...');
      return;
    }

    try {
      const originalXhrOpen = XMLHttpRequest.prototype.open;
      const originalXhrSend = XMLHttpRequest.prototype.send;

      // 标记已打补丁，防止重复
      XMLHttpRequest.prototype._jdHelperPatched = true;

      XMLHttpRequest.prototype.open = function (...args) {
        // 存储请求URL，以便在 'load' 事件中使用
        this._url = args[1];
        this._method = args[0];
        return originalXhrOpen.apply(this, args);
      };

      XMLHttpRequest.prototype.send = function (...args) {
        // 添加加载事件监听器
        this.addEventListener('load', () => {
          if (this._url && this._url.includes('functionId=pc_detailpage_wareBusiness')) {
            console.log('[JD-HELPER] 🎯 成功拦截XHR API请求:', this._url);

            try {
              const apiResponseData = JSON.parse(this.responseText);

              // 使用统一数据处理函数
              processUnifiedData(apiResponseData, 'xhr_api', 'product');

            } catch (err) {
              console.warn('[JD-HELPER] XHR响应解析失败:', err);
            }
          }
        });

        // 添加错误事件监听器
        this.addEventListener('error', () => {
          if (this._url && this._url.includes('functionId=pc_detailpage_wareBusiness')) {
            console.warn('[JD-HELPER] XHR请求失败:', this._url);
          }
        });

        return originalXhrSend.apply(this, args);
      };

      console.log('[JD-HELPER] ✅ XMLHttpRequest补丁已激活');
    } catch (error) {
      console.error('[JD-HELPER] XMLHttpRequest补丁安装失败:', error);
    }
  }

  // 2. 第三方插件数据检测功能 - 统一数据处理
  function setupThirdPartyDataDetection() {
    console.log('[JD-HELPER] 🎧 设置第三方插件事件监听器...');

    const currentSKU = getCurrentSKUFromUrl();
    console.log(`[JD-HELPER] 当前页面SKU: ${currentSKU}`);

    // 监听谷歌购物点点插件的数据事件
    window.addEventListener('jd_item_res', function (event) {
      console.log('[JD-HELPER] 🎯 捕获到 jd_item_res 事件:', event.detail);

      if (event.detail && event.detail.data) {
        // 使用统一数据处理函数，不再区分第三方来源
        processUnifiedData(event.detail.data, 'third_party_shopping', 'product');
      }
    });

    // 监听促销数据事件
    window.addEventListener('jd_item_prom_res', function (event) {
      console.log('[JD-HELPER] 🎯 捕获到 jd_item_prom_res 促销事件:', event.detail);

      if (event.detail && event.detail.data) {
        // 促销数据也通过统一处理函数
        processUnifiedData(event.detail.data, 'third_party_promo', 'promotion');
      }
    });

    // 启动兜底检测机制（优化版）
    startFallbackDetection();
  }

  // 3. 从URL中提取商品SKU
  function getCurrentSKUFromUrl() {
    const url = window.location.href;
    const match = url.match(/\/(\d+)\.html/);
    return match ? match[1] : null;
  }

  // 4. 兜底检测机制 - 智能停止版本
  function startFallbackDetection() {
    // 如果已经获取到任何数据，则不启动兜底检测
    if (hasDetectedData) {
      console.log('[JD-HELPER] 已有数据源，跳过兜底检测');
      return;
    }

    console.log('[JD-HELPER] 🔍 启动兜底检测机制...');

    let attempts = 0;
    const maxAttempts = 10; // 减少检测次数，提高性能

    detectInterval = setInterval(() => {
      attempts++;

      // 如果在检测过程中已获取到任何数据，立即停止
      if (hasDetectedData) {
        clearInterval(detectInterval);
        detectInterval = null;
        console.log('[JD-HELPER] 🛑 检测期间已获取数据，停止兜底检测');
        return;
      }

      console.log(`[JD-HELPER] 兜底检测第 ${attempts} 次...`);

      // 检查localStorage中是否有其他来源的统一数据
      const currentSKU = getCurrentSKUFromUrl();
      if (currentSKU) {
        const unifiedDataKey = `jd_unified_data_${currentSKU}`;
        const unifiedData = localStorage.getItem(unifiedDataKey);

        if (unifiedData) {
          console.log('[JD-HELPER] 🎯 localStorage中发现已有统一数据，停止检测');
          clearInterval(detectInterval);
          detectInterval = null;
          hasDetectedData = true;
          return;
        }
      }

      // 达到最大尝试次数，停止检测
      if (attempts >= maxAttempts) {
        console.log('[JD-HELPER] 🏁 兜底检测已达最大次数，停止检测');
        clearInterval(detectInterval);
        detectInterval = null;

        // 分发检测完成事件，通知content script使用DOM提取
        window.dispatchEvent(new CustomEvent('JdFallbackDetectionComplete', {
          detail: {
            hasUnifiedData: hasDetectedData,
            message: '兜底检测完成，可能需要DOM提取'
          }
        }));
      }
    }, 500); // 500ms间隔，平衡性能和响应速度
  }

  // 5. 提前多次尝试拦截，确保100%成功
  function ensureEarlyInterception() {
    // 立即尝试第一次拦截
    patchXHR();

    // 在不同的执行时机再次尝试，确保万无一失
    if (document.readyState === 'loading') {
      // 文档仍在加载中，在DOMContentLoaded时再次尝试
      document.addEventListener('DOMContentLoaded', () => {
        console.log('[JD-HELPER] 🔄 DOMContentLoaded - 再次确保XHR拦截');
        patchXHR();
      });
    }

    // 微任务队列中再次尝试
    Promise.resolve().then(() => {
      console.log('[JD-HELPER] 🔄 Promise microtask - 再次确保XHR拦截');
      patchXHR();
    });

    // 宏任务中再次尝试
    setTimeout(() => {
      console.log('[JD-HELPER] 🔄 setTimeout 0ms - 再次确保XHR拦截');
      patchXHR();
    }, 0);

    // 稍后再次尝试
    setTimeout(() => {
      console.log('[JD-HELPER] 🔄 setTimeout 10ms - 最后确保XHR拦截');
      patchXHR();
    }, 10);
  }

  // 6. 主执行流程
  function initialize() {
    console.log('[JD-HELPER] 🚀 开始初始化inject脚本...');

    // 第一步：确保XHR拦截尽早生效
    ensureEarlyInterception();

    // 第二步：设置第三方数据检测
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', setupThirdPartyDataDetection);
    } else {
      setupThirdPartyDataDetection();
    }

    console.log('[JD-HELPER] ✅ Inject脚本初始化完成');
  }

  // 立即开始初始化
  initialize();

})();
