<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>官方立减功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            color: #333;
            margin-top: 0;
        }
        .result {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧮 官方立减功能测试</h1>
        <p>测试优惠算法模块对"官方立减"促销的识别和计算能力</p>

        <div class="test-section">
            <h3>📊 测试用例1：官方立减15%（百分比立减）</h3>
            <p><strong>商品信息：</strong>原价158.7元，购买1件</p>
            <p><strong>促销信息：</strong>官方立减15%，无使用门槛</p>
            <p><strong>预期结果：</strong>158.7 * 15% = 23.805元，约24元</p>
            <button class="button" onclick="testOfficialReductionPercent()">🧪 测试百分比立减</button>
            <div id="result1" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📊 测试用例2：官方立减24元（固定金额立减）</h3>
            <p><strong>商品信息：</strong>原价158.7元，购买1件</p>
            <p><strong>促销信息：</strong>官方立减24元</p>
            <p><strong>预期结果：</strong>直接减免24元</p>
            <button class="button" onclick="testOfficialReductionAmount()">🧪 测试固定金额立减</button>
            <div id="result2" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📊 测试用例3：多件购买的官方立减</h3>
            <p><strong>商品信息：</strong>原价158.7元，购买2件</p>
            <p><strong>促销信息：</strong>官方立减15%</p>
            <p><strong>预期结果：</strong>158.7 * 2 * 15% = 47.61元</p>
            <button class="button" onclick="testOfficialReductionMultiple()">🧪 测试多件立减</button>
            <div id="result3" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📊 测试用例4：完整京东数据测试</h3>
            <p><strong>模拟京东真实数据：</strong>包含PLUS立减、单品满件折、官方立减的组合优惠</p>
            <button class="button" onclick="testRealJDData()">🧪 测试真实数据</button>
            <div id="result4" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🔍 算法调试工具</h3>
            <button class="button" onclick="showCalculatorInstance()">📋 查看算法实例</button>
            <button class="button" onclick="testPromotionParsing()">🔧 测试促销解析</button>
            <div id="debug-result" class="result"></div>
        </div>
    </div>

    <!-- 引入优惠算法模块 -->
    <script src="../js/优惠算法模块.js"></script>

    <script>
        // 初始化优惠计算器
        const calculator = new JDPromotionCalculator();
        
        function logResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : 'info';
            element.innerHTML = `<span class="${className}">[${timestamp}] ${message}</span>`;
        }

        // 测试官方立减百分比
        function testOfficialReductionPercent() {
            try {
                console.log('🧪 开始测试官方立减百分比...');
                
                const mockData = {
                    p: 158.7, // 原价
                    preferenceInfo: {
                        promotions: [
                            {
                                "crossStoreFullCut": false,
                                "formal": false,
                                "logPromoId": "286642058655",
                                "proSortNum": 190,
                                "promoId": 286642058655,
                                "shortText": "官方立减15%",
                                "tag": 90,
                                "text": "官方立减",
                                "typeNumber": "25",
                                "value": "官方立减15%，无使用门槛，7月31日 23:59结束"
                            }
                        ]
                    }
                };

                const result = calculator.calculatePromotions(mockData, 1);
                
                logResult('result1', `✅ 测试完成！
📊 计算结果：
  - 原价：¥${result.originalPrice}
  - 数量：${result.quantity}件
  - 总价：¥${result.totalPrice}
  - 优惠金额：¥${result.totalDiscount.toFixed(2)}
  - 到手价：¥${result.finalPrice.toFixed(2)}
  - 到手单价：¥${result.finalUnitPrice.toFixed(2)}

🎯 应用的促销：
${result.appliedPromotions.map(p => `  - ${p.text}: ${p.method || p.value}`).join('\n')}

📋 所有促销：
${result.allPromotions.map(p => `  - [${p.subType}] ${p.text}: ${p.method || p.value}`).join('\n')}`, 'success');

            } catch (error) {
                console.error('❌ 测试失败:', error);
                logResult('result1', `❌ 测试失败: ${error.message}`, 'error');
            }
        }

        // 测试官方立减固定金额
        function testOfficialReductionAmount() {
            try {
                console.log('🧪 开始测试官方立减固定金额...');
                
                const mockData = {
                    p: 158.7,
                    preferenceInfo: {
                        promotions: [
                            {
                                "crossStoreFullCut": false,
                                "formal": false,
                                "logPromoId": "286642058656",
                                "proSortNum": 190,
                                "promoId": 286642058656,
                                "shortText": "官方立减24元",
                                "tag": 90,
                                "text": "官方立减",
                                "typeNumber": "25",
                                "value": "官方立减24元，无使用门槛"
                            }
                        ]
                    }
                };

                const result = calculator.calculatePromotions(mockData, 1);
                
                logResult('result2', `✅ 测试完成！
📊 计算结果：
  - 原价：¥${result.originalPrice}
  - 数量：${result.quantity}件
  - 总价：¥${result.totalPrice}
  - 优惠金额：¥${result.totalDiscount.toFixed(2)}
  - 到手价：¥${result.finalPrice.toFixed(2)}
  - 到手单价：¥${result.finalUnitPrice.toFixed(2)}

🎯 应用的促销：
${result.appliedPromotions.map(p => `  - ${p.text}: ${p.method || p.value}`).join('\n')}

📋 所有促销：
${result.allPromotions.map(p => `  - [${p.subType}] ${p.text}: ${p.method || p.value}`).join('\n')}`, 'success');

            } catch (error) {
                console.error('❌ 测试失败:', error);
                logResult('result2', `❌ 测试失败: ${error.message}`, 'error');
            }
        }

        // 测试多件购买的官方立减
        function testOfficialReductionMultiple() {
            try {
                console.log('🧪 开始测试多件官方立减...');
                
                const mockData = {
                    p: 158.7,
                    preferenceInfo: {
                        promotions: [
                            {
                                "crossStoreFullCut": false,
                                "formal": false,
                                "logPromoId": "286642058655",
                                "proSortNum": 190,
                                "promoId": 286642058655,
                                "shortText": "官方立减15%",
                                "tag": 90,
                                "text": "官方立减",
                                "typeNumber": "25",
                                "value": "官方立减15%，无使用门槛，7月31日 23:59结束"
                            }
                        ]
                    }
                };

                const result = calculator.calculatePromotions(mockData, 2);
                
                logResult('result3', `✅ 测试完成！
📊 计算结果：
  - 原价：¥${result.originalPrice}
  - 数量：${result.quantity}件
  - 总价：¥${result.totalPrice}
  - 优惠金额：¥${result.totalDiscount.toFixed(2)}
  - 到手价：¥${result.finalPrice.toFixed(2)}
  - 到手单价：¥${result.finalUnitPrice.toFixed(2)}

🎯 应用的促销：
${result.appliedPromotions.map(p => `  - ${p.text}: ${p.method || p.value}`).join('\n')}`, 'success');

            } catch (error) {
                console.error('❌ 测试失败:', error);
                logResult('result3', `❌ 测试失败: ${error.message}`, 'error');
            }
        }

        // 测试真实京东数据
        function testRealJDData() {
            try {
                console.log('🧪 开始测试真实京东数据...');
                
                const mockData = {
                    p: 158.7,
                    preferenceInfo: {
                        promotions: [
                            {
                                "crossStoreFullCut": false,
                                "formal": false,
                                "link": "//plus.jd.com/right/index#item-exclusive",
                                "logPromoId": "308050394650",
                                "proSortNum": 20,
                                "promoId": 308050394650,
                                "promoTags": [1128],
                                "shortText": "PLUS专享立减1.59元",
                                "tag": 40,
                                "text": "PLUS专享立减",
                                "typeNumber": "",
                                "value": "可与PLUS价、满减、券等优惠叠加使用"
                            },
                            {
                                "crossStoreFullCut": false,
                                "formal": false,
                                "logPromoId": "307354521213",
                                "proSortNum": 100,
                                "promoId": 307354521213,
                                "shortText": "单品立享8折",
                                "tag": 97,
                                "text": "单品满件折",
                                "typeNumber": "",
                                "value": "满1件享8.00折"
                            },
                            {
                                "crossStoreFullCut": false,
                                "formal": false,
                                "logPromoId": "286642058655",
                                "proSortNum": 190,
                                "promoId": 286642058655,
                                "shortText": "官方立减15%",
                                "tag": 90,
                                "text": "官方立减",
                                "typeNumber": "25",
                                "value": "官方立减15%，无使用门槛，7月31日 23:59结束"
                            }
                        ]
                    }
                };

                const result = calculator.calculatePromotions(mockData, 1);
                
                logResult('result4', `✅ 真实数据测试完成！
📊 计算结果：
  - 原价：¥${result.originalPrice}
  - 数量：${result.quantity}件
  - 总价：¥${result.totalPrice}
  - 优惠金额：¥${result.totalDiscount.toFixed(2)}
  - 到手价：¥${result.finalPrice.toFixed(2)}
  - 到手单价：¥${result.finalUnitPrice.toFixed(2)}

🎯 应用的促销：
${result.appliedPromotions.map(p => `  - ${p.text}: ${p.shortText || p.method || p.value}`).join('\n')}

📋 所有促销：
${result.allPromotions.map(p => `  - [${p.subType}] ${p.text}: ${p.shortText || p.method || p.value}`).join('\n')}

💡 验证计算：
  京东价: ¥158.7
  - PLUS立减: ¥1.59
  - 单品8折减免: ¥${(158.7 * 0.2).toFixed(2)}
  - 官方立减15%: ¥${(158.7 * 0.15).toFixed(2)}
  预期到手价: ¥${(158.7 - 1.59 - 158.7 * 0.2 - 158.7 * 0.15).toFixed(2)}`, 'success');

            } catch (error) {
                console.error('❌ 测试失败:', error);
                logResult('result4', `❌ 测试失败: ${error.message}`, 'error');
            }
        }

        // 显示计算器实例信息
        function showCalculatorInstance() {
            try {
                const patterns = calculator.promotionPatterns;
                logResult('debug-result', `🔍 优惠算法模块调试信息：

📋 促销匹配规则：
${Object.entries(patterns).map(([key, pattern]) => 
    `  - ${key}: ${pattern.toString()}`
).join('\n')}

🧪 官方立减测试：
${calculator.isOfficialReductionPromotion ? '✅ 官方立减识别方法已加载' : '❌ 官方立减识别方法未加载'}
${calculator.parseOfficialReduction ? '✅ 官方立减解析方法已加载' : '❌ 官方立减解析方法未加载'}`, 'info');

            } catch (error) {
                logResult('debug-result', `❌ 调试失败: ${error.message}`, 'error');
            }
        }

        // 测试促销解析
        function testPromotionParsing() {
            try {
                const testPromotion = {
                    "shortText": "官方立减15%",
                    "tag": 90,
                    "text": "官方立减",
                    "value": "官方立减15%，无使用门槛，7月31日 23:59结束"
                };

                const parsed = calculator.parseSinglePromotion(testPromotion);
                
                logResult('debug-result', `🔧 促销解析测试：

🎯 输入数据：
${JSON.stringify(testPromotion, null, 2)}

📊 解析结果：
${JSON.stringify(parsed, null, 2)}

✅ 解析状态：
  - 是否识别为官方立减: ${calculator.isOfficialReductionPromotion(testPromotion.text, testPromotion.shortText)}
  - 官方立减信息: ${JSON.stringify(calculator.parseOfficialReduction(testPromotion.shortText, testPromotion.value))}`, 'success');

            } catch (error) {
                logResult('debug-result', `❌ 解析测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后显示初始信息
        window.onload = function() {
            console.log('🎉 官方立减功能测试页面加载完成');
            showCalculatorInstance();
        };
    </script>
</body>
</html>
