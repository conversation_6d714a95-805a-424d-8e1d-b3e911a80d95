<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>官方立减快速测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>官方立减快速测试</h1>
    
    <button class="button" onclick="quickTest()">🧪 快速测试</button>
    <div id="result" class="result"></div>

    <script src="../js/优惠算法模块.js"></script>
    <script>
        function quickTest() {
            try {
                const calculator = new JDPromotionCalculator();
                console.log('🧪 开始快速测试官方立减...');
                
                // 测试数据
                const testData = {
                    p: 158.7,
                    preferenceInfo: {
                        promotions: [
                            {
                                "shortText": "官方立减15%",
                                "tag": 90,
                                "text": "官方立减", 
                                "value": "官方立减15%，无使用门槛，7月31日 23:59结束"
                            }
                        ]
                    }
                };

                // 测试识别
                const promotion = testData.preferenceInfo.promotions[0];
                const isOfficialReduction = calculator.isOfficialReductionPromotion(promotion.text, promotion.shortText);
                const parsedInfo = calculator.parseOfficialReduction(promotion.shortText, promotion.value);
                const fullParsed = calculator.parseSinglePromotion(promotion);
                
                console.log('识别结果:', isOfficialReduction);
                console.log('解析结果:', parsedInfo);
                console.log('完整解析:', fullParsed);
                
                // 测试完整计算
                const result = calculator.calculatePromotions(testData, 1);
                
                document.getElementById('result').innerHTML = `
                    <h3>测试结果</h3>
                    <p><strong>识别为官方立减:</strong> ${isOfficialReduction}</p>
                    <p><strong>解析信息:</strong> ${JSON.stringify(parsedInfo)}</p>
                    <p><strong>完整解析:</strong> ${JSON.stringify(fullParsed, null, 2)}</p>
                    <p><strong>计算结果:</strong></p>
                    <ul>
                        <li>原价: ¥${result.originalPrice}</li>
                        <li>优惠金额: ¥${result.totalDiscount.toFixed(2)}</li>
                        <li>到手价: ¥${result.finalPrice.toFixed(2)}</li>
                        <li>到手单价: ¥${result.finalUnitPrice.toFixed(2)}</li>
                    </ul>
                    <p><strong>应用的促销:</strong></p>
                    <ul>
                        ${result.appliedPromotions.map(p => `<li>${p.text}: 减免¥${p.discountAmount?.toFixed(2) || '0'}</li>`).join('')}
                    </ul>
                    <p><strong>预期计算:</strong> 158.7 × 15% = ${(158.7 * 0.15).toFixed(2)}元</p>
                `;
                
            } catch (error) {
                console.error('测试失败:', error);
                document.getElementById('result').innerHTML = `❌ 测试失败: ${error.message}`;
            }
        }
        
        // 页面加载后自动测试
        window.onload = function() {
            quickTest();
        };
    </script>
</body>
</html>
