/**
 * 复制格式化文案模块 - 解耦版
 * 不再依赖统一元素提取器，直接使用inject.js的统一数据和算法模块
 */

// 防止重复声明
if (typeof window.FormattedCopyManager === 'undefined') {

    class FormattedCopyManager {
        constructor() {
            this.initialized = false;
            this.promotionData = null; // 存储算法模块的优惠数据
        }

        /**
         * 初始化模块
         */
        init() {
            this.initialized = true;
            console.log('格式化文案模块初始化完成 - 解耦版');
            
            // 监听算法模块的优惠计算结果
            document.addEventListener('JdPromotionCalculated', (event) => {
                if (event.detail && event.detail.results && event.detail.results.optimal) {
                    this.promotionData = event.detail.results.optimal;
                    console.log('格式化文案模块收到算法模块数据:', this.promotionData);
                }
            });
        }

        /**
         * 从inject.js统一数据中获取商品原价
         */
        getOriginalPrice() {
            try {
                // 获取当前商品SKU
                const currentSKU = this.getCurrentSKU();
                if (!currentSKU) {
                    console.warn('无法获取当前商品SKU');
                    return 0;
                }

                // 从localStorage获取统一数据
                const unifiedDataKey = `jd_unified_data_${currentSKU}`;
                const unifiedDataString = localStorage.getItem(unifiedDataKey);
                
                if (!unifiedDataString) {
                    console.warn('未找到统一数据，尝试从页面提取原价');
                    return this.extractPriceFromDOM();
                }

                const unifiedData = JSON.parse(unifiedDataString);
                
                // 从统一数据中提取原价 (p字段)
                let originalPrice = 0;
                if (unifiedData.data && unifiedData.data.price && unifiedData.data.price.p) {
                    originalPrice = parseFloat(unifiedData.data.price.p);
                }

                console.log('从统一数据获取原价:', originalPrice);
                return isNaN(originalPrice) ? 0 : originalPrice;

            } catch (error) {
                console.error('获取原价失败:', error);
                return this.extractPriceFromDOM();
            }
        }

        /**
         * 从DOM中提取原价作为备用方案
         */
        extractPriceFromDOM() {
            try {
                // 尝试多种价格元素选择器
                const priceSelectors = [
                    '.dd .price-now', // 当前价格
                    '.price-original', // 原价
                    '.p-price .price', // 价格
                    '[data-price]' // 数据属性价格
                ];

                for (const selector of priceSelectors) {
                    const priceEl = document.querySelector(selector);
                    if (priceEl) {
                        const priceText = priceEl.textContent || priceEl.getAttribute('data-price');
                        const price = parseFloat(priceText.replace(/[^\d.]/g, ''));
                        if (!isNaN(price) && price > 0) {
                            console.log('从DOM提取到价格:', price);
                            return price;
                        }
                    }
                }

                console.warn('无法从DOM提取价格');
                return 0;
            } catch (error) {
                console.error('DOM价格提取失败:', error);
                return 0;
            }
        }

        /**
         * 获取当前商品SKU
         */
        getCurrentSKU() {
            const url = window.location.href;
            const match = url.match(/\/(\d+)\.html/);
            return match ? match[1] : null;
        }

        /**
         * 获取活动到手价（从算法模块）
         */
        getActivityPrice() {
            try {
                // 优先从算法模块的优惠数据获取最优单价
                if (this.promotionData && this.promotionData.optimalUnitPrice > 0) {
                    console.log('从算法模块获取到手价:', this.promotionData.optimalUnitPrice);
                    return this.promotionData.optimalUnitPrice;
                }

                // 如果算法模块没有数据，使用原价
                console.log('算法模块暂无数据，使用原价作为到手价');
                return this.getOriginalPrice();
            } catch (error) {
                console.error('获取活动价失败:', error);
                return this.getOriginalPrice();
            }
        }

        /**
         * 获取最优购买数量（从算法模块）
         */
        getOptimalQuantity() {
            try {
                // 从算法模块的优惠数据获取最优购买数量
                if (this.promotionData && this.promotionData.optimalQuantity > 0) {
                    console.log('从算法模块获取最优购买数量:', this.promotionData.optimalQuantity);
                    return this.promotionData.optimalQuantity;
                }

                // 如果算法模块没有数据，默认返回1
                console.log('算法模块暂无数据，使用默认购买数量1');
                return 1;
            } catch (error) {
                console.error('获取最优购买数量失败:', error);
                return 1;
            }
        }

        /**
         * 计算折扣（活动到手价除以原价）
         */
        calculateDiscount() {
            const originalPrice = this.getOriginalPrice();
            const activityPrice = this.getActivityPrice();

            if (originalPrice > 0 && activityPrice > 0) {
                const discountRatio = activityPrice / originalPrice;
                const discount = Math.round(discountRatio * 10); // 四舍五入取整
                
                // 如果折扣是10折（即原价等于到手价），返回特殊标识
                if (discount >= 10) {
                    return '特价';
                }
                
                return discount;
            }

            return 10;
        }

        /**
         * 获取商品标题（简化版）
         */
        getCleanTitle() {
            try {
                // 方法1：优先从页面title获取
                const pageTitle = document.title;
                if (pageTitle) {
                    // 去掉京东相关后缀，如："【行情 报价 价格 评测】-京东"
                    let cleanTitle = pageTitle
                        .replace(/【[^】]*】.*?-京东.*?$/i, '') // 去掉【行情 报价 价格 评测】-京东
                        .replace(/-京东.*?$/i, '') // 去掉其他-京东后缀
                        .replace(/【[^】]*】/g, '') // 去掉所有【】括号内容
                        .replace(/\s+/g, ' ') // 合并多个空格
                        .trim();
                    
                    if (cleanTitle && cleanTitle.length > 5) { // 确保标题有实际内容
                        console.log('从页面title提取标题:', cleanTitle);
                        return cleanTitle;
                    }
                }

                // 方法2：从.sku-name-title获取纯文本
                const skuNameTitle = document.querySelector('.sku-name-title');
                if (skuNameTitle) {
                    // 克隆节点并移除所有img标签，只保留文本
                    const clonedNode = skuNameTitle.cloneNode(true);
                    const images = clonedNode.querySelectorAll('img');
                    images.forEach(img => img.remove());
                    
                    let titleText = clonedNode.textContent
                        .replace(/\s+/g, ' ') // 合并多个空格
                        .replace(/收藏\s*$/, '') // 去掉末尾的"收藏"
                        .trim();
                    
                    if (titleText && titleText.length > 5) {
                        console.log('从.sku-name-title提取标题:', titleText);
                        return titleText;
                    }
                }

                // 备用方案：其他常见选择器
                const fallbackSelectors = ['.sku-name', 'h1'];
                for (const selector of fallbackSelectors) {
                    const titleEl = document.querySelector(selector);
                    if (titleEl && titleEl.textContent.trim()) {
                        const fallbackTitle = titleEl.textContent
                            .replace(/\s+/g, ' ')
                            .replace(/【.*?】/g, '')
                            .replace(/收藏\s*$/, '')
                            .trim();
                        
                        if (fallbackTitle && fallbackTitle.length > 5) {
                            console.log(`从备用选择器${selector}提取标题:`, fallbackTitle);
                            return fallbackTitle;
                        }
                    }
                }

                console.warn('无法提取商品标题');
                return '商品标题获取失败';
                
            } catch (error) {
                console.error('获取商品标题失败:', error);
                return '商品标题获取失败';
            }
        }

        /**
         * 获取转换后的链接
         */
        getConvertedLink() {
            // 直接使用全局转换后的链接
            return window.convertedLink || location.href.split('#')[0];
        }

        /**
         * 生成格式化文案
         */
        generateFormattedContent() {
            try {
                const title = this.getCleanTitle();
                const convertedLink = this.getConvertedLink();
                const originalPrice = this.getOriginalPrice();
                const activityPrice = this.getActivityPrice();
                const discount = this.calculateDiscount();
                const optimalQuantity = this.getOptimalQuantity();
                
                // 计算总价和节省金额
                const totalPrice = (activityPrice * optimalQuantity);
                const originalTotalPrice = (originalPrice * optimalQuantity);
                const savings = originalTotalPrice - totalPrice;
                
                // 判断是否为特价（原价等于到手价）
                const isSpecialPrice = discount === '特价' || Math.abs(originalPrice - activityPrice) < 0.01;

                console.log('文案生成数据 (解耦版):', {
                    title,
                    convertedLink,
                    originalPrice,
                    activityPrice,
                    discount,
                    optimalQuantity,
                    totalPrice,
                    originalTotalPrice,
                    savings,
                    isSpecialPrice
                });

                // 构建文案
                let content = '';

                // 标题行 - 包含折扣和商品标题
                if (isSpecialPrice) {
                    content += `✅【特价】${title}\n\n`;
                } else {
                    content += `✅【${discount}折】${title}\n\n`;
                }

                // 链接行
                content += `👉${convertedLink}\n`;
                content += `----------------\n`;

                // 价格提示
                content += `❗价格不对，可能地区价，或优惠已结束\n`;

                if (isSpecialPrice) {
                    // 特价情况：不显示原价和省钱，简化显示
                    content += `👉购买${optimalQuantity}件最优\n`;
                    content += `✅到手单价：${activityPrice.toFixed(2)}`;
                } else {
                    // 有优惠情况：显示完整信息
                    content += `👉商品原价：${originalPrice}\n`;
                    
                    if (savings > 0.01) { // 有实际节省
                        content += `👉购买${optimalQuantity}件最优，省${savings.toFixed(1)}\n`;
                    } else {
                        content += `👉购买${optimalQuantity}件最优\n`;
                    }
                    
                    content += `👇参与优惠后\n`;
                    content += `✅到手总价：${totalPrice.toFixed(2)}\n`;
                    content += `✅到手单价：${activityPrice.toFixed(2)}`;
                }

                console.log('生成的格式化文案 (解耦版):', content);
                return content;

            } catch (error) {
                console.error('生成格式化文案失败:', error);
                return '文案生成失败，请重试';
            }
        }

        /**
         * 复制格式化文案到剪贴板
         */
        async copyFormattedContent() {
            if (!this.initialized) {
                console.warn('格式化文案模块未初始化');
                return { success: false, message: '模块未初始化' };
            }

            try {
                const content = this.generateFormattedContent();

                if (content) {
                    await navigator.clipboard.writeText(content);
                    return {
                        success: true,
                        message: '格式化文案已复制',
                        content: content
                    };
                } else {
                    return {
                        success: false,
                        message: '文案生成失败'
                    };
                }

            } catch (error) {
                console.error('复制格式化文案失败:', error);
                return {
                    success: false,
                    message: '复制失败: ' + error.message
                };
            }
        }

        /**
         * 获取当前商品信息摘要
         */
        getProductSummary() {
            return {
                title: this.getCleanTitle(),
                originalPrice: this.getOriginalPrice(),
                activityPrice: this.getActivityPrice(),
                discount: this.calculateDiscount(),
                optimalQuantity: this.getOptimalQuantity(),
                link: this.getConvertedLink()
            };
        }
    }

    // 创建全局实例并自动初始化
    window.FormattedCopyManager = new FormattedCopyManager();
    window.FormattedCopyManager.init(); // 立即初始化，设置事件监听器

    // 导出模块
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = FormattedCopyManager;
    }

} // 结束 if 语句
