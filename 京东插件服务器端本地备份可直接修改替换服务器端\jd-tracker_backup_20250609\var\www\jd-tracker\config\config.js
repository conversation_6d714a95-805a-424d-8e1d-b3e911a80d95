// 服务器配置文件
module.exports = {
  // 服务器端口
  port: process.env.PORT || 3000,
    // 数据库配置
  database: {
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'tracker_user',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'product_tracker',
    port: process.env.DB_PORT || 3306,
    connectionLimit: 10,
    waitForConnections: true,
    queueLimit: 0
  },
  
  // JWT密钥
  jwtSecret: process.env.JWT_SECRET || 'your-jwt-secret-key',
  
  // 允许的源地址
  allowedOrigins: process.env.ALLOWED_ORIGINS ? 
    process.env.ALLOWED_ORIGINS.split(',') : [
      'chrome-extension://*',
      'moz-extension://*'
    ],
  
  // API配置
  api: {
    // 京东商品信息API
    jdApi: {
      baseUrl: 'http://japi.jingtuitui.com/api/universal',
      appid: process.env.JD_APPID || '2504011836194311',
      appkey: process.env.JD_APPKEY || '5516658d1c3ec236d35df07851d1d082',
      version: 'v3',
      unionid: process.env.JD_UNIONID || '2003821643',
      timeout: 10000
    }
  },
  
  // 定时任务配置
  scheduler: {
    // 价格更新频率 (cron表达式)
    priceUpdateCron: '0 */30 * * * *', // 每30分钟
    // 清理过期数据频率
    cleanupCron: '0 0 2 * * *', // 每天凌晨2点
    // 数据备份频率
    backupCron: '0 0 4 * * 0' // 每周日凌晨4点
  },
  
  // 缓存配置
  cache: {
    // Redis配置（可选）
    redis: {
      host: process.env.REDIS_HOST || 'localhost',
      port: process.env.REDIS_PORT || 6379,
      password: process.env.REDIS_PASSWORD || '',
      db: process.env.REDIS_DB || 0
    },
    // 内存缓存TTL (秒)
    ttl: 300 // 5分钟
  },
  
  // 日志配置
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    file: process.env.LOG_FILE || './logs/app.log',
    maxSize: '10m',
    maxFiles: 5
  }
};
