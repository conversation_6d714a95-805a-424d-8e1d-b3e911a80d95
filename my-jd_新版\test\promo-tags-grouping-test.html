<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>promoTags分组规则测试</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .result { 
            background: #f8f9fa; 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 5px; 
            font-family: monospace;
            white-space: pre-wrap;
        }
        .button { 
            background: #007bff; 
            color: white; 
            border: none; 
            padding: 10px 20px; 
            border-radius: 5px; 
            cursor: pointer; 
            margin: 5px;
        }
        .button:hover { background: #0056b3; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏷️ promoTags分组规则测试</h1>
        <p>测试优惠算法模块对promoTags字段的分组处理逻辑</p>

        <div class="test-section">
            <h3>📊 测试场景说明</h3>
            <table>
                <tr>
                    <th>促销</th>
                    <th>text</th>
                    <th>tag</th>
                    <th>promoTags</th>
                    <th>预期分组</th>
                </tr>
                <tr>
                    <td>PLUS95折</td>
                    <td>PLUS95折</td>
                    <td>40</td>
                    <td>[30]</td>
                    <td>promoTags_30</td>
                </tr>
                <tr>
                    <td>满减</td>
                    <td>满减</td>
                    <td>15</td>
                    <td>[39]</td>
                    <td>promoTags_39</td>
                </tr>
                <tr>
                    <td>多买优惠</td>
                    <td>多买优惠</td>
                    <td>19</td>
                    <td>[39]</td>
                    <td>promoTags_39（与满减同组！）</td>
                </tr>
            </table>
            <p><strong>关键测试点：</strong>满减和多买优惠有相同的promoTags[39]，应该被分为同一组，不可叠加</p>
        </div>

        <div class="test-section">
            <h3>🧪 测试案例1：有promoTags的分组测试</h3>
            <button class="button" onclick="testPromoTagsGrouping()">🏷️ 测试promoTags分组</button>
            <div id="result1" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🧪 测试案例2：混合分组测试（有无promoTags混合）</h3>
            <button class="button" onclick="testMixedGrouping()">🔀 测试混合分组</button>
            <div id="result2" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🧪 测试案例3：完整计算测试</h3>
            <button class="button" onclick="testFullCalculation()">🧮 测试完整计算</button>
            <div id="result3" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🔍 分组算法调试</h3>
            <button class="button" onclick="debugGrouping()">🔧 调试分组逻辑</button>
            <div id="debug-result" class="result"></div>
        </div>
    </div>

    <script src="../js/优惠算法模块.js"></script>
    <script>
        const calculator = new JDPromotionCalculator();
        
        function logResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : 'info';
            element.innerHTML = `<span class="${className}">[${timestamp}] ${message}</span>`;
        }

        // 测试promoTags分组
        function testPromoTagsGrouping() {
            try {
                console.log('🧪 测试promoTags分组规则...');
                
                const testPromotions = [
                    {
                        "crossStoreFullCut": false,
                        "formal": false,
                        "link": "//plus.jd.com/right/index#item-discount",
                        "logPromoId": "275221832973",
                        "proSortNum": 50,
                        "promoId": 275221832973,
                        "promoTags": [30],
                        "shortText": "PLUS95折 立减2.34元",
                        "tag": 40,
                        "text": "PLUS95折",
                        "typeNumber": "",
                        "value": "可与PLUS价、满减、券等优惠叠加使用"
                    },
                    {
                        "crossStoreFullCut": false,
                        "customTag": {},
                        "formal": false,
                        "logPromoId": "288867100271",
                        "proSortNum": 80,
                        "promoId": 288867100271,
                        "promoTags": [39],
                        "tag": 15,
                        "text": "满减",
                        "typeNumber": "15",
                        "value": "66元选3件"
                    },
                    {
                        "crossStoreFullCut": false,
                        "customTag": {},
                        "formal": false,
                        "logPromoId": "288719783763",
                        "promoId": 288719783763,
                        "promoTags": [39],
                        "shortText": "满3件享7折",
                        "tag": 19,
                        "text": "多买优惠",
                        "typeNumber": "15",
                        "value": "满2件，总价打8折；满3件，总价打7折"
                    }
                ];

                const groups = calculator.groupPromotionsByText(testPromotions);
                
                logResult('result1', `✅ promoTags分组测试完成！

📊 分组结果：
${Object.entries(groups).map(([key, promos]) => 
    `🏷️ 组 "${key}":
    包含促销: ${promos.length}个
    ${promos.map(p => `    - ${p.text} (tag:${p.tag}, promoTags:${JSON.stringify(p.promoTags)})`).join('\n')}`
).join('\n\n')}

🔍 分组分析：
- PLUS95折：promoTags[30] → 独立分组 ✅
- 满减：promoTags[39] → 分组promoTags_39 ✅  
- 多买优惠：promoTags[39] → 分组promoTags_39（与满减同组）✅

✨ 预期行为：满减和多买优惠有相同promoTags[39]，被正确分为同一组！
这意味着它们不能同时享受，只会选择最优的一个。`, 'success');

            } catch (error) {
                console.error('❌ 测试失败:', error);
                logResult('result1', `❌ 测试失败: ${error.message}`, 'error');
            }
        }

        // 测试混合分组
        function testMixedGrouping() {
            try {
                console.log('🧪 测试混合分组（有无promoTags混合）...');
                
                const testPromotions = [
                    {
                        "text": "PLUS专享立减",
                        "tag": 40,
                        "promoTags": [1128],
                        "value": "PLUS专享立减1.59元"
                    },
                    {
                        "text": "单品满件折",
                        "tag": 97,
                        // 注意：没有promoTags字段
                        "value": "满1件享8.00折"
                    },
                    {
                        "text": "官方立减",
                        "tag": 90,
                        // 注意：没有promoTags字段
                        "value": "官方立减15%"
                    },
                    {
                        "text": "满减",
                        "tag": 15,
                        "promoTags": [39],
                        "value": "66元选3件"
                    }
                ];

                const groups = calculator.groupPromotionsByText(testPromotions);
                
                logResult('result2', `✅ 混合分组测试完成！

📊 分组结果：
${Object.entries(groups).map(([key, promos]) => 
    `🏷️ 组 "${key}":
    包含促销: ${promos.length}个
    ${promos.map(p => `    - ${p.text} (tag:${p.tag}, promoTags:${JSON.stringify(p.promoTags || 'undefined')})`).join('\n')}`
).join('\n\n')}

🔍 分组分析：
- 有promoTags的促销：按promoTags分组
- 无promoTags的促销：按text字段分组
- 不同组之间可以叠加使用

✨ 预期行为：4个促销被分为4个不同的组，理论上都可以叠加！`, 'success');

            } catch (error) {
                console.error('❌ 测试失败:', error);
                logResult('result2', `❌ 测试失败: ${error.message}`, 'error');
            }
        }

        // 测试完整计算
        function testFullCalculation() {
            try {
                console.log('🧪 测试完整计算（包含promoTags分组）...');
                
                const mockData = {
                    p: 46.8, // 假设原价
                    preferenceInfo: {
                        promotions: [
                            {
                                "crossStoreFullCut": false,
                                "formal": false,
                                "logPromoId": "275221832973",
                                "proSortNum": 50,
                                "promoId": 275221832973,
                                "promoTags": [30],
                                "shortText": "PLUS95折 立减2.34元",
                                "tag": 40,
                                "text": "PLUS95折",
                                "value": "可与PLUS价、满减、券等优惠叠加使用"
                            },
                            {
                                "crossStoreFullCut": false,
                                "logPromoId": "288867100271",
                                "proSortNum": 80,
                                "promoId": 288867100271,
                                "promoTags": [39],
                                "tag": 15,
                                "text": "满减",
                                "value": "66元选3件"
                            },
                            {
                                "crossStoreFullCut": false,
                                "logPromoId": "288719783763",
                                "promoId": 288719783763,
                                "promoTags": [39],
                                "shortText": "满3件享7折",
                                "tag": 19,
                                "text": "多买优惠",
                                "value": "满2件，总价打8折；满3件，总价打7折"
                            }
                        ]
                    }
                };

                const result = calculator.calculatePromotions(mockData, 3); // 购买3件测试多买优惠
                
                logResult('result3', `✅ 完整计算测试完成！

📊 计算结果：
  - 原价：¥${result.originalPrice}
  - 数量：${result.quantity}件
  - 总价：¥${result.totalPrice}
  - 优惠金额：¥${result.totalDiscount.toFixed(2)}
  - 到手价：¥${result.finalPrice.toFixed(2)}
  - 到手单价：¥${result.finalUnitPrice.toFixed(2)}

🎯 应用的促销：
${result.appliedPromotions.map(p => `  - ${p.text}: 减免¥${p.discountAmount?.toFixed(2) || '0'}`).join('\n')}

📋 所有促销：
${result.allPromotions.map(p => `  - [${p.subType}] ${p.text}: ${p.shortText || p.value}`).join('\n')}

🔍 分组验证：
- PLUS95折 (promoTags:[30])：独立组 ✅
- 满减 vs 多买优惠 (都是promoTags:[39])：同组，只选最优 ✅

💡 关键验证点：
满减和多买优惠因为有相同的promoTags[39]，只会选择其中最优的一个！`, 'success');

            } catch (error) {
                console.error('❌ 测试失败:', error);
                logResult('result3', `❌ 测试失败: ${error.message}`, 'error');
            }
        }

        // 调试分组逻辑
        function debugGrouping() {
            try {
                const testData = [
                    { text: "促销A", promoTags: [30] },
                    { text: "促销B", promoTags: [39] },
                    { text: "促销C", promoTags: [39] },
                    { text: "促销D" }, // 没有promoTags
                    { text: "促销E", promoTags: [30, 50] },
                    { text: "促销F", promoTags: [50, 30] } // 相同标签不同顺序
                ];

                const groups = calculator.groupPromotionsByText(testData);
                
                logResult('debug-result', `🔧 分组逻辑调试：

📊 测试数据：
${testData.map((p, i) => `${i+1}. ${p.text} - promoTags: ${JSON.stringify(p.promoTags || 'undefined')}`).join('\n')}

🏷️ 分组结果：
${Object.entries(groups).map(([key, promos]) => 
    `组 "${key}": ${promos.map(p => p.text).join(', ')}`
).join('\n')}

✨ 分组规则验证：
- 有promoTags：按promoTags数组值分组
- 无promoTags：按text字段分组  
- 相同promoTags的促销（即使text不同）会被分为同一组
- promoTags数组会自动排序，确保[30,50]和[50,30]被视为相同`, 'info');

            } catch (error) {
                logResult('debug-result', `❌ 调试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后显示初始信息
        window.onload = function() {
            console.log('🎉 promoTags分组测试页面加载完成');
            testPromoTagsGrouping(); // 自动运行第一个测试
        };
    </script>
</body>
</html>
