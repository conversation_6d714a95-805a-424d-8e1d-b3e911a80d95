/**
 * 价格走势分析模块 - 简化版本
 * 先插入元素，再异步获取数据库数据更新显示
 */

// console.log('价格走势模块开始加载...');

(function () {
    'use strict';

    // console.log('价格走势模块 IIFE 开始执行');

    // 检查是否已存在我们的插件
    if (document.getElementById('ltby-price-trend-root')) {
        // console.log('价格走势插件已存在，跳过初始化');
        return;
    }

    // 价格趋势数据
    let currentTrendData = {
        trend: '新收',
        priceHistory: [],
        currentPrice: 0,
        skuId: ''
    };

    // 当前插入的元素引用
    let currentTrendElement = null;
    let currentPopover = null;

    // 创建价格走势元素
    function createPriceTrendElement(trendType = '新收') {
        // console.log('开始创建价格走势元素, 趋势:', trendType);

        // 根据趋势类型设置不同的颜色和文字
        const trendConfig = {
            '新收': { color: 'blue', text: '新收' },
            '平稳': { color: 'gray', text: '平稳' },
            '上涨': { color: 'red', text: '上涨' },
            '下降': { color: 'green', text: '下降' },
            '最低': { color: 'gold', text: '最低' }
        };

        const config = trendConfig[trendType] || trendConfig['新收'];        // 创建简洁的HTML结构
        const htmlStructure = `
            <div id="ltby-price-trend-root" class="ltby-ui" data-trend="${trendType}">
                <div class="ltby-trend-container">
                    <span class="ltby-trend-tag ltby-trend-${config.color}">
                        <span class="ltby-trend-icon">📊</span>
                        <span class="ltby-trend-text">${config.text}</span>
                    </span>
                </div>
            </div>
        `;

        // 创建容器元素
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = htmlStructure;
        const priceTrendElement = tempDiv.firstElementChild;

        // 添加基础样式
        const style = document.createElement('style');
        style.textContent = `
            .ltby-ui {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-size: 12px;
                line-height: 1.4;
            }
            .ltby-trend-container {
                display: inline-block;
            }
            .ltby-trend-tag {
                display: inline-flex;
                align-items: center;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.3s ease;
            }
            .ltby-trend-blue { background: #e6f7ff; color: #1890ff; border: 1px solid #91d5ff; }
            .ltby-trend-gray { background: #f5f5f5; color: #8c8c8c; border: 1px solid #d9d9d9; }
            .ltby-trend-red { background: #fff2f0; color: #f5222d; border: 1px solid #ffccc7; }
            .ltby-trend-green { background: #f6ffed; color: #52c41a; border: 1px solid #b7eb8f; }
            .ltby-trend-gold { background: #fffbe6; color: #faad14; border: 1px solid #ffe58f; }
            .ltby-trend-tag:hover {
                opacity: 0.8;
                transform: translateY(-1px);
            }
            .ltby-trend-icon {
                margin-right: 4px;
            }
        `;

        if (!document.querySelector('#ltby-trend-styles')) {
            style.id = 'ltby-trend-styles';
            document.head.appendChild(style);
        }

        // console.log('价格走势元素创建完成');
        return priceTrendElement;
    }

    // 更新趋势显示
    function updateTrendDisplay(newTrend) {
        if (!currentTrendElement) return;

        // console.log('更新趋势显示:', newTrend);

        const trendConfig = {
            '新收': { color: 'blue', text: '新收' },
            '平稳': { color: 'gray', text: '平稳' },
            '上涨': { color: 'red', text: '上涨' },
            '下降': { color: 'green', text: '下降' },
            '最低': { color: 'gold', text: '最低' }
        };

        const config = trendConfig[newTrend] || trendConfig['新收'];

        // 更新元素属性
        currentTrendElement.setAttribute('data-trend', newTrend);

        // 更新标签样式和文字
        const trendTag = currentTrendElement.querySelector('.ltby-trend-tag');
        const trendText = currentTrendElement.querySelector('.ltby-trend-text');

        if (trendTag && trendText) {
            // 移除旧的颜色类
            trendTag.className = trendTag.className.replace(/ltby-trend-(blue|gray|red|green|gold)/, '');
            // 添加新的颜色类
            trendTag.classList.add(`ltby-trend-${config.color}`);
            // 更新文字
            trendText.textContent = config.text;
        }

        // 更新当前数据
        currentTrendData.trend = newTrend;

        // console.log('趋势显示更新完成:', newTrend);
    }

    // 插入到页面
    function insertPriceTrendElement() {
        // console.log('开始尝试插入价格走势元素...');

        // 多种插入策略
        const strategies = [
            // 策略1: 优先检测下柜提示
            () => {
                const delistedTip = document.querySelector('.itemover-tip');
                if (delistedTip && delistedTip.textContent.includes('该商品已下柜')) {
                    // console.log('检测到商品已下柜，插入到下柜提示右侧');
                    return { container: delistedTip.parentNode, position: 'after', style: 'float: right; margin-right: 20px;' };
                }
                return null;
            },

            // 策略2: 预约享资格元素
            () => {
                const reservationElement = document.querySelector('.yy-category.J-yy-category');
                if (reservationElement && !reservationElement.classList.contains('hide')) {
                    // console.log('检测到预约享资格元素，插入到其右侧');
                    return { container: reservationElement.parentNode, position: 'after', style: 'display: inline-block; margin-left: 10px; vertical-align: top;' };
                }
                return null;
            },

            // 策略3: 国际站comment-count
            () => {
                if (location.hostname.includes('jd.hk')) {
                    const commentCountElement = document.querySelector('#comment-count.comment-count.item.fl');
                    if (commentCountElement) {
                        // console.log('检测到国际站comment-count元素');
                        return { container: commentCountElement, position: 'append', style: 'display: inline-block; margin-left: 8px; vertical-align: middle;' };
                    }
                }
                return null;
            },

            // 策略4: 普通商品页面的comment-count区域
            () => {
                const commentCount = document.querySelector('#comment-count');
                if (commentCount) {
                    const summaryInfo = commentCount.closest('.summary-info');
                    if (summaryInfo) {
                        // console.log('找到comment-count区域的summary-info');
                        return { container: summaryInfo, position: 'prepend', style: 'display: inline-block; margin-right: 10px;' };
                    }
                }
                return null;
            },

            // 策略5: commentNotice内的summary-info
            () => {
                const target = document.querySelector('.commentNotice .summary-info');
                if (target) {
                    // console.log('找到commentNotice内的summary-info');
                    return { container: target, position: 'prepend', style: 'display: inline-block; margin-right: 10px;' };
                }
                return null;
            },

            // 策略6: 任何summary-info
            () => {
                const summaryInfos = document.querySelectorAll('.summary-info');
                for (let summaryInfo of summaryInfos) {
                    if (!summaryInfo.closest('.choose-btns') && !summaryInfo.closest('.btn-area')) {
                        // console.log('找到合适的summary-info');
                        return { container: summaryInfo, position: 'prepend', style: 'display: inline-block; margin-right: 10px;' };
                    }
                }
                return null;
            }
        ];

        for (let i = 0; i < strategies.length; i++) {
            const result = strategies[i]();
            if (result) {
                // console.log(`使用策略${i + 1}插入:`, result);

                // 创建价格走势元素（默认显示'新收'）
                const priceTrendElement = createPriceTrendElement('新收');

                // 应用样式
                if (result.style) {
                    priceTrendElement.style.cssText = result.style;
                }

                // 插入到指定位置
                switch (result.position) {
                    case 'prepend':
                        result.container.insertBefore(priceTrendElement, result.container.firstChild);
                        break;
                    case 'append':
                        result.container.appendChild(priceTrendElement);
                        break;
                    case 'after':
                        if (result.container.nextSibling) {
                            result.container.parentNode.insertBefore(priceTrendElement, result.container.nextSibling);
                        } else {
                            result.container.parentNode.appendChild(priceTrendElement);
                        }
                        break;
                }

                // 保存引用
                currentTrendElement = priceTrendElement;

                // console.log('价格走势元素插入成功！');

                // 创建悬停弹窗
                currentPopover = createPriceTrendPopover();

                // 添加事件监听
                setupEventListeners(priceTrendElement, currentPopover);
                // 异步获取数据库数据并更新显示
                setTimeout(() => {
                    initializeDatabaseData();
                    // 设置第三方数据监听器
                    setupThirdPartyDataListener();
                }, 100);

                return true;
            }
        }

        // console.log('所有插入策略都失败了');
        return false;
    }

    // 创建悬停弹窗
    function createPriceTrendPopover() {
        const popoverHTML = `
            <div class="ltby-price-popover" id="ltby-price-popover" style="
                position: absolute;
                display: none;
                background: white;
                border: 1px solid #d9d9d9;
                border-radius: 6px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                padding: 12px;
                min-width: 280px;
                max-width: 400px;
                z-index: 10000;
                font-size: 12px;
                line-height: 1.4;
            ">
                <div class="ltby-popover-header" style="border-bottom: 1px solid #f0f0f0; padding-bottom: 8px; margin-bottom: 8px;">
                    <div style="font-weight: bold; color: #1890ff;">LTBY价格走势分析</div>
                    <div id="ltby-trend-status" style="color: #666; margin-top: 4px;">状态: 新收录商品</div>
                </div>
                <div class="ltby-popover-content">
                    <div id="ltby-price-summary" style="margin-bottom: 12px;">
                        <div>当前活动价: ¥<span id="ltby-current-price">0</span></div>
                    </div>
                    <!-- 图表切换标签 -->
                    <div id="ltby-chart-tabs" style="display: flex; margin-bottom: 12px; border-bottom: 1px solid #e0e0e0;">
                        <button class="ltby-tab-btn active" data-mode="price" style="flex: 1; padding: 8px 12px; border: none; background: none; cursor: pointer; font-size: 12px; color: #1890ff; border-bottom: 2px solid #1890ff;">
                            商品价格走势
                        </button>
                        <button class="ltby-tab-btn" data-mode="market" style="flex: 1; padding: 8px 12px; border: none; background: none; cursor: pointer; font-size: 12px; color: #666; border-bottom: 2px solid transparent;">
                            市场价格走势
                        </button>
                    </div>

                    <!-- 价格走势图容器 -->
                    <div id="ltby-price-chart-container" style="margin-bottom: 12px;">
                        <canvas id="ltby-price-chart" width="360" height="180" style="border: 1px solid #e0e0e0; border-radius: 4px; background: #fafafa; display: block;"></canvas>
                    </div>

                    <!-- 市场价格走势图容器 -->
                    <div id="ltby-market-chart-container" style="margin-bottom: 12px; display: none;">
                        <canvas id="ltby-market-chart" width="360" height="180" style="border: 1px solid #e0e0e0; border-radius: 4px; background: #fafafa; display: block;"></canvas>
                    </div>
                </div>
                <div class="ltby-popover-footer" style="border-top: 1px solid #f0f0f0; padding-top: 8px; margin-top: 8px;">
                    <div style="color: #999; font-size: 11px;">· 价格信息由LTBY数据库提供，仅供参考</div>
                </div>
            </div>
        `;

        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = popoverHTML;
        const popover = tempDiv.firstElementChild;

        document.body.appendChild(popover);

        // 绑定标签切换事件
        bindTabEvents(popover);

        // 绑定Canvas鼠标事件
        bindCanvasEvents(popover);

        return popover;
    }

    // 绑定标签切换事件
    function bindTabEvents(popover) {
        const tabBtns = popover.querySelectorAll('.ltby-tab-btn');
        const priceChartContainer = popover.querySelector('#ltby-price-chart-container');
        const marketChartContainer = popover.querySelector('#ltby-market-chart-container');

        tabBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const mode = btn.dataset.mode;

                // 更新标签状态
                tabBtns.forEach(tab => {
                    if (tab === btn) {
                        tab.style.color = '#1890ff';
                        tab.style.borderBottomColor = '#1890ff';
                        tab.classList.add('active');
                    } else {
                        tab.style.color = '#666';
                        tab.style.borderBottomColor = 'transparent';
                        tab.classList.remove('active');
                    }
                });

                // 切换显示内容
                if (mode === 'price') {
                    priceChartContainer.style.display = 'block';
                    marketChartContainer.style.display = 'none';
                    // 绘制商品价格走势图
                    setTimeout(() => drawPriceChart(), 100);
                } else if (mode === 'market') {
                    priceChartContainer.style.display = 'none';
                    marketChartContainer.style.display = 'block';
                    // 绘制市场价格走势图
                    setTimeout(() => drawMarketChart(), 100);
                }
            });
        });
    }

    // 绑定Canvas鼠标事件
    function bindCanvasEvents(popover) {
        const priceCanvas = popover.querySelector('#ltby-price-chart');
        const marketCanvas = popover.querySelector('#ltby-market-chart');

        if (priceCanvas) {
            bindCanvasMouseEvents(priceCanvas, 'price');
        }
        if (marketCanvas) {
            bindCanvasMouseEvents(marketCanvas, 'market');
        }
    }

    // 绑定单个Canvas的鼠标事件
    function bindCanvasMouseEvents(canvas, chartType) {
        let tooltip = null;

        canvas.addEventListener('mousemove', (event) => {
            const rect = canvas.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;

            const tooltipData = getTooltipData(x, y, canvas, chartType);

            // 调试日志（已禁用，避免刷屏）
            // if (tooltipData) {
            //     console.log('🖱️ 显示tooltip:', tooltipData);
            // }

            if (tooltipData) {
                showTooltip(event.clientX, event.clientY, tooltipData);
            } else {
                hideTooltip();
            }
        });

        canvas.addEventListener('mouseleave', () => {
            hideTooltip();
        });

        function showTooltip(clientX, clientY, data) {
            hideTooltip(); // 先隐藏现有的tooltip

            tooltip = document.createElement('div');
            tooltip.className = 'ltby-chart-tooltip';
            tooltip.style.cssText = `
                position: fixed;
                background: white;
                color: #333;
                padding: 10px 12px;
                border-radius: 6px;
                font-size: 12px;
                z-index: 10001;
                pointer-events: none;
                white-space: nowrap;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                border: 1px solid #e0e0e0;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            `;

            tooltip.innerHTML = `
                <div style="font-weight: bold; margin-bottom: 6px; color: #333;">${data.date}</div>
                <div style="color: #1890ff; font-weight: bold; margin-bottom: 2px;">● 售价: ¥${data.price}</div>
                <div style="color: #666; font-size: 11px;">${data.description}</div>
            `;

            document.body.appendChild(tooltip);

            // 调整位置避免超出屏幕
            const tooltipRect = tooltip.getBoundingClientRect();
            let left = clientX + 10;
            let top = clientY - tooltipRect.height - 10;

            if (left + tooltipRect.width > window.innerWidth) {
                left = clientX - tooltipRect.width - 10;
            }
            if (top < 0) {
                top = clientY + 10;
            }

            tooltip.style.left = left + 'px';
            tooltip.style.top = top + 'px';
        }

        function hideTooltip() {
            if (tooltip && tooltip.parentNode) {
                document.body.removeChild(tooltip);
                tooltip = null;
            }
        }

        // 清理现有的tooltip（避免重复）
        function cleanupTooltips() {
            const existingTooltips = document.querySelectorAll('.ltby-chart-tooltip');
            existingTooltips.forEach(tip => {
                if (tip.parentNode) {
                    tip.parentNode.removeChild(tip);
                }
            });
        }
    }

    // 获取tooltip数据
    function getTooltipData(mouseX, mouseY, canvas, chartType) {
        const padding = 40;
        const width = canvas.width;
        const height = canvas.height;
        const chartWidth = width - padding * 2;
        const chartHeight = height - padding * 2;

        // 检查鼠标是否在图表区域内
        if (mouseX < padding || mouseX > padding + chartWidth ||
            mouseY < padding || mouseY > padding + chartHeight) {
            return null;
        }

        if (chartType === 'price') {
            return getPriceTooltipData(mouseX, mouseY, padding, chartWidth, chartHeight);
        } else if (chartType === 'market') {
            return getMarketTooltipData(mouseX, mouseY, padding, chartWidth, chartHeight);
        }

        return null;
    }

    // 获取价格图表的tooltip数据
    function getPriceTooltipData(mouseX, mouseY, padding, chartWidth, chartHeight) {
        // 从全局对象或当前数据获取
        const trendData = window.currentTrendData || currentTrendData;
        const currentPriceRaw = trendData.currentPrice || 0;
        // 确保价格是数字类型
        const currentPrice = parseFloat(currentPriceRaw) || 0;
        const trend = trendData.trend || '新收';
        const priceHistory = trendData.priceHistory || [];

        // 调试日志（简化）
        // console.log('🔍 获取tooltip数据:', { currentPrice, trend });

        // 如果是直线（新收或平稳）
        if (trend === '新收' || trend === '平稳' || priceHistory.length <= 1) {
            const today = new Date();
            const dateStr = formatTooltipDate(today);

            // 获取真实的价格说明
            let description = '新收录商品';

            // 直接从页面元素提取价格说明（已解耦统一元素提取器）
            try {
                // 尝试从页面元素直接提取价格说明
                const priceDescElement = document.querySelector('.p-price .price-desc, .summary-price .desc, .price-description');
                if (priceDescElement && priceDescElement.textContent.trim()) {
                    description = priceDescElement.textContent.trim();
                } else {
                    // 备用方案：根据页面价格元素判断价格类型
                    const promotionElement = document.querySelector('.prom-item, .promotion-text, .coupon-info');
                    if (promotionElement) {
                        description = '促销价格';
                    }
                }
            } catch (error) {
                console.log('[价格走势] 提取价格说明失败，使用默认值');
            }

            // 如果没有获取到价格说明，使用数据库结果
            if (description === '新收录商品') {
                const dbResult = window.databaseManager?.lastCheckResult;
                if (dbResult) {
                    // 使用当前商品的价格说明
                    if (dbResult.product?.current_price_desc) {
                        description = dbResult.product.current_price_desc;
                    }
                    // 如果没有当前价格说明，从历史记录获取
                    else if (priceHistory.length > 0) {
                        const latestItem = priceHistory[priceHistory.length - 1];
                        description = latestItem.price_desc ||
                            (trend === '平稳' ? '价格平稳' : '新收录商品');
                    }
                }
            }

            const tooltipData = {
                date: dateStr,
                price: currentPrice.toFixed(2),
                description: description
            };

            return tooltipData;
        }

        // 如果是曲线，找到最接近的数据点
        const data = prepareChartData();
        if (data.length < 2) {
            return null;
        }

        // 计算鼠标位置对应的数据点
        const relativeX = mouseX - padding;
        const dataIndex = Math.round((relativeX / chartWidth) * (data.length - 1));

        if (dataIndex >= 0 && dataIndex < data.length) {
            const item = data[dataIndex];
            // 从原始历史数据中找到对应的项
            const historyItem = priceHistory.find(h => {
                const hDate = new Date(h.recorded_at);
                return Math.abs(hDate - item.date) < 24 * 60 * 60 * 1000; // 1天内
            }) || priceHistory[Math.min(dataIndex, priceHistory.length - 1)];

            return {
                date: formatTooltipDate(item.date),
                price: item.price.toFixed(2),
                description: historyItem?.price_method || historyItem?.price_desc || '活动价'
            };
        }

        return null;
    }

    // 获取市场价格图表的tooltip数据
    function getMarketTooltipData(mouseX, mouseY, padding, chartWidth, chartHeight) {
        const marketPrice = getMarketPrice();

        if (!marketPrice || marketPrice <= 0) {
            return null;
        }

        const today = new Date();
        const dateStr = formatTooltipDate(today);

        return {
            date: dateStr,
            price: marketPrice.toFixed(2),
            description: '市场价格'
        };
    }

    // 格式化tooltip日期
    function formatTooltipDate(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day} 00:00:00`;
    }

    // 绘制市场价格走势图
    function drawMarketChart() {
        const canvas = document.getElementById('ltby-market-chart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const width = canvas.width;
        const height = canvas.height;

        // 清空画布
        ctx.clearRect(0, 0, width, height);

        // 获取市场价格
        const marketPrice = getMarketPrice();

        if (!marketPrice || marketPrice <= 0) {
            drawNoMarketPriceMessage(ctx, width, height);
            return;
        }

        // 绘制市场价格直线
        drawMarketPriceLine(ctx, width, height, marketPrice);
    }

    // 获取市场价格
    function getMarketPrice() {
        // 从数据库结果获取市场价格
        if (window.databaseManager && window.databaseManager.lastCheckResult) {
            const result = window.databaseManager.lastCheckResult;
            if (result.marketPriceInfo && result.marketPriceInfo.market_price) {
                return parseFloat(result.marketPriceInfo.market_price);
            }
            if (result.product && result.product.market_price) {
                return parseFloat(result.product.market_price);
            }
        }
        return 0;
    }

    // 绘制无市场价格消息
    function drawNoMarketPriceMessage(ctx, width, height) {
        ctx.fillStyle = '#999';
        ctx.font = '14px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('暂无市场价格数据', width / 2, height / 2 - 10);
        ctx.fillText('请先设置市场价格', width / 2, height / 2 + 10);
    }

    // 绘制市场价格直线
    function drawMarketPriceLine(ctx, width, height, marketPrice) {
        const padding = 40;
        const chartWidth = width - padding * 2;
        const chartHeight = height - padding * 2;
        const centerY = padding + chartHeight / 2;

        // 绘制背景网格
        drawSimpleGrid(ctx, padding, chartWidth, chartHeight);

        // 绘制市场价格直线
        ctx.strokeStyle = '#faad14'; // 橙色表示市场价
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(padding, centerY);
        ctx.lineTo(padding + chartWidth, centerY);
        ctx.stroke();

        // 绘制端点
        ctx.fillStyle = '#faad14';
        ctx.beginPath();
        ctx.arc(padding, centerY, 4, 0, 2 * Math.PI);
        ctx.fill();
        ctx.beginPath();
        ctx.arc(padding + chartWidth, centerY, 4, 0, 2 * Math.PI);
        ctx.fill();

        // 显示市场价格
        ctx.fillStyle = '#333';
        ctx.font = 'bold 16px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(`¥${marketPrice.toFixed(2)}`, width / 2, centerY - 25);

        // 显示描述
        ctx.fillStyle = '#666';
        ctx.font = '12px Arial';
        ctx.fillText('市场价格', width / 2, centerY + 25);

        // 显示日期范围
        const today = new Date();
        const dateStr = `${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;

        ctx.fillStyle = '#999';
        ctx.font = '10px Arial';
        ctx.textAlign = 'left';
        ctx.fillText(dateStr, padding, padding + chartHeight + 15);
        ctx.textAlign = 'right';
        ctx.fillText(dateStr, padding + chartWidth, padding + chartHeight + 15);
    }

    // 更新弹窗内容
    function updatePopoverContent() {
        if (!currentPopover) return;

        const statusElement = currentPopover.querySelector('#ltby-trend-status');
        const currentPriceElement = currentPopover.querySelector('#ltby-current-price');
        const historyListElement = currentPopover.querySelector('#ltby-history-list');

        // 更新状态
        if (statusElement) {
            const statusText = currentTrendData.trend === '新收' ?
                '新收录商品' :
                `价格${currentTrendData.trend}`;
            statusElement.textContent = `状态: ${statusText}`;
            statusElement.style.color = getTrendColor(currentTrendData.trend);
            statusElement.style.fontWeight = 'bold';
        }

        // 更新当前价格
        if (currentPriceElement) {
            const numericPrice = parseFloat(currentTrendData.currentPrice);
            currentPriceElement.textContent = isNaN(numericPrice) ? '0.00' : numericPrice.toFixed(2);
        }

        // 更新历史记录
        if (historyListElement) {
            if (currentTrendData.priceHistory && currentTrendData.priceHistory.length > 0) {
                const historyHTML = currentTrendData.priceHistory.slice(-10).reverse().map(item => {
                    // 只显示日期，格式：25-06-01
                    const date = new Date(item.recorded_at);
                    const year = date.getFullYear().toString().slice(-2);
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    const formattedDate = `${year}-${month}-${day}`;
                    const trendIcon = {
                        '新收': '🆕',
                        '平稳': '➖',
                        '上涨': '⬆️',
                        '下降': '⬇️',
                        '最低': '👑'
                    }[item.price_trend] || '📊';

                    // 修复价格显示问题：尝试多个字段，确保数据来源严谨
                    const price = extractPriceFromHistoryItem(item);

                    return `
                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 4px 0; border-bottom: 1px solid #f5f5f5;">
                            <span style="color: #666; font-size: 11px;">${formattedDate}</span>
                            <div style="display: flex; flex-direction: column; align-items: flex-end;">
                                <span style="font-weight: bold; color: #e74c3c;">¥${price.toFixed(2)}</span>
                                <span style="color: #999; font-size: 10px;">${item.price_method || item.price_desc || '活动价'}</span>
                            </div>
                            <span style="font-size: 14px;">${trendIcon}</span>
                        </div>
                    `;
                }).join('');
                historyListElement.innerHTML = historyHTML;
            } else {
                historyListElement.innerHTML = `
                    <div style="text-align: center; color: #999; padding: 20px;">
                        <div>暂无历史记录</div>
                        <div style="font-size: 11px; margin-top: 4px;">这是首次收录的商品</div>
                    </div>
                `;
            }
        }
    }

    // 获取趋势颜色
    function getTrendColor(trend) {
        const colors = {
            '新收': '#1890ff',
            '平稳': '#8c8c8c',
            '上涨': '#f5222d',
            '下降': '#52c41a',
            '最低': '#faad14'
        };
        return colors[trend] || '#1890ff';
    }

    // 从历史记录项中提取价格（确保数据严谨性）
    function extractPriceFromHistoryItem(item) {
        // 按优先级尝试不同的价格字段
        const priceFields = [
            'activity_price',    // 活动价格（优先）
            'price',            // 基础价格
            'current_price',    // 当前价格
            'final_price',      // 最终价格
            'original_price'    // 原价
        ];

        for (const field of priceFields) {
            if (item[field] !== undefined && item[field] !== null) {
                const price = parseFloat(item[field]);
                if (!isNaN(price) && price > 0) {
                    return price;
                }
            }
        }

        // 如果所有字段都无效，记录警告并返回0
        console.warn('⚠️ 历史记录中未找到有效价格字段:', item);
        return 0;
    }

    // 绘制价格走势图（重写版：新收/无变化显示直线）
    function drawPriceChart() {
        const canvas = document.getElementById('ltby-price-chart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const width = canvas.width;
        const height = canvas.height;

        // 清空画布
        ctx.clearRect(0, 0, width, height);

        // 获取当前价格和趋势
        const currentPriceRaw = currentTrendData.currentPrice || 0;
        const currentPrice = parseFloat(currentPriceRaw) || 0;  // 确保是数字
        const trend = currentTrendData.trend || '新收';
        const priceHistory = currentTrendData.priceHistory || [];

        // 判断显示类型
        if (trend === '新收' || priceHistory.length <= 1) {
            // 新收或无历史数据：显示直线
            drawStraightLine(ctx, width, height, currentPrice, '新收录商品');
        } else if (trend === '平稳') {
            // 价格平稳：显示直线
            const lastPriceRaw = extractPriceFromHistoryItem(priceHistory[priceHistory.length - 1]);
            const lastPrice = parseFloat(lastPriceRaw) || currentPrice;  // 确保是数字
            drawStraightLine(ctx, width, height, lastPrice, '价格平稳');
        } else {
            // 有价格变化：显示曲线
            const data = prepareChartData();
            if (data.length >= 2) {
                drawChart(ctx, data, width, height);
            } else {
                drawStraightLine(ctx, width, height, currentPrice, '数据不足');
            }
        }
    }

    // 准备图表数据
    function prepareChartData() {
        const history = currentTrendData.priceHistory || [];

        // 转换数据格式
        const data = history.map(item => ({
            date: new Date(item.recorded_at),
            price: extractPriceFromHistoryItem(item)
        })).filter(item => item.price > 0);

        // 按日期排序
        data.sort((a, b) => a.date - b.date);

        return data;
    }

    // 绘制直线（新收/平稳状态）
    function drawStraightLine(ctx, width, height, price, description) {
        const padding = 40;
        const chartWidth = width - padding * 2;
        const chartHeight = height - padding * 2;
        const centerY = padding + chartHeight / 2;

        // 绘制背景网格
        drawSimpleGrid(ctx, padding, chartWidth, chartHeight);

        // 绘制水平直线
        ctx.strokeStyle = '#52c41a'; // 绿色表示稳定
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(padding, centerY);
        ctx.lineTo(padding + chartWidth, centerY);
        ctx.stroke();

        // 绘制端点
        ctx.fillStyle = '#52c41a';
        ctx.beginPath();
        ctx.arc(padding, centerY, 4, 0, 2 * Math.PI);
        ctx.fill();
        ctx.beginPath();
        ctx.arc(padding + chartWidth, centerY, 4, 0, 2 * Math.PI);
        ctx.fill();

        // 显示价格信息
        ctx.fillStyle = '#333';
        ctx.font = 'bold 16px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(`¥${price.toFixed(2)}`, width / 2, centerY - 25);

        // 显示状态描述
        ctx.fillStyle = '#666';
        ctx.font = '12px Arial';
        ctx.fillText(description, width / 2, centerY + 25);

        // 显示日期范围
        const today = new Date();
        const dateStr = `${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;

        ctx.fillStyle = '#999';
        ctx.font = '10px Arial';
        ctx.textAlign = 'left';
        ctx.fillText(dateStr, padding, padding + chartHeight + 15);
        ctx.textAlign = 'right';
        ctx.fillText(dateStr, padding + chartWidth, padding + chartHeight + 15);
    }

    // 绘制简单网格（用于直线图）
    function drawSimpleGrid(ctx, padding, chartWidth, chartHeight) {
        ctx.strokeStyle = '#f5f5f5';
        ctx.lineWidth = 1;

        // 只绘制几条主要的网格线
        const centerY = padding + chartHeight / 2;

        // 水平中线
        ctx.beginPath();
        ctx.moveTo(padding, centerY);
        ctx.lineTo(padding + chartWidth, centerY);
        ctx.stroke();

        // 垂直线
        for (let i = 1; i < 4; i++) {
            const x = padding + (chartWidth / 4) * i;
            ctx.beginPath();
            ctx.moveTo(x, padding);
            ctx.lineTo(x, padding + chartHeight);
            ctx.stroke();
        }
    }

    // 绘制图表
    function drawChart(ctx, data, width, height) {
        const padding = 40;
        const chartWidth = width - padding * 2;
        const chartHeight = height - padding * 2;

        // 计算价格范围
        const prices = data.map(item => item.price);
        const minPrice = Math.min(...prices);
        const maxPrice = Math.max(...prices);
        const priceRange = maxPrice - minPrice || 1;

        // 绘制背景网格
        drawGrid(ctx, padding, chartWidth, chartHeight, minPrice, maxPrice);

        // 绘制价格线
        drawPriceLine(ctx, data, padding, chartWidth, chartHeight, minPrice, priceRange);

        // 绘制坐标轴标签
        drawAxisLabels(ctx, data, padding, chartWidth, chartHeight, minPrice, maxPrice);
    }

    // 绘制网格
    function drawGrid(ctx, padding, chartWidth, chartHeight, minPrice, maxPrice) {
        ctx.strokeStyle = '#f0f0f0';
        ctx.lineWidth = 1;

        // 水平网格线（价格）
        for (let i = 0; i <= 4; i++) {
            const y = padding + (chartHeight / 4) * i;
            ctx.beginPath();
            ctx.moveTo(padding, y);
            ctx.lineTo(padding + chartWidth, y);
            ctx.stroke();
        }

        // 垂直网格线（时间）
        for (let i = 0; i <= 6; i++) {
            const x = padding + (chartWidth / 6) * i;
            ctx.beginPath();
            ctx.moveTo(x, padding);
            ctx.lineTo(x, padding + chartHeight);
            ctx.stroke();
        }
    }

    // 绘制价格线
    function drawPriceLine(ctx, data, padding, chartWidth, chartHeight, minPrice, priceRange) {
        if (data.length < 2) return;

        ctx.strokeStyle = '#1890ff';
        ctx.lineWidth = 2;
        ctx.beginPath();

        data.forEach((item, index) => {
            const x = padding + (chartWidth / (data.length - 1)) * index;
            const y = padding + chartHeight - ((item.price - minPrice) / priceRange) * chartHeight;

            if (index === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        });

        ctx.stroke();

        // 绘制数据点
        ctx.fillStyle = '#1890ff';
        data.forEach((item, index) => {
            const x = padding + (chartWidth / (data.length - 1)) * index;
            const y = padding + chartHeight - ((item.price - minPrice) / priceRange) * chartHeight;

            ctx.beginPath();
            ctx.arc(x, y, 3, 0, 2 * Math.PI);
            ctx.fill();
        });
    }

    // 绘制坐标轴标签
    function drawAxisLabels(ctx, data, padding, chartWidth, chartHeight, minPrice, maxPrice) {
        ctx.fillStyle = '#666';
        ctx.font = '10px Arial';

        // Y轴标签（价格）
        ctx.textAlign = 'right';
        ctx.textBaseline = 'middle';
        for (let i = 0; i <= 4; i++) {
            const price = minPrice + (maxPrice - minPrice) * (1 - i / 4);
            const y = padding + (chartHeight / 4) * i;
            ctx.fillText(`¥${price.toFixed(1)}`, padding - 5, y);
        }

        // X轴标签（日期）
        ctx.textAlign = 'center';
        ctx.textBaseline = 'top';
        const maxLabels = 4;
        const step = Math.max(1, Math.floor(data.length / maxLabels));

        data.forEach((item, index) => {
            if (index % step === 0 || index === data.length - 1) {
                const x = padding + (chartWidth / (data.length - 1)) * index;
                const dateStr = `${String(item.date.getMonth() + 1).padStart(2, '0')}-${String(item.date.getDate()).padStart(2, '0')}`;
                ctx.fillText(dateStr, x, padding + chartHeight + 5);
            }
        });
    }

    // 前端价格趋势计算已移除 - 现在使用服务器端计算的结果    
    
    // 获取当前商品信息（直接从页面元素提取）
    function getCurrentProductInfo() {
        try {
            // 直接从页面元素提取基础信息
            const sku = getCurrentSkuFromUrl();
            if (!sku) return null;
            
            const priceElement = document.querySelector('.price-now, .p-price .price, .summary-price em');
            const titleElement = document.querySelector('.sku-name, .product-name, h1');
            
            return {
                basic: {
                    skuId: sku,
                    title: titleElement ? titleElement.textContent.trim() : '商品',
                    price: priceElement ? priceElement.textContent.replace(/[^\d.]/g, '') : '0'
                }
            };
        } catch (error) {
            console.error('获取商品信息失败:', error);
            return null;
        }
    }

    // 获取当前最优价格（直接从页面元素）
    function getCurrentBestPrice() {
        try {
            const priceElement = document.querySelector('.price-now, .p-price .price, .summary-price em');
            if (priceElement) {
                const priceText = priceElement.textContent.replace(/[^\d.]/g, '');
                return parseFloat(priceText) || 0;
            }
        } catch (error) {
            console.log('获取价格失败:', error);
        }
        return 0;
    }

    // 初始化数据库数据
    async function initializeDatabaseData() {
        try {
            console.log('🔄 开始初始化数据库数据...');

            // 获取当前商品信息
            const productInfo = getCurrentProductInfo();
            if (!productInfo || !productInfo.basic || !productInfo.basic.skuId) {
                console.error('❌ 无法获取商品信息');
                updateTrendDisplay('新收');
                updatePopoverContent();
                return;
            }

            console.log('📋 获取的商品信息:', productInfo);

            // 检查数据库管理器是否可用
            if (!window.databaseManager) {
                console.log('❌ 数据库管理器不可用，显示基础状态');
                currentTrendData.skuId = productInfo.basic.skuId;
                currentTrendData.currentPrice = getCurrentBestPrice();
                currentTrendData.trend = '新收';
                updateTrendDisplay('新收');
                updatePopoverContent();
                return;
            }

            const skuId = productInfo.basic.skuId;
            const currentPrice = getCurrentBestPrice();

            // 更新当前趋势数据
            currentTrendData.skuId = skuId;
            currentTrendData.currentPrice = currentPrice;

            // 检查是否已有数据库结果，避免重复调用
            if (window.databaseManager && window.databaseManager.lastCheckResult) {
                console.log('🔍 使用已有的数据库检查结果');
                const checkResult = window.databaseManager.lastCheckResult;
                processDatabaseResult(checkResult, skuId);
                return;
            }

            // 如果没有现有结果，监听数据库完成事件
            console.log('🔍 等待数据库检查完成...');
            document.addEventListener('databaseFullUpdateComplete', (event) => {
                console.log('🔍 收到数据库完成事件:', event.detail);
                processDatabaseResult(event.detail, skuId);
            }, { once: true });

            return; // 不再直接调用数据库检查
        } catch (error) {
            console.error('💥 初始化数据库数据失败:', error);

            // 错误时显示基础状态
            currentTrendData.trend = '新收';
            updateTrendDisplay('新收');
            updatePopoverContent();
        }
    }

    /**
     * 处理数据库检查结果（优化版：使用服务器计算的趋势）
     */
    function processDatabaseResult(checkResult, skuId) {
        if (checkResult.success) {
            console.log('✅ 商品页面检查成功:', checkResult);

            // 更新本地数据
            currentTrendData.priceHistory = checkResult.priceHistory || [];
            currentTrendData.currentPrice = checkResult.currentPrice || getCurrentBestPrice();

            // 使用服务器端计算的价格趋势（不再前端重复计算）
            const serverTrend = window.databaseManager?.getPriceTrendText(checkResult.priceTrend) || checkResult.priceTrend;
            currentTrendData.trend = serverTrend;

            console.log('📊 使用服务器端价格走势分析结果:', {
                serverTrend: serverTrend,
                rawTrend: checkResult.priceTrend,
                isNew: checkResult.isNewProduct,
                historyCount: currentTrendData.priceHistory.length,
                currentPrice: currentTrendData.currentPrice
            });

            // 更新显示
            updateTrendDisplay(serverTrend);

        } else {
            console.error('❌ 商品页面检查失败:', checkResult.error);

            // 错误时显示基础状态
            currentTrendData.trend = '新收';
            currentTrendData.priceHistory = [];
            updateTrendDisplay('新收');
        }

        // 更新弹窗内容
        updatePopoverContent();

        // 绘制价格图表
        setTimeout(() => drawPriceChart(), 500);

        console.log('🎉 数据库数据初始化完成:', currentTrendData);

        // 设置数据库事件监听器（用于后续更新）
        setupDatabaseEventListener();
    }

    // 设置数据库事件监听器
    function setupDatabaseEventListener() {
        console.log('📡 设置数据库事件监听器...');

        // 监听数据库检查完成事件
        document.addEventListener('databaseCheckComplete', function (event) {
            console.log('📡 接收到数据库检查完成事件:', event.detail);

            const result = event.detail;
            if (result.success && result.skuId === currentTrendData.skuId) {
                // 更新价格走势数据
                const trendText = window.databaseManager.getPriceTrendText(result.priceTrend);
                currentTrendData.trend = trendText;
                currentTrendData.priceHistory = result.priceHistory || [];
                currentTrendData.currentPrice = result.currentPrice || 0;

                console.log('✅ 价格走势数据已更新:', currentTrendData);

                // 更新显示
                updateTrendDisplay(trendText);
                updatePopoverContent();
            }
        });
    }

    // 设置第三方数据监听（直接监听页面变化）
    let thirdPartyListenerSetup = false;
    function setupThirdPartyDataListener() {
        if (thirdPartyListenerSetup) {
            console.log('📡 第三方数据监听器已设置，跳过重复设置');
            return;
        }

        console.log('📡 设置第三方数据监听器');
        thirdPartyListenerSetup = true;

        // 监听第三方数据更新事件
        document.addEventListener('thirdPartyDataReady', (event) => {
            console.log('🔔 第三方数据更新:', event.detail);

            if (event.detail && event.detail.isValid && event.detail.numericPrice > 0) {
                console.log('💰 检测到新的第三方价格:', event.detail.numericPrice);

                // 延迟处理，确保页面稳定
                setTimeout(() => {
                    handleThirdPartyPriceUpdate(event.detail);
                }, 500);
            }
        });
    }

    // 处理第三方价格更新（优化版：等待数据库重新计算趋势）
    async function handleThirdPartyPriceUpdate() {
        try {
            console.log('🔄 处理第三方价格更新...');

            if (!window.databaseManager || !currentTrendData.skuId) {
                console.log('❌ 数据库不可用或SKU无效，跳过处理');
                return;
            }

            // 获取最新价格
            const newPrice = getCurrentBestPrice();
            const skuId = currentTrendData.skuId;

            console.log('📊 价格更新信息:', {
                skuId: skuId,
                newPrice: newPrice,
                oldPrice: currentTrendData.currentPrice
            });

            // 如果价格没有实质性变化，跳过处理
            const priceDiff = Math.abs(newPrice - currentTrendData.currentPrice);
            if (priceDiff < 0.01) {
                console.log('📊 价格变化很小，跳过更新');
                return;
            }

            // 更新本地价格
            currentTrendData.currentPrice = newPrice;

            // 等待数据库重新计算趋势（通过监听数据库更新事件）
            console.log('📊 等待数据库重新计算价格趋势...');

            // 注意：实际的趋势计算会在数据库交互模块的下次更新中完成
            // 这里只是临时更新显示，真正的趋势会通过数据库事件更新

        } catch (error) {
            console.error('❌ 处理第三方价格更新失败:', error);
        }
    }

    // 设置事件监听器
    function setupEventListeners(triggerElement, popover) {
        let showTimeout, hideTimeout;

        triggerElement.addEventListener('mouseenter', () => {
            clearTimeout(hideTimeout);
            showTimeout = setTimeout(() => {
                showPopover(triggerElement, popover);
            }, 300);
        });

        triggerElement.addEventListener('mouseleave', () => {
            clearTimeout(showTimeout);
            hideTimeout = setTimeout(() => {
                hidePopover(popover);
            }, 300);
        });

        popover.addEventListener('mouseenter', () => {
            clearTimeout(hideTimeout);
        });

        popover.addEventListener('mouseleave', () => {
            hideTimeout = setTimeout(() => {
                hidePopover(popover);
            }, 300);
        });
    }

    // 显示弹窗
    function showPopover(triggerElement, popover) {
        const rect = triggerElement.getBoundingClientRect();
        const popoverWidth = 300;

        let left = rect.left + rect.width / 2 - popoverWidth / 2;
        const top = rect.bottom + 10;

        if (left < 10) left = 10;
        if (left + popoverWidth > window.innerWidth - 10) {
            left = window.innerWidth - popoverWidth - 10;
        }

        popover.style.left = left + 'px';
        popover.style.top = top + 'px';
        popover.style.display = 'block';
    }

    // 隐藏弹窗
    function hidePopover(popover) {
        popover.style.display = 'none';
    }

    // 初始化函数
    function initialize() {
        console.log('价格走势模块开始初始化...');

        // 检查页面是否为京东商品页
        const isJDProductPage = (location.hostname.includes('jd.com') || location.hostname.includes('jd.hk')) &&
            (location.pathname.includes('.html') || location.pathname.match(/\/\d+/));

        if (!isJDProductPage) {
            console.log('非京东商品页，跳过初始化');
            return;
        }

        // 立即尝试插入
        if (insertPriceTrendElement()) {
            console.log('立即插入成功');
            return;
        }

        // 重试机制
        let attempts = 0;
        const maxAttempts = 50;

        const tryInsert = () => {
            attempts++;
            console.log(`重试插入 #${attempts}`);

            if (insertPriceTrendElement()) {
                console.log('重试插入成功');
                return;
            }

            if (attempts < maxAttempts) {
                setTimeout(tryInsert, 100);
            } else {
                console.log('达到最大尝试次数，插入失败');
            }
        };

        setTimeout(tryInsert, 100);

        // MutationObserver 监听页面变化
        const observer = new MutationObserver(() => {
            if (!document.getElementById('ltby-price-trend-root')) {
                setTimeout(() => {
                    insertPriceTrendElement();
                }, 100);
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // 初始化时机 - 增加延迟等待第三方数据加载
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(initialize, 2000); // 增加延迟
        });
    } else {
        setTimeout(initialize, 2000); // 增加延迟
    }

    // 调试函数
    window.debugLtbyPriceTrend = function () {
        console.log('🔧 === LTBY 价格走势调试信息 ===');
        console.log('🌐 页面URL:', location.href);
        console.log('📊 当前趋势数据:', currentTrendData);

        // 检查全局对象状态
        console.log('🔍 全局对象检查:');
        console.log('  ↳ ProductExtractor构造函数:', !!window.ProductExtractor);
        console.log('  ↳ productExtractor实例:', !!window.productExtractor);
        console.log('  ↳ DatabaseManager构造函数:', !!window.DatabaseManager);
        console.log('  ↳ databaseManager实例:', !!window.databaseManager);
        console.log('  ↳ thirdPartyExtractor实例:', !!window.thirdPartyExtractor);

        if (window.databaseManager) {
            console.log('📊 DatabaseManager详情:');
            console.log('  ↳ 类型:', typeof window.databaseManager);
            console.log('  ↳ 构造函数名:', window.databaseManager.constructor?.name);
            console.log('  ↳ API地址:', window.databaseManager.apiBaseUrl || 'unknown');
            console.log('  ↳ 方法检查:');
            console.log('    • getProductBySku:', typeof window.databaseManager.getProductBySku === 'function');
            console.log('    • saveProduct:', typeof window.databaseManager.saveProduct === 'function');
            console.log('    • updateProduct:', typeof window.databaseManager.updateProduct === 'function');
            console.log('    • getPriceHistory:', typeof window.databaseManager.getPriceHistory === 'function');
            console.log('    • savePriceHistory:', typeof window.databaseManager.savePriceHistory === 'function');
            console.log('    • testConnection:', typeof window.databaseManager.testConnection === 'function');
        }

        // 测试当前商品查询
        const testSku = "100161240728";
        if (window.databaseManager && typeof window.databaseManager.getProductBySku === 'function') {
            console.log('🧪 执行查询测试...');
            console.log('  ↳ 测试SKU:', testSku);
            window.databaseManager.getProductBySku(testSku).then(result => {
                if (result) {
                    console.log('✅ 查询成功:', result);
                } else {
                    console.log('❌ 查询结果为空，商品不存在');
                }
            }).catch(error => {
                console.error('❌ 查询失败:', error);
            });
        }

        // 测试数据库连接
        if (window.databaseManager && typeof window.databaseManager.testConnection === 'function') {
            console.log('🧪 测试数据库连接...');
            window.databaseManager.testConnection().then(result => {
                console.log('  ↳ 连接测试结果:', result);
            }).catch(error => {
                console.error('  ↳ 连接测试失败:', error);
            });
        }

        console.log('🔄 强制重新初始化...');
        initializeDatabaseData();

        // 调试价格数据结构
        console.log('🔍 === 价格数据结构调试 ===');
        if (window.databaseManager && window.databaseManager.lastCheckResult) {
            const result = window.databaseManager.lastCheckResult;
            console.log('📊 数据库查询结果:', result);
            console.log('📊 价格历史数据:', result.priceHistory);
            console.log('📊 市场价格信息:', result.marketPriceInfo);
            console.log('📊 当前价格:', result.currentPrice);
            console.log('📊 价格趋势:', result.priceTrend);

            if (result.priceHistory && result.priceHistory.length > 0) {
                console.log('📊 历史记录示例:', result.priceHistory[0]);
                console.log('📊 可用字段:', Object.keys(result.priceHistory[0]));

                // 测试价格提取
                const testPrice = extractPriceFromHistoryItem(result.priceHistory[0]);
                console.log('📊 提取的价格:', testPrice);
            }
        } else {
            console.log('❌ 无数据库查询结果');
        }

        // 检查弹窗状态
        const popover = document.getElementById('ltby-price-trend-popover');
        console.log('📋 弹窗状态:', {
            exists: !!popover,
            visible: popover ? popover.style.display !== 'none' : false
        });
    };

    // 全局强制修复价格显示函数
    window.fixPriceTrendDisplay = function () {
        console.log('🔧 强制修复价格走势显示...');

        // 更新弹窗内容
        updatePopoverContent();

        // 重新绘制图表
        setTimeout(() => {
            drawPriceChart();
            drawMarketChart();
        }, 100);

        // 如果有数据库结果，更新趋势显示
        if (window.databaseManager && window.databaseManager.lastCheckResult) {
            const result = window.databaseManager.lastCheckResult;
            const trendText = window.databaseManager.getPriceTrendText(result.priceTrend);
            updateTrendDisplay(trendText);
            console.log('✅ 价格走势显示修复完成');
        } else {
            console.log('❌ 无数据库数据，无法修复');
        }
    };

    // 调试tooltip数据函数
    function debugTooltipData() {
        console.log('🔍 === Tooltip数据调试 ===');

        // 检查当前趋势数据
        console.log('📊 当前趋势数据:', currentTrendData);

        // 检查数据库结果
        if (window.databaseManager && window.databaseManager.lastCheckResult) {
            const result = window.databaseManager.lastCheckResult;
            console.log('📊 数据库结果:', result);
            console.log('📊 价格历史:', result.priceHistory);
            console.log('📊 市场价格信息:', result.marketPriceInfo);

            if (result.priceHistory && result.priceHistory.length > 0) {
                console.log('📊 历史记录示例:', result.priceHistory[0]);
                console.log('📊 历史记录字段:', Object.keys(result.priceHistory[0]));

                // 测试价格提取
                const testPrice = extractPriceFromHistoryItem(result.priceHistory[0]);
                console.log('📊 提取的价格:', testPrice);
            }
        } else {
            console.log('❌ 无数据库结果');
        }

        // 测试tooltip数据生成
        const canvas = document.getElementById('ltby-price-chart');
        if (canvas) {
            console.log('🧪 测试tooltip数据生成...');
            const testTooltipData = getPriceTooltipData(200, 100, 40, 320, 140);
            console.log('🧪 测试tooltip数据:', testTooltipData);
        }

        // 检查Canvas事件绑定
        const priceCanvas = document.getElementById('ltby-price-chart');
        const marketCanvas = document.getElementById('ltby-market-chart');
        console.log('🎯 Canvas状态:', {
            priceCanvas: !!priceCanvas,
            marketCanvas: !!marketCanvas,
            priceCanvasEvents: priceCanvas ? 'mousemove' in priceCanvas : false
        });
    }

    // 暴露到全局
    window.debugTooltipData = debugTooltipData;
    window.currentTrendData = currentTrendData;  // 暴露趋势数据到全局
    window.ltbyPriceTrendModule = {
        currentTrendData,
        extractPriceFromHistoryItem,
        getPriceTooltipData,
        getMarketTooltipData,
        formatTooltipDate
    };

    // 简单测试函数
    window.testTooltip = function () {
        console.log('🧪 === 简单Tooltip测试 ===');
        console.log('📊 当前趋势数据:', window.currentTrendData);
        console.log('📊 数据库结果:', window.databaseManager?.lastCheckResult);

        // 测试tooltip数据生成
        if (window.ltbyPriceTrendModule) {
            const testData = window.ltbyPriceTrendModule.getPriceTooltipData(200, 100, 40, 320, 140);
            console.log('🧪 测试tooltip数据:', testData);
        }

        // 检查Canvas事件
        const canvas = document.getElementById('ltby-price-chart');
        if (canvas) {
            console.log('🎯 Canvas存在，尝试触发鼠标事件...');
            // 模拟鼠标移动事件
            const event = new MouseEvent('mousemove', {
                clientX: canvas.getBoundingClientRect().left + 200,
                clientY: canvas.getBoundingClientRect().top + 100,
                bubbles: true
            });
            canvas.dispatchEvent(event);
        }
    };

})();

console.log('价格走势模块文件加载完成');

// 简单的tooltip测试函数
window.testTooltipSimple = function () {
    console.log('🧪 === 简单Tooltip测试 ===');

    const canvas = document.getElementById('ltby-price-chart');
    if (canvas) {
        console.log('✅ Canvas存在');
        console.log('💡 请将鼠标移动到绿色直线上，应该显示价格信息');

        // 检查数据
        if (window.databaseManager?.lastCheckResult) {
            const result = window.databaseManager.lastCheckResult;
            console.log('📊 当前价格:', result.currentPrice);
            console.log('📊 价格趋势:', result.priceTrend);
            console.log('📊 完整数据结构:', result);

            // 检查价格说明数据的所有可能位置
            console.log('📊 数据库结果的价格说明字段:', {
                'product.price_desc': result.product?.price_desc,
                'product.price_method': result.product?.price_method,
                'root.price_desc': result.price_desc,
                'root.price_method': result.price_method,
                'data.price_desc': result.data?.price_desc,
                'data.price_method': result.data?.price_method
            });

            // 检查历史数据中的价格说明
            if (result.priceHistory && result.priceHistory.length > 0) {
                const latestHistory = result.priceHistory[result.priceHistory.length - 1];
                console.log('📊 最新历史记录的价格说明:', {
                    price_method: latestHistory.price_method,
                    price_desc: latestHistory.price_desc,
                    allFields: Object.keys(latestHistory)
                });
            }
        }
    } else {
        console.log('❌ Canvas不存在，请先打开价格走势弹窗');
    }
};

// 全局tooltip修复和测试函数（在IIFE外部）
window.fixTooltipIssue = function () {
    console.log('🔧 === 修复Tooltip问题 ===');

    // 检查弹窗是否存在
    const popover = document.getElementById('ltby-price-popover');
    if (!popover) {
        console.log('❌ 弹窗不存在');
        return;
    }

    // 检查Canvas是否存在
    const priceCanvas = document.getElementById('ltby-price-chart');
    const marketCanvas = document.getElementById('ltby-market-chart');

    console.log('📊 Canvas状态:', {
        priceCanvas: !!priceCanvas,
        marketCanvas: !!marketCanvas
    });

    if (!priceCanvas) {
        console.log('❌ 价格Canvas不存在');
        return;
    }

    // 检查数据库数据
    const dbResult = window.databaseManager?.lastCheckResult;
    console.log('📊 数据库结果:', dbResult);

    if (!dbResult) {
        console.log('❌ 无数据库数据');
        return;
    }

    // 手动绑定Canvas事件
    console.log('🔧 手动绑定Canvas事件...');

    // 移除现有事件监听器
    const newCanvas = priceCanvas.cloneNode(true);
    priceCanvas.parentNode.replaceChild(newCanvas, priceCanvas);

    // 添加新的事件监听器
    newCanvas.addEventListener('mousemove', function (event) {
        console.log('🖱️ 鼠标移动事件触发');

        const rect = newCanvas.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;

        console.log('🖱️ 鼠标位置:', { x, y });

        // 检查是否在图表区域内
        const padding = 40;
        const width = newCanvas.width;
        const height = newCanvas.height;
        const chartWidth = width - padding * 2;
        const chartHeight = height - padding * 2;

        if (x < padding || x > padding + chartWidth ||
            y < padding || y > padding + chartHeight) {
            console.log('🖱️ 鼠标不在图表区域内');
            hideTooltip();
            return;
        }

        // 生成tooltip数据
        const tooltipData = generateTooltipData(dbResult);
        console.log('🔍 生成的tooltip数据:', tooltipData);

        if (tooltipData) {
            showTooltip(event.clientX, event.clientY, tooltipData);
        }
    });

    newCanvas.addEventListener('mouseleave', function () {
        console.log('🖱️ 鼠标离开Canvas');
        hideTooltip();
    });

    console.log('✅ Canvas事件绑定完成');

    // 生成tooltip数据的函数
    function generateTooltipData(dbResult) {
        const currentPrice = dbResult.currentPrice || 0;
        const trend = dbResult.priceTrend || 'new';
        const priceHistory = dbResult.priceHistory || [];

        // 转换趋势文本
        const trendMap = {
            'new': '新收',
            'stable': '平稳',
            'up': '上涨',
            'down': '下降',
            'lowest': '最低'
        };
        const trendText = trendMap[trend] || '新收';

        const today = new Date();
        const dateStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')} 00:00:00`;

        let description = '新收录商品';
        if (trendText === '平稳') {
            description = '价格平稳';
        } else if (priceHistory.length > 0) {
            const lastItem = priceHistory[priceHistory.length - 1];
            description = lastItem.price_method || lastItem.price_desc || '活动价';
        }

        return {
            date: dateStr,
            price: currentPrice.toFixed(2),
            description: description
        };
    }

    // 显示tooltip的函数
    function showTooltip(clientX, clientY, data) {
        hideTooltip(); // 先隐藏现有的

        const tooltip = document.createElement('div');
        tooltip.className = 'ltby-chart-tooltip-fixed';
        tooltip.style.cssText = `
            position: fixed;
            background: white;
            color: #333;
            padding: 10px 12px;
            border-radius: 6px;
            font-size: 12px;
            z-index: 10001;
            pointer-events: none;
            white-space: nowrap;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            border: 1px solid #e0e0e0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        `;

        tooltip.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 6px; color: #333;">${data.date}</div>
            <div style="color: #1890ff; font-weight: bold; margin-bottom: 2px;">● 售价: ¥${data.price}</div>
            <div style="color: #666; font-size: 11px;">${data.description}</div>
        `;

        document.body.appendChild(tooltip);

        // 调整位置
        const tooltipRect = tooltip.getBoundingClientRect();
        let left = clientX + 10;
        let top = clientY - tooltipRect.height - 10;

        if (left + tooltipRect.width > window.innerWidth) {
            left = clientX - tooltipRect.width - 10;
        }
        if (top < 0) {
            top = clientY + 10;
        }

        tooltip.style.left = left + 'px';
        tooltip.style.top = top + 'px';

        console.log('✅ Tooltip显示成功');
    }

    // 隐藏tooltip的函数
    function hideTooltip() {
        const existingTooltips = document.querySelectorAll('.ltby-chart-tooltip-fixed');
        existingTooltips.forEach(tip => {
            if (tip.parentNode) {
                tip.parentNode.removeChild(tip);
            }
        });
    }
};

// 从URL获取SKU的辅助函数
function getCurrentSkuFromUrl() {
    const url = window.location.href;
    const match = url.match(/\/(\d+)\.html/);
    return match ? match[1] : null;
}
