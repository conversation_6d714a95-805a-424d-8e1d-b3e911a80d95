/**
 * 市场价格管理模块
 * 负责商品市场价格的读取、更新和显示
 */

// console.log('市场价格模块开始加载...');

class MarketPriceManager {
    constructor() {
        this.apiBaseUrl = 'https://zzz7.top/api/products';
        this.currentSkuId = null;
        this.currentMarketPrice = null;
        this.priceInput = null;
        this.updateButton = null;
        this.priceDisplay = null;
        this.updateTimeElement = null;
        this.currentUpdateTime = null;
        this.isInitialized = false;
        this.boundHandleDatabaseUpdate = this.handleDatabaseUpdate.bind(this);

        this.marketPriceSource = null;
        this.marketPriceSourceSku = null;
        this.marketPriceSourceTitle = null; // Added to store source product title
        this.marketPriceSimilarity = null;

        // console.log('MarketPriceManager 初始化完成');
    }

    async initialize() {
        try {
            // console.log('市场价格模块开始初始化...');

            // 等待统一元素提取器准备就绪
            if (!window.unifiedExtractor) {
                console.error('统一元素提取器不可用');
                return;
            }

            // 获取SKU（统一接口）
            this.currentSkuId = window.unifiedExtractor.getSkuId();

            if (!this.currentSkuId) {
                console.error('无法获取SKU，可能不是有效的商品页面');
                return;
            }

            // console.log('提取到商品SKU:', this.currentSkuId);

            // 从数据库加载市场价格 (initial load)
            await this.loadMarketPriceFromDatabase();

            // 绑定现有的市场价格元素
            await this.bindMarketPriceElements();

            // Listen for updates from DatabaseManager
            document.addEventListener('databaseFullUpdateComplete', this.boundHandleDatabaseUpdate);

            this.isInitialized = true;
            // console.log('MarketPriceManager: isInitialized SET TO:', this.isInitialized); // 新增日志
            // console.log('市场价格模块初始化完成，当前市场价格:', this.currentMarketPrice || '无数据');

            // Dispatch a custom event to notify that MarketPriceManager is initialized
            try {
                document.dispatchEvent(new CustomEvent('marketPriceManagerInitialized', {
                    detail: {
                        sku: this.currentSkuId,
                        marketPrice: this.currentMarketPrice,
                        timestamp: new Date().toISOString()
                    }
                }));
                // console.log('MarketPriceManager.prototype.initialize: Dispatched marketPriceManagerInitialized event.');
            } catch (e) {
                console.error('MarketPriceManager.prototype.initialize: Error dispatching event', e);
            }

        } catch (error) {
            console.error('市场价格模块初始化失败:', error);
            this.isInitialized = false; // Ensure flag is false on error
        }
    }

    /**
     * Handles updates from DatabaseManager after /check-page call
     */
    handleDatabaseUpdate(event) {
        if (!event.detail || !event.detail.product || event.detail.product.sku !== this.currentSkuId) {
            return;
        }

        const marketInfo = event.detail.marketPriceInfo;
        const productData = event.detail.product;

        // console.log('MarketPriceManager received databaseFullUpdateComplete event. SKU:', productData.sku, 'MarketInfo:', marketInfo);

        if (marketInfo && typeof marketInfo.market_price !== 'undefined' && marketInfo.market_price !== null) {
            this.currentMarketPrice = marketInfo.market_price;
            this.currentUpdateTime = marketInfo.source_market_price_updated_at || productData.market_price_updated_at || marketInfo.updated_at;

            if (marketInfo.source === 'similar_product') {
                this.marketPriceSource = 'similarity';
                this.marketPriceSourceSku = marketInfo.source_sku; // Assumes server sends this
                this.marketPriceSourceTitle = marketInfo.source_title; // Assumes server sends this

                let rawSimilarity = marketInfo.similarity;
                if (typeof rawSimilarity === 'string') {
                    if (rawSimilarity.includes('%')) {
                        rawSimilarity = parseFloat(rawSimilarity.replace('%', '')) / 100;
                    } else {
                        rawSimilarity = parseFloat(rawSimilarity);
                    }
                }
                this.marketPriceSimilarity = !isNaN(rawSimilarity) && typeof rawSimilarity === 'number' ? rawSimilarity : null;

            } else if (marketInfo.source === 'self') {
                this.marketPriceSource = 'self';
                this.marketPriceSourceSku = null;
                this.marketPriceSourceTitle = null;
                this.marketPriceSimilarity = null;
            } else {
                this.marketPriceSource = 'self';
                this.marketPriceSourceSku = null;
                this.marketPriceSourceTitle = null;
                this.marketPriceSimilarity = null;
            }
        } else {
            this.currentMarketPrice = null;
            this.currentUpdateTime = null;
            this.marketPriceSource = null;
            this.marketPriceSourceSku = null;
            this.marketPriceSourceTitle = null;
            this.marketPriceSimilarity = null;
            console.log('MarketPriceManager: Event indicates no market price. Clearing display.');
        }

        this.updatePriceDisplay();
        this.updateTimeDisplay();
    }

    /**
     * 从数据库加载市场价格
     */
    async loadMarketPriceFromDatabase() {
        try {
            console.log('从数据库加载市场价格 (initial), SKU:', this.currentSkuId);
            const productInfo = await window.databaseManager.getProductBySku(this.currentSkuId);

            if (productInfo && productInfo.success && productInfo.data && productInfo.data.product) {
                const dbProduct = productInfo.data.product;
                // This is the product's own stored market price.
                // It might be overridden by the databaseFullUpdateComplete event if a similarity match is found
                // or if the product's own price is updated more recently via check-page.
                if (dbProduct.market_price !== null && typeof dbProduct.market_price !== 'undefined') {
                    this.currentMarketPrice = dbProduct.market_price;
                    this.currentUpdateTime = dbProduct.market_price_updated_at;
                    this.marketPriceSource = 'self';
                    this.marketPriceSourceSku = null;
                    this.marketPriceSourceTitle = null; // Ensure cleared for self source
                    this.marketPriceSimilarity = null;
                } else {
                    // No direct market price stored for this product initially
                    this.currentMarketPrice = null;
                    this.currentUpdateTime = null;
                    this.marketPriceSource = null;
                    this.marketPriceSourceSku = null;
                    this.marketPriceSourceTitle = null;
                    this.marketPriceSimilarity = null;
                }
                console.log('市场价格(直接加载)成功:', this.currentMarketPrice || '无数据', 'Source:', this.marketPriceSource);
            } else {
                this.currentMarketPrice = null;
                this.currentUpdateTime = null;
                this.marketPriceSource = null;
                this.marketPriceSourceSku = null;
                this.marketPriceSourceTitle = null;
                this.marketPriceSimilarity = null;
                console.log('商品不存在于数据库或无市场价格数据(直接加载)');
            }
        } catch (error) {
            console.error('加载市场价格(直接加载)失败:', error);
            this.currentMarketPrice = null;
            this.currentUpdateTime = null;
            this.marketPriceSource = null;
            this.marketPriceSourceSku = null;
            this.marketPriceSourceTitle = null;
            this.marketPriceSimilarity = null;
        }
        // Update display after initial load attempt
        this.updatePriceDisplay();
        this.updateTimeDisplay();
    }

    /**
     * 绑定现有的市场价格元素
     */
    async bindMarketPriceElements() {
        try {
            console.log('开始绑定市场价格元素...');

            // 等待元素出现（最多等待3秒）
            let attempts = 0;
            const maxAttempts = 30;

            while (attempts < maxAttempts) {
                this.priceInput = document.getElementById('new-market-price');
                this.updateButton = document.getElementById('update-market-price-btn');
                this.priceDisplay = document.querySelector('[data-id="market-price"]');
                this.updateTimeElement = document.getElementById('update-time');

                if (this.priceInput && this.updateButton && this.priceDisplay && this.updateTimeElement) {
                    break;
                }
                await new Promise(resolve => setTimeout(resolve, 100));
                attempts++;
            }

            if (!this.priceInput || !this.updateButton || !this.priceDisplay || !this.updateTimeElement) {
                console.error('未找到市场价格相关元素');
                console.log('查找结果:', {
                    priceInput: !!this.priceInput,
                    updateButton: !!this.updateButton,
                    priceDisplay: !!this.priceDisplay,
                    updateTimeElement: !!this.updateTimeElement
                });
                return;
            }

            console.log('找到市场价格元素:', {
                priceInput: this.priceInput,
                updateButton: this.updateButton,
                priceDisplay: this.priceDisplay,
                updateTimeElement: this.updateTimeElement
            });

            // 更新显示
            this.updatePriceDisplay();
            this.updateTimeDisplay();

            // 绑定更新按钮事件
            this.updateButton.addEventListener('click', async (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('市场价格更新按钮被点击');
                await this.updateMarketPrice();
            });

            // 绑定输入框回车事件
            this.priceInput.addEventListener('keypress', async (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    console.log('市场价格输入框回车');
                    await this.updateMarketPrice();
                }
            });

            console.log('市场价格元素绑定成功');

        } catch (error) {
            console.error('绑定市场价格元素失败:', error);
        }
    }

    /**
     * 更新价格显示
     */
    updatePriceDisplay() {
        if (!this.priceDisplay) return;

        let tooltipText = '暂无市场价数据';
        let displayHtml;

        if (this.currentMarketPrice !== null && typeof this.currentMarketPrice !== 'undefined') {
            displayHtml = `<span style="color: #ff4d4f; font-weight: bold;">市价:</span><span style="color: #1890ff; font-weight: bold;">¥${parseFloat(this.currentMarketPrice).toFixed(2)}</span>`;

            let formattedUpdateDate = 'N/A';
            if (this.currentUpdateTime) {
                try {
                    const date = new Date(this.currentUpdateTime);
                    const year = date.getFullYear().toString().slice(-2);
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    formattedUpdateDate = `${year}-${month}-${day}`;
                } catch (e) {
                    formattedUpdateDate = '日期无效';
                }
            }

            if (this.marketPriceSource === 'self') {
                tooltipText = `市价来源: 本商品\n更新日期: ${formattedUpdateDate}`;
            } else if (this.marketPriceSource === 'similarity') {
                const similarityPercentage = this.marketPriceSimilarity ? (this.marketPriceSimilarity * 100).toFixed(1) : 'N/A';
                const sourceSkuDisplay = this.marketPriceSourceSku ? this.marketPriceSourceSku : 'N/A';
                const sourceTitleDisplay = this.marketPriceSourceTitle ? this.marketPriceSourceTitle : 'N/A';
                tooltipText = `市价来源: 相似商品\nSKU: ${sourceSkuDisplay}\n标题: ${sourceTitleDisplay}\n相似度: ${similarityPercentage}%\n更新日期: ${formattedUpdateDate}`;
            } else {
                tooltipText = `市场价格: ¥${parseFloat(this.currentMarketPrice).toFixed(2)}\n更新日期: ${formattedUpdateDate}`;
            }
        } else {
            displayHtml = `<span style="color: #ff4d4f; font-weight: bold;">市价:</span><span style="color: #999;">无数据</span>`;
        }

        this.priceDisplay.innerHTML = displayHtml;
        this.priceDisplay.title = tooltipText;

        // Add click functionality for similar product source
        if (this.marketPriceSource === 'similarity' && this.marketPriceSourceSku) {
            this.priceDisplay.style.cursor = 'pointer';
            this.priceDisplay.onclick = () => {
                const url = `https://item.jd.com/${this.marketPriceSourceSku}.html`;
                window.open(url, '_blank');
                console.log(`MarketPriceManager: Clicked to open similar product: ${url}`);
            };
        } else {
            this.priceDisplay.style.cursor = 'default';
            this.priceDisplay.onclick = null; // Remove listener if not applicable
        }

        console.log('价格显示已更新:', this.priceDisplay.textContent, 'Tooltip:', tooltipText);
    }

    /**
     * 更新时间显示
     */
    updateTimeDisplay() {
        if (!this.updateTimeElement) return;

        if (this.currentUpdateTime) {
            const updateDate = new Date(this.currentUpdateTime);
            // 使用格式：25-06-01 (年-月-日)
            const year = updateDate.getFullYear().toString().slice(-2); // 取年份后两位
            const month = String(updateDate.getMonth() + 1).padStart(2, '0');
            const day = String(updateDate.getDate()).padStart(2, '0');
            const formattedTime = `${year}-${month}-${day}`;

            this.updateTimeElement.textContent = formattedTime;
            this.updateTimeElement.style.color = '#1890ff'; // 蓝色
            this.updateTimeElement.style.fontWeight = 'normal'; // 不加粗
            this.updateTimeElement.style.fontSize = '12px'; // 稍小的字体
        } else {
            this.updateTimeElement.textContent = '无数据';
            this.updateTimeElement.style.color = '#999';
            this.updateTimeElement.style.fontWeight = 'normal';
            this.updateTimeElement.style.fontSize = '12px';
        }

        console.log('更新时间显示已更新:', this.updateTimeElement.textContent);
    }

    /**
     * 更新市场价格到数据库
     */
    async updateMarketPrice() {
        try {
            if (!this.priceInput) {
                console.error('价格输入框不存在');
                return;
            }

            const inputPrice = this.priceInput.value.trim();
            if (!inputPrice) {
                this.showMessage('请输入价格', 'error');
                return;
            }

            // 验证价格格式
            const price = parseFloat(inputPrice);
            if (isNaN(price) || price <= 0) {
                this.showMessage('请输入有效的价格', 'error');
                return;
            }

            console.log('准备更新市场价格:', price);

            // 显示加载状态
            this.setLoadingState(true);

            // 调用API更新市场价格
            const response = await fetch(`${this.apiBaseUrl}/${this.currentSkuId}/market-price`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    market_price: price
                })
            });

            if (response.ok) {
                const result = await response.json();

                if (result.success) {
                    this.currentMarketPrice = result.data.market_price || price;
                    this.currentUpdateTime = result.data.updated_at || new Date().toISOString();

                    this.marketPriceSource = 'self';
                    this.marketPriceSourceSku = null;
                    this.marketPriceSourceTitle = null; // Ensure cleared on manual update
                    this.marketPriceSimilarity = null;

                    this.updatePriceDisplay();
                    this.updateTimeDisplay();

                    // 清空输入框
                    this.priceInput.value = '';

                    // 显示成功提示
                    this.showMessage('市场价格更新成功', 'success');

                    console.log('市场价格更新成功:', this.currentMarketPrice);
                    console.log('更新时间:', this.currentUpdateTime);
                    console.log('服务器返回数据:', result.data);
                } else {
                    throw new Error(result.error || '更新失败');
                }
            } else {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

        } catch (error) {
            console.error('更新市场价格失败:', error);
            this.showMessage('更新失败，请重试', 'error');
        } finally {
            this.setLoadingState(false);
        }
    }

    /**
     * 设置加载状态
     */
    setLoadingState(loading) {
        if (this.updateButton) {
            this.updateButton.disabled = loading;
            this.updateButton.textContent = loading ? '更新中...' : '更新';
            this.updateButton.style.cursor = loading ? 'not-allowed' : 'pointer';
        }
    }

    /**
     * 显示消息提示
     */
    showMessage(message, type = 'info') {
        // 创建消息元素
        const messageEl = document.createElement('div');
        messageEl.textContent = message;
        messageEl.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            padding: 12px 20px;
            border-radius: 4px;
            color: white;
            font-size: 14px;
            font-weight: bold;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
        `;

        // 根据类型设置颜色
        switch (type) {
            case 'success':
                messageEl.style.backgroundColor = '#52c41a';
                break;
            case 'error':
                messageEl.style.backgroundColor = '#ff4d4f';
                break;
            default:
                messageEl.style.backgroundColor = '#1890ff';
        }

        document.body.appendChild(messageEl);

        // 3秒后自动移除
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.parentNode.removeChild(messageEl);
            }
        }, 3000);
    }

    /**
     * 获取当前状态
     */
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            currentSkuId: this.currentSkuId,
            currentMarketPrice: this.currentMarketPrice,
            currentUpdateTime: this.currentUpdateTime,
            marketPriceSource: this.marketPriceSource,
            marketPriceSourceSku: this.marketPriceSourceSku,
            marketPriceSourceTitle: this.marketPriceSourceTitle, // Added for debugging
            marketPriceSimilarity: this.marketPriceSimilarity,
            hasElements: !!(this.priceInput && this.updateButton && this.priceDisplay && this.updateTimeElement)
        };
    }

    // Add a cleanup method if needed, e.g., when the content script is destroyed
    destroy() {
        document.removeEventListener('databaseFullUpdateComplete', this.boundHandleDatabaseUpdate);
        console.log('MarketPriceManager event listener removed.');
    }
}

// 创建全局实例
window.marketPriceManager = new MarketPriceManager();

// 兼容性接口（如果需要）
window.MarketPriceManager = {
    init: (skuId) => {
        console.log('MarketPriceManager.init 被调用，SKU:', skuId);
        return window.marketPriceManager.initialize();
    },
    updatePrice: (price) => {
        console.log('MarketPriceManager.updatePrice 被调用，价格:', price);
        if (window.marketPriceManager.priceInput) {
            window.marketPriceManager.priceInput.value = price;
            return window.marketPriceManager.updateMarketPrice();
        }
        return Promise.resolve(false);
    },
    getStatus: () => {
        return window.marketPriceManager.getStatus();
    }
};

// 自动初始化
(function () {
    console.log('市场价格模块自动初始化开始...');

    function initializeWhenReady() {
        if (!location.href.includes('item.jd.com')) {
            console.log('不是京东商品页面，跳过市场价格模块初始化');
            return;
        }

        if (!window.databaseManager || !window.unifiedExtractor) {
            console.log('Dependencies (DatabaseManager or UnifiedExtractor) not ready, retrying market price init shortly...');
            setTimeout(initializeWhenReady, 500);
            return;
        }

        // Check if unifiedExtractor has SKU ID, if not, it might still be initializing
        if (!window.unifiedExtractor.getSkuId()) {
            console.log('UnifiedExtractor not fully ready (no SKU ID), retrying market price init shortly...');
            setTimeout(initializeWhenReady, 500);
            return;
        }

        // All dependencies seem ready, proceed with initialization.
        // The setTimeout for 1500ms is to allow other modules (like DatabaseManager which makes API calls)
        // to potentially complete their initial work. MarketPriceManager itself will also do an initial load,
        // and then listen for the comprehensive update event.
        setTimeout(async () => {
            try {
                if (!window.marketPriceManager.isInitialized) { // Initialize only once
                    await window.marketPriceManager.initialize();
                }
            } catch (error) {
                console.error('市场价格模块自动初始化失败:', error);
                // Optional: Add retry logic here if truly necessary, but initialize() itself has error handling.
            }
        }, 500); // Reduced delay, main data comes from event.
    }

    // 等待页面加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeWhenReady);
    } else {
        initializeWhenReady();
    }
})();

// 添加全局调试函数
window.debugMarketPrice = function () {
    console.log('🔍 调试市场价格模块...');
    if (window.marketPriceManager) {
        console.log('📊 当前管理器状态:', window.marketPriceManager.getStatus());

        // 测试按钮点击
        if (window.marketPriceManager.updateButton) {
            console.log('🔘 测试按钮点击...');
            window.marketPriceManager.updateButton.click();
        } else {
            console.log('❌ 更新按钮不存在');
        }
    } else {
        console.log('❌ marketPriceManager 未初始化');
    }
};

console.log('市场价格模块加载完成');
console.log('💡 调试命令: window.debugMarketPrice()');
