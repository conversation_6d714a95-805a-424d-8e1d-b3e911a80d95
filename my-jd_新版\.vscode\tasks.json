{"version": "2.0.0", "tasks": [{"label": "Git: 快速提交", "type": "shell", "command": "git", "args": ["add", ".", "&&", "git", "commit", "-m", "快速提交: ${input:commitMessage}"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Git: 查看状态", "type": "shell", "command": "git", "args": ["status"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Git: 查看日志", "type": "shell", "command": "git", "args": ["log", "--oneline", "--graph", "-10"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "项目: 创建完整备份", "type": "shell", "command": "powershell", "args": ["-Command", "$date = Get-Date -Format 'yyyyMMdd_HHmmss'; $backupPath = \"../备份_$date\"; Copy-Item -Path . -Destination $backupPath -Recurse -Force; Write-Host \"备份创建完成: $backupPath\""], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "插件: 重新加载扩展", "type": "shell", "command": "powershell", "args": ["-Command", "Write-Host '请在Chrome中手动重新加载扩展：'; Write-Host '1. 打开 chrome://extensions/'; Write-Host '2. 找到 LTBY京东助手'; Write-Host '3. 点击刷新按钮'; Start-Process 'chrome://extensions/'"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "设置: 切换自动保存模式", "type": "shell", "command": "powershell", "args": ["-Command", "$modes = @('off', 'afterDelay', 'onFocusChange', 'onWindowChange'); $current = (Get-Content '.vscode/settings.json' | ConvertFrom-Json).'files.autoSave'; Write-Host \"当前模式: $current\"; Write-Host '可选模式:'; $modes | ForEach-Object { Write-Host \"  $_\" }; $choice = Read-Host '请选择新模式'; if ($modes -contains $choice) { $settings = Get-Content '.vscode/settings.json' | ConvertFrom-Json; $settings.'files.autoSave' = $choice; $settings | ConvertTo-Json -Depth 10 | Set-Content '.vscode/settings.json'; Write-Host \"已切换到: $choice\" } else { Write-Host '无效选择' }"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "shared"}}, {"label": "设置: 显示当前配置", "type": "shell", "command": "powershell", "args": ["-Command", "$settings = Get-Content '.vscode/settings.json' | ConvertFrom-Json; Write-Host '当前VSCode配置:' -ForegroundColor Green; Write-Host \"自动保存模式: $($settings.'files.autoSave')\" -ForegroundColor Yellow; Write-Host \"保存延迟: $($settings.'files.autoSaveDelay')ms\" -ForegroundColor Yellow; Write-Host \"保存时格式化: $($settings.'editor.formatOnSave')\" -ForegroundColor Yellow; Write-Host \"去除空白: $($settings.'files.trimTrailingWhitespace')\" -ForegroundColor Yellow"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}], "inputs": [{"id": "commitMessage", "description": "输入提交信息", "default": "更新代码", "type": "promptString"}]}