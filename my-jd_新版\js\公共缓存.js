const LinkConverter = {
    CACHE_KEY: 'convertedLinks',
    currentPageLink: null,
    initPromise: null,

    async init(url) {
        if (this.initPromise) {
            return this.initPromise;
        }

        // console.log('[LinkConverter] 开始初始化转链');
        this.initPromise = this.convertLink(url);
        return this.initPromise;
    },

    async getCachedLink(url) {
        return new Promise(resolve => {
            chrome.storage.local.get([this.CACHE_KEY], result => {
                const links = result[this.CACHE_KEY] || {};
                const cachedData = links[url];
                if (cachedData && Date.now() - cachedData.timestamp < 30 * 24 * 60 * 60 * 1000) {
                    // console.log('[LinkConverter] 使用缓存的转链结果:', cachedData.link);
                    resolve(cachedData.link);
                } else {
                    // console.log('[LinkConverter] 没有找到缓存或缓存已过期');
                    resolve(null);
                }
            });
        });
    },

    async cacheLink(url, convertedUrl) {
        return new Promise(resolve => {
            chrome.storage.local.get([this.CACHE_KEY], result => {
                const links = result[this.CACHE_KEY] || {};
                links[url] = {
                    link: convertedUrl,
                    timestamp: Date.now()
                };
                chrome.storage.local.set({ [this.CACHE_KEY]: links }, resolve);
            });
        });
    },

    async clearCache(url) {
        return new Promise(resolve => {
            chrome.storage.local.get([this.CACHE_KEY], result => {
                const links = result[this.CACHE_KEY] || {};
                delete links[url];
                chrome.storage.local.set({ [this.CACHE_KEY]: links }, resolve);
            });
        });
    },

    getCurrentLink() {
        // 返回当前页面的转链结果，如果没有则返回null
        return this.currentPageLink;
    },

    async convertLink(url) {
        // console.log('[LinkConverter] 开始处理URL:', url);
        // console.log('[LinkConverter] 当前缓存状态:', this.currentPageLink);

        // 如果已经有当前页面的结果，直接返回
        if (this.currentPageLink) {
            // console.log('[LinkConverter] 返回当前页面结果:', this.currentPageLink);
            return this.currentPageLink;
        }

        const cachedLink = await this.getCachedLink(url);
        if (cachedLink) {
            // 检查缓存的是否是原始URL（说明之前转链失败）
            if (cachedLink === url) {
                // console.log('[LinkConverter] 缓存的是原始URL，重新尝试转链');
                // 清除这个无效缓存，重新转链
                await this.clearCache(url);
            } else {
                // console.log('[LinkConverter] 使用storage缓存结果:', cachedLink);
                this.currentPageLink = cachedLink;
                return cachedLink;
            }
        }

        try {
            // console.log('[LinkConverter] 请求新的转链结果');
            const response = await new Promise((resolve, reject) => {
                chrome.runtime.sendMessage(
                    { type: 'convertLink', url },
                    response => {
                        // console.log('[LinkConverter] API原始返回结果:', JSON.stringify(response, null, 2));
                        if (response && response.success) {
                            resolve(response.data);
                        } else {
                            reject(new Error(response?.error || '转链失败'));
                        }
                    }
                );
            });

            // console.log('[LinkConverter] API解析前的response:', JSON.stringify(response, null, 2));            // 修改API响应解析逻辑，适配新的转链API格式
            // 优先检查新的京东转链API格式（return字段）
            if (response.return !== undefined) {
                // console.log('[LinkConverter] 检测到新京东转链API格式，return值:', response.return);

                // 如果return="0"或msg="ok~"，说明转链成功，进入详细解析
                if ((response.return === "0" || response.msg === "ok~") && response.result) {
                    // 优先从result.link获取，如果没有则从result.data.shortURL获取
                    const convertedUrl = response.result.link || response.result.data?.shortURL;
                    if (convertedUrl) {
                        // console.log('[LinkConverter] 新API转链成功:', convertedUrl);
                        await this.cacheLink(url, convertedUrl);
                        this.currentPageLink = convertedUrl;
                        return convertedUrl;
                    }
                }
                
                // console.log('[LinkConverter] 新API转链失败或返回数据格式不符', response);
                // 即使失败，也缓存原始URL，避免短时间内重复请求失败的链接
                await this.cacheLink(url, url);
                this.currentPageLink = url;
                return url;
            }

            // 兼容旧的联盟转链API格式
            if (response.jingfen_jingdong_union_open_promotion_bysubunionid_get_response) {
                // console.log('[LinkConverter] 检测到旧联盟API格式');
                const result = response.jingfen_jingdong_union_open_promotion_bysubunionid_get_response.getResult;
                const parsedResult = JSON.parse(result);
                if (parsedResult.code === 200 && parsedResult.data.shortURL) {
                    const convertedUrl = parsedResult.data.shortURL;
                    // console.log('[LinkConverter] 旧API转链成功:', convertedUrl);
                    await this.cacheLink(url, convertedUrl);
                    this.currentPageLink = convertedUrl;
                    return convertedUrl;
                } else {
                    // console.log('[LinkConverter] 旧API转链失败', parsedResult);
                }
            }

            // console.log('[LinkConverter] 所有API格式均不匹配或转链失败');
            // 如果所有尝试都失败了，缓存原始URL，防止重复请求
            await this.cacheLink(url, url);
            this.currentPageLink = url;
            return url;

        } catch (error) {
            console.error('[LinkConverter] 转链过程中发生严重错误:', error);
            // 发生异常时，同样缓存原始URL
            await this.cacheLink(url, url);
            this.currentPageLink = url;
            return url;
        }
    }
};

// 将LinkConverter暴露到全局window对象
if (typeof window !== 'undefined') {
    window.LinkConverter = LinkConverter;
}

const PriceHistoryCache = {
    CACHE_KEY_PREFIX: 'priceHistory_',
    CACHE_DURATION: 7 * 24 * 60 * 60 * 1000, // 7 days

    async get(productId) {
        return new Promise(resolve => {
            const cacheKey = this.CACHE_KEY_PREFIX + productId;
            chrome.storage.local.get([cacheKey], result => {
                const cachedData = result[cacheKey];
                if (cachedData && Date.now() - cachedData.timestamp < this.CACHE_DURATION) {
                    // console.log(`[PriceHistoryCache] Found cached data for ${productId}`);
                    resolve(cachedData.data);
                } else {
                    // console.log(`[PriceHistoryCache] No valid cache for ${productId}`);
                    resolve(null);
                }
            });
        });
    },

    async set(productId, data) {
        return new Promise(resolve => {
            const cacheKey = this.CACHE_KEY_PREFIX + productId;
            const cacheData = {
                data: data,
                timestamp: Date.now()
            };
            // console.log(`[PriceHistoryCache] Caching data for ${productId}`);
            chrome.storage.local.set({ [cacheKey]: cacheData }, resolve);
        });
    }
};

const MarketPriceCache = {
    CACHE_KEY_PREFIX: 'marketPrice_',
    CACHE_DURATION: 3 * 24 * 60 * 60 * 1000, // 3 days

    async get(productName) {
        return new Promise(resolve => {
            const cacheKey = this.CACHE_KEY_PREFIX + productName;
            chrome.storage.local.get([cacheKey], result => {
                const cachedData = result[cacheKey];
                if (cachedData && Date.now() - cachedData.timestamp < this.CACHE_DURATION) {
                    // console.log(`[MarketPriceCache] Found cached data for ${productName}`);
                    resolve(cachedData.data);
                } else {
                    // console.log(`[MarketPriceCache] No valid cache for ${productName}`);
                    resolve(null);
                }
            });
        });
    },

    async set(productName, data) {
        return new Promise(resolve => {
            const cacheKey = this.CACHE_KEY_PREFIX + productName;
            const cacheData = {
                data: data,
                timestamp: Date.now()
            };
            // console.log(`[MarketPriceCache] Caching data for ${productName}`);
            chrome.storage.local.set({ [cacheKey]: cacheData }, resolve);
        });
    }
};
