// 自动领取优惠券模块 - 在商品页自动领取所有可用优惠券
if (window.AutoCouponInitialized) {
    // console.warn('自动领取优惠券模块已初始化，跳过重复初始化。');
} else {
    window.AutoCouponInitialized = true;
    (function () {
        const AutoCoupon = {
            // 配置参数
            config: {
                enabled: true, // 是否启用自动领取功能
                clickDelay: 300, // 每次点击之间的延迟(毫秒)
                maxRetryAttempts: 3, // 最大重试次数
                waitForCouponLoad: 1000, // 等待优惠券区域加载的时间(毫秒)
                debugMode: true, // 调试模式 - 开启以查看操作过程
            },

            // 内部状态
            state: {
                isRunning: false,
                processedButtons: new Set(), // 已处理的按钮，避免重复点击
                totalClaimed: 0, // 已领取数量
                hasSlider: false, // 是否遇到滑块验证
            },            // 优惠券相关选择器（简化版）
            selectors: {
                // 优惠券触发器
                trigger: '.more-btn',
                fallbackTrigger: '#summary-quan',
                // 优惠券面板
                panel: '.coupons-list-box'
            },            // 初始化
            init() {
                if (!this.isItemPage()) {
                    return;
                }

                // console.log('自动领取优惠券模块初始化...');
                // console.log('按 Ctrl+Q 开始自动领取优惠券');

                // 设置快捷键监听
                this.setupShortcuts();
            },

            // 检查是否在商品页
            isItemPage() {
                return location.href.includes('item.jd.com');
            },            // 设置快捷键
            setupShortcuts() {
                document.addEventListener('keydown', (event) => {
                    // Ctrl+Q 触发优惠券领取
                    if (event.ctrlKey && event.key === 'q' && !event.shiftKey && !event.altKey) {
                        event.preventDefault();
                        this.log('快捷键触发: Ctrl+Q - 开始自动领取优惠券');
                        this.startAutoClaim();
                    }
                });
            },

            // 开始自动领取优惠券
            async startAutoClaim() {
                if (this.state.isRunning) {
                    this.log('优惠券领取正在进行中，请勿重复触发');
                    return;
                }

                this.log('开始自动领取优惠券...');
                this.state.isRunning = true;
                this.state.processedButtons.clear();
                this.state.totalClaimed = 0;
                this.state.hasSlider = false; try {
                    // 1. 先尝试触发优惠券展开（重试机制）
                    let triggered = false;
                    for (let attempt = 1; attempt <= 3; attempt++) {
                        triggered = await this.triggerCouponPanel();
                        if (triggered) break;

                        if (attempt < 3) {
                            await this.delay(1000);
                        }
                    }
                    if (!triggered) {
                        this.log('多次尝试后仍未找到优惠券展开区域');
                        return;
                    }// 2. 等待优惠券面板加载
                    await this.delay(800);

                    // 3. 查找优惠券面板
                    const couponPanel = this.findCouponPanel();
                    if (!couponPanel) {
                        this.log('优惠券面板未出现，可能此商品无可领取优惠券');
                        return;
                    }

                    this.log('找到优惠券面板，开始扫描可领取的优惠券...');                    // 4. 查找所有"立即领取"按钮
                    const claimButtons = this.findClaimButtons(couponPanel);
                    if (claimButtons.length === 0) {
                        this.log('未找到"立即领取"按钮，所有优惠券可能已被领取');
                        // 无券可领也要关闭弹窗
                        await this.delay(500);
                        this.closeCouponPanel();
                        return;
                    }

                    this.log(`找到 ${claimButtons.length} 个可领取的优惠券`);

                    // 5. 逐个点击领取
                    for (let i = 0; i < claimButtons.length; i++) {
                        // 检查是否遇到滑块验证
                        if (this.checkForSliderVerification()) {
                            this.log('检测到滑块验证，停止自动领取');
                            this.state.hasSlider = true;
                            break;
                        }

                        const button = claimButtons[i];
                        await this.clickClaimButton(button);
                        this.state.totalClaimed++;

                        // 延迟一段时间再点击下一个
                        if (i < claimButtons.length - 1) {
                            await this.delay(this.config.clickDelay);
                        }
                    }

                    this.log(`优惠券领取完成，共领取 ${this.state.totalClaimed} 个优惠券`);

                    // 自动关闭弹窗
                    await this.delay(1000); // 等待最后一个优惠券领取完成
                    this.closeCouponPanel();

                } catch (error) {
                    console.error('自动领取优惠券过程中出错:', error);
                } finally {
                    this.state.isRunning = false;
                }
            },            // 触发优惠券面板展开
            async triggerCouponPanel() {
                // 等待并查找可见的触发器
                let trigger = await this.waitForVisibleTrigger();

                if (!trigger) {
                    this.log('未找到可见的优惠券展开触发器');
                    return false;
                }

                this.log(`找到触发器，开始展开优惠券`);

                try {
                    // 滚动到元素位置
                    trigger.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    await this.delay(500);

                    // 再次检查元素是否可见
                    if (!this.isElementVisible(trigger)) {
                        await this.delay(1000);
                    }

                    // 直接点击，让优惠券正常展开
                    trigger.click();

                    // 等待面板完全加载
                    await this.delay(2000);

                } catch (error) {
                    console.error('触发展开时出错:', error);
                    return false;
                }

                return true;
            },// 等待可见的触发器出现
            async waitForVisibleTrigger(maxWait = 5000) {
                const startTime = Date.now();

                while (Date.now() - startTime < maxWait) {
                    // 优先使用 .more-btn
                    let trigger = document.querySelector('.more-btn');
                    if (trigger && this.isElementVisible(trigger)) {
                        return trigger;
                    }

                    // 备用 #summary-quan
                    trigger = document.querySelector('#summary-quan');
                    if (trigger && this.isElementVisible(trigger)) {
                        return trigger;
                    }

                    await this.delay(100);
                }

                // 超时后尝试使用存在的触发器
                return document.querySelector('.more-btn') || document.querySelector('#summary-quan');
            },            // 查找优惠券面板
            findCouponPanel() {
                const panel = document.querySelector('.coupons-list-box');
                if (panel && this.isElementVisible(panel)) {
                    this.log('找到优惠券面板');
                    return panel;
                }

                this.log('未找到优惠券面板');
                return null;
            },            // 关闭优惠券弹窗
            closeCouponPanel() {
                this.log('尝试关闭优惠券弹窗...');

                // 查找关闭按钮 (img.drawer-title-img)
                const closeBtn = document.querySelector('img.drawer-title-img');
                if (closeBtn && this.isElementVisible(closeBtn)) {
                    this.log('找到关闭按钮，点击关闭');
                    closeBtn.click();
                    return true;
                }

                this.log('未找到关闭按钮');
                return false;
            },// 查找"立即领取"按钮
            findClaimButtons(panel) {
                const buttons = [];

                // 方法1: 查找包含"立即领取"文本的 .coupon-btn-span
                const claimSpans = panel.querySelectorAll('.coupon-btn-span');
                for (const span of claimSpans) {
                    if (span.textContent.trim() === '立即领取') {
                        const button = span.closest('.coupon-btn');
                        if (button && this.isElementVisible(button)) {
                            buttons.push(button);
                        }
                    }
                }

                // 方法2: 如果没找到，尝试查找其他可能的领取按钮
                if (buttons.length === 0) {
                    const allButtons = panel.querySelectorAll('button, a, .btn, [class*="btn"]');
                    for (const button of allButtons) {
                        const text = button.textContent.trim();
                        if (text === '立即领取' || text === '领取' || text === '免费领取') {
                            if (this.isElementVisible(button)) {
                                buttons.push(button);
                            }
                        }
                    }
                }

                // 方法3: 通过文本内容查找
                if (buttons.length === 0) {
                    const allElements = panel.querySelectorAll('*');
                    for (const element of allElements) {
                        if (element.textContent.trim() === '立即领取' &&
                            this.isElementVisible(element) &&
                            (element.onclick || element.getAttribute('onclick') ||
                                window.getComputedStyle(element).cursor === 'pointer')) {
                            buttons.push(element);
                        }
                    }
                }

                this.log(`找到 ${buttons.length} 个可领取的优惠券按钮`);
                return buttons;
            },// 检查元素是否可见
            isElementVisible(element) {
                if (!element) return false;
                const rect = element.getBoundingClientRect();
                return rect.width > 0 && rect.height > 0 && element.offsetParent !== null;
            },

            // 延迟函数
            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            },            // 点击领取按钮
            async clickClaimButton(button) {
                try {
                    const buttonText = button.textContent.trim();
                    this.log(`领取: ${buttonText}`);

                    // 滚动到按钮位置
                    button.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    await this.delay(200);

                    // 模拟用户点击
                    button.click();

                    // 等待响应
                    await this.delay(500);

                    // 检查按钮是否变为"立即使用"
                    const span = button.querySelector('.coupon-btn-span');
                    if (span && span.textContent.trim() === '立即使用') {
                        this.log('✓ 领取成功');
                    }

                } catch (error) {
                    console.error('点击优惠券按钮时出错:', error);
                }
            },

            // 检查是否成功领取
            checkClaimSuccess(button) {
                // 检查按钮文本是否变为"已领取"、"立即使用"等
                const buttonText = button.textContent.trim();
                return buttonText.includes('已领取') ||
                    buttonText.includes('立即使用') ||
                    buttonText.includes('已抢光');
            },            // 检查是否遇到滑块验证
            checkForSliderVerification() {
                // 更精确的滑块验证选择器
                const sliderSelectors = [
                    '.jd-captcha',           // 京东验证码
                    '.captcha-container',    // 验证码容器
                    '.verify-popup',         // 验证弹窗
                    '.slider-container',     // 滑块容器
                    '#verify-popup'         // 验证弹窗ID
                ];

                // 检查是否有明确的滑块验证元素可见
                for (const selector of sliderSelectors) {
                    try {
                        const elements = document.querySelectorAll(selector);
                        for (const element of elements) {
                            if (this.isElementVisible(element)) {
                                // 进一步检查是否确实是验证码
                                const text = element.textContent.toLowerCase();
                                if (text.includes('验证') || text.includes('captcha') ||
                                    text.includes('滑动') || text.includes('拖动')) {
                                    this.log(`发现滑块验证: ${selector}`);
                                    return true;
                                }
                            }
                        }
                    } catch (error) {
                        // 忽略无效选择器
                    }
                }

                return false;
            },

            // 延迟函数
            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            },            // 日志输出
            log(message) {
                const prefix = '[自动领取优惠券]';
                console.log(`${prefix} ${message}`);
            },            // 手动触发优惠券领取（供其他模块调用）
            async manualClaim() {
                return await this.startAutoClaim();
            },

            // 检查是否可以领取优惠券
            canClaimCoupons() {
                return this.isItemPage() && !this.state.isRunning;
            },

            // 获取当前状态
            getStatus() {
                return {
                    isRunning: this.state.isRunning,
                    totalClaimed: this.state.totalClaimed,
                    hasSlider: this.state.hasSlider,
                    enabled: this.config.enabled,
                };
            },

            // 启用/禁用功能
            setEnabled(enabled) {
                this.config.enabled = enabled;
                this.log(`自动领取优惠券功能已${enabled ? '启用' : '禁用'}`);
            },            // 切换调试模式
            toggleDebugMode() {
                this.config.debugMode = !this.config.debugMode;
                this.log(`调试模式已${this.config.debugMode ? '开启' : '关闭'}`);
            },// 调试：显示页面上的优惠券相关元素
            debugCouponElements() {
                if (!this.config.debugMode) return;

                // 检查触发器
                const moreBtnElement = document.querySelector('.more-btn');
                const summaryElement = document.querySelector('#summary-quan');
                const panelElement = document.querySelector('.coupons-list-box');

                let status = '';
                if (moreBtnElement && this.isElementVisible(moreBtnElement)) {
                    status += '触发器✓ ';
                } else if (summaryElement && this.isElementVisible(summaryElement)) {
                    status += '备用触发器✓ ';
                }

                if (panelElement && this.isElementVisible(panelElement)) {
                    status += '面板✓ ';

                    // 快速统计按钮
                    const allTexts = Array.from(panelElement.querySelectorAll('*')).map(el => el.textContent.trim());
                    const claimCount = allTexts.filter(text => text === '立即领取').length;
                    const useCount = allTexts.filter(text => text === '立即使用').length;
                    status += `可领取:${claimCount} 已领取:${useCount}`;
                }

                if (status) this.log(`元素状态: ${status}`);
            },
        };

        // 将模块暴露到全局
        window.AutoCoupon = AutoCoupon;

        // 自动初始化
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => AutoCoupon.init());
        } else {
            AutoCoupon.init();
        }

        console.log('自动领取优惠券模块加载完成');
    })();
}
