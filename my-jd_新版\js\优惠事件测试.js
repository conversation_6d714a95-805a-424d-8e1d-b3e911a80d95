/**
 * 优惠事件测试模块
 * 用于测试JdPromotionCalculated事件监听架构
 */

(function () {
  'use strict';

  // 配置选项
  const CONFIG = {
    enableDetailedLogs: false, // 是否显示详细日志
    enableUIDisplay: true,     // 是否显示UI面板
    autoCloseDelay: 0          // UI面板自动关闭延迟（毫秒，0为不自动关闭）
  };

  console.log('[JD-HELPER] 🧪 优惠事件测试模块已加载');

  // 提供全局配置接口 - 确保在全局作用域中可用
  window.promotionTestConfig = {
    // 启用详细日志
    enableDetailedLogs: function () {
      CONFIG.enableDetailedLogs = true;
      console.log('[JD-HELPER] ✅ 已启用详细日志输出');
    },

    // 禁用详细日志
    disableDetailedLogs: function () {
      CONFIG.enableDetailedLogs = false;
      console.log('[JD-HELPER] ❌ 已禁用详细日志输出');
    },

    // 启用UI显示
    enableUI: function () {
      CONFIG.enableUIDisplay = true;
      console.log('[JD-HELPER] ✅ 已启用UI面板显示');
    },

    // 禁用UI显示
    disableUI: function () {
      CONFIG.enableUIDisplay = false;
      console.log('[JD-HELPER] ❌ 已禁用UI面板显示');
      // 移除现有面板
      const existingPanel = document.getElementById('jd-promotion-display');
      if (existingPanel) {
        existingPanel.remove();
      }
    },

    // 设置自动关闭延迟
    setAutoClose: function (delay) {
      CONFIG.autoCloseDelay = delay;
      console.log(`[JD-HELPER] ⏰ UI面板自动关闭延迟设置为: ${delay}ms`);
    },

    // 查看当前配置
    showConfig: function () {
      console.log('[JD-HELPER] 📋 当前配置:', CONFIG);
    },

    // 调试批量计算结果 - 使用页面实际数据
    debugBatchCalculation: function () {
      console.log('[JD-HELPER] 🔍 开始调试批量计算...');

      // 优先使用页面实际数据
      let testData = null;

      // 尝试获取页面实际数据
      if (typeof window.unifiedExtractor !== 'undefined' && window.unifiedExtractor.getProductData) {
        const pageData = window.unifiedExtractor.getProductData();
        if (pageData && pageData.apiData) {
          testData = pageData.apiData;
          console.log('[JD-HELPER] 📊 使用页面实际数据进行测试');
        }
      }

      // 如果没有页面数据，使用模拟数据
      if (!testData) {
        testData = {
          price: { op: "279.00", p: "279.00" },
          prom: {
            pickOneTag: 0,
            tags: [
              { text: "满1件享7.50折", subType: "单品满件折" },
              { text: "PLUS专享立减14元", subType: "PLUS专享立减" },
              { text: "满299享6折(京粉券)", subType: "粉|东券" }
            ]
          }
        };
        console.log('[JD-HELPER] 📊 使用模拟数据进行测试');
      }

      if (typeof window.jdPromotionCalculator !== 'undefined') {
        try {
          const batchResults = window.jdPromotionCalculator.calculateBatchPromotions(testData, 50);
          console.log('[JD-HELPER] 📊 批量计算结果（前10项）:', batchResults.slice(0, 10));

          // 分析最优数量
          let minPrice = Infinity;
          let optimalQty = 1;
          let allPrices = [];

          batchResults.forEach(item => {
            if (item.quantity && item.finalUnitPrice) {
              allPrices.push({
                数量: item.quantity,
                单价: item.finalUnitPrice.toFixed(2),
                总价: item.finalPrice.toFixed(2),
                优惠: item.totalDiscount.toFixed(2)
              });

              if (item.finalUnitPrice < minPrice) {
                minPrice = item.finalUnitPrice;
                optimalQty = item.quantity;
              }
            }
          });

          console.log(`[JD-HELPER] 🎯 计算得出最优数量: ${optimalQty}件, 单价: ¥${minPrice.toFixed(2)}`);
          console.log('[JD-HELPER] 📊 价格分布（前20项）:');
          console.table(allPrices.slice(0, 20));

          // 触发UI显示
          const testEvent = new CustomEvent('JdPromotionCalculated', {
            detail: {
              source: '调试测试',
              results: {
                single: batchResults[0],
                batch: batchResults
              },
              error: null
            }
          });
          document.dispatchEvent(testEvent);

        } catch (error) {
          console.error('[JD-HELPER] ❌ 调试失败:', error);
        }
      } else {
        console.error('[JD-HELPER] ❌ 优惠算法模块未加载');
      }
    }
  };

  // 确保配置对象立即可用
  console.log('[JD-HELPER] 🔧 全局配置对象已设置:', Object.keys(window.promotionTestConfig));

  // 监听优惠计算完成事件
  document.addEventListener('JdPromotionCalculated', function (event) {
    console.log('[JD-HELPER] 🎉 收到优惠计算结果 - 数据源:', event.detail.source);

    const { source, results, error, calculator } = event.detail;

    if (error) {
      console.error('[JD-HELPER] ❌ 优惠计算出错:', error);
      return;
    }

    if (results && results.single) {
      const single = results.single;

      // 简化的结果日志
      console.log(`[JD-HELPER] 💰 优惠结果: ¥${single.originalPrice} → ¥${single.finalPrice} (省¥${single.totalDiscount.toFixed(2)})`);

      // 详细日志（可配置）
      if (CONFIG.enableDetailedLogs) {
        console.log('[JD-HELPER] 📈 单件优惠结果:');
        console.log(`  - 原价: ¥${single.originalPrice}`);
        console.log(`  - 总优惠: ¥${single.totalDiscount}`);
        console.log(`  - 到手价: ¥${single.finalPrice}`);
        console.log(`  - 到手单价: ¥${single.finalUnitPrice}`);
        console.log(`  - 应用优惠券: ${single.appliedCoupons.length}张`);
        console.log(`  - 应用促销: ${single.appliedPromotions.length}个`);

        // 显示应用的促销详情
        if (single.appliedPromotions.length > 0) {
          console.log('  - 促销详情:');
          single.appliedPromotions.forEach((promo, index) => {
            console.log(`    ${index + 1}. ${promo.text} (${promo.subType}): -¥${promo.discountAmount}`);
          });
        }

        // 显示应用的优惠券详情
        if (single.appliedCoupons.length > 0) {
          console.log('  - 优惠券详情:');
          single.appliedCoupons.forEach((coupon, index) => {
            console.log(`    ${index + 1}. ${coupon.description} (${coupon.subType}): -¥${coupon.discountAmount}`);
          });
        }
      }
    }

    if (results && results.batch && CONFIG.enableDetailedLogs) {
      console.log('[JD-HELPER] 📊 批量优惠结果:');
      results.batch.slice(0, 5).forEach((item, index) => {
        if (item.quantity && item.finalUnitPrice) {
          console.log(`  - ${item.quantity}件: 单价¥${item.finalUnitPrice.toFixed(2)}, 总价¥${item.finalPrice.toFixed(2)}, 总优惠¥${item.totalDiscount.toFixed(2)}`);
        }
      });
    }

    // UI展示逻辑
    if (CONFIG.enableUIDisplay) {
      addPromotionUIDisplay(event.detail);
    }
  });

  /**
   * 添加优惠信息UI展示
   * @param {Object} calculationResult - 计算结果
   */
  function addPromotionUIDisplay(calculationResult) {
    // 检查是否已存在显示区域
    let displayArea = document.getElementById('jd-promotion-display');
    if (!displayArea) {
      displayArea = document.createElement('div');
      displayArea.id = 'jd-promotion-display';
      displayArea.style.cssText = `
                position: fixed;
                top: 80px;
                right: 20px;
                width: 320px;
                max-height: 500px;
                background: #fff;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 0;
                box-shadow: 0 6px 20px rgba(0,0,0,0.12);
                z-index: 10000;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Microsoft YaHei', sans-serif;
                font-size: 13px;
                line-height: 1.4;
                overflow: hidden;
                border-top: 3px solid #e3101e;
            `;
      document.body.appendChild(displayArea);
    }

    const { results, error } = calculationResult;

    if (error) {
      displayArea.innerHTML = `
                <div style="padding: 15px; color: #ff4444; font-weight: 500; text-align: center;">
                    <div style="font-size: 16px; margin-bottom: 8px;">⚠️ 计算出错</div>
                    <div style="font-size: 12px; color: #999;">${error}</div>
                </div>
            `;
      return;
    }

    if (results && results.single) {
      const single = results.single;
      const optimal = results.optimal; // 直接使用算法模块计算的最优数据

      let html = `
                <!-- 标题栏 -->
                <div style="background: linear-gradient(135deg, #ff6b6b 0%, #e3101e 100%); color: white; padding: 12px 15px; display: flex; align-items: center; justify-content: space-between;">
                    <div style="display: flex; align-items: center;">
                        <span style="font-size: 18px; margin-right: 8px;">💰</span>
                        <div>
                            <div style="font-weight: bold; font-size: 15px;">购买分析</div>
                            <div style="font-size: 11px; opacity: 0.9;">数据源: ${calculationResult.source}</div>
                        </div>
                    </div>
                    <button onclick="document.getElementById('jd-promotion-display').remove()"
                            style="background: none; border: none; color: white; font-size: 16px; cursor: pointer; padding: 0; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center;">
                        ×
                    </button>
                </div>

                <!-- 主要价格信息 -->
                <div style="padding: 15px; background: #f8f9fa;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px; padding: 10px; background: white; border-radius: 6px; border-left: 4px solid #28a745;">
                        <span style="font-weight: 600; color: #333;">最优单价</span>
                        <span style="font-size: 20px; font-weight: bold; color: #e3101e;">¥${optimal.optimalUnitPrice.toFixed(2)}</span>
                    </div>
                    <div style="display: flex; gap: 10px;">
                        <div style="flex: 1; padding: 8px 10px; background: white; border-radius: 6px; text-align: center;">
                            <div style="font-size: 11px; color: #666; margin-bottom: 3px;">最优买数</div>
                            <div style="font-weight: bold; color: #333; font-size: 16px;">×${optimal.optimalQuantity}</div>
                        </div>
                        <div style="flex: 1; padding: 8px 10px; background: white; border-radius: 6px; text-align: center;">
                            <div style="font-size: 11px; color: #666; margin-bottom: 3px;">最优总价</div>
                            <div style="font-weight: bold; color: #333; font-size: 16px;">¥${optimal.optimalTotalPrice.toFixed(0)}</div>
                        </div>
                    </div>
                    <div style="margin-top: 10px; padding: 8px 10px; background: #fff8dc; border-radius: 4px; border-left: 3px solid #ffa500;">
                        <div style="font-size: 11px; color: #666; margin-bottom: 2px;">单件价格对比</div>
                        <div style="font-size: 12px; color: #333;">
                            单买: ¥${single.finalUnitPrice.toFixed(2)} → 批量最优: ¥${optimal.optimalUnitPrice.toFixed(2)}
                            <span style="color: #28a745; font-weight: bold;">(省¥${optimal.unitPriceComparison.discount.toFixed(2)}/件)</span>
                        </div>
                    </div>
                </div>

                <!-- 优惠说明 -->
                <div style="padding: 15px; border-bottom: 1px solid #eee;">
                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                        <span style="font-weight: 600; color: #333; margin-right: 10px;">💡 优惠说明</span>
                    </div>
                    <div style="font-size: 12px; color: #666; line-height: 1.5;">
                        <div style="margin-bottom: 6px;">
                            <strong style="color: #28a745;">小优：</strong>${optimal.minQuantity}件起享受最优单价 ¥${optimal.optimalUnitPrice.toFixed(2)}
                        </div>
                        <div style="margin-bottom: 6px;">
                            <strong style="color: #ff6b35;">大优：</strong>最多${optimal.maxQuantity}件仍享受最优单价
                        </div>
                        <div>
                            <strong style="color: #6c757d;">节省：</strong>相比原价每件省¥${optimal.unitPriceComparison.discount.toFixed(2)} (${optimal.savingsPercent.toFixed(1)}%)
                        </div>
                    </div>
                </div>
                            <strong style="color: #007bff;">使用优惠：</strong>${single.appliedPromotions.map(p => p.text).join('、') || '暂无优惠'}
                        </div>
                    </div>
                </div>
            `;

      // 添加促销详情
      if (single.appliedPromotions.length > 0) {
        html += `
                    <div style="padding: 15px; border-bottom: 1px solid #eee;">
                        <div style="display: flex; align-items: center; margin-bottom: 10px;">
                            <span style="font-size: 14px; margin-right: 8px;">🎁</span>
                            <span style="font-weight: 600; color: #333;">应用促销</span>
                        </div>
                        <div style="max-height: 100px; overflow-y: auto;">
                `;
        single.appliedPromotions.forEach((promo, index) => {
          html += `
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 6px 10px; margin-bottom: 6px; background: #f0f8ff; border-radius: 4px; border-left: 3px solid #007bff;">
                                <span style="font-size: 12px; color: #333; flex: 1;">${promo.text}</span>
                                <span style="font-size: 12px; font-weight: bold; color: #28a745;">-¥${promo.discountAmount.toFixed(2)}</span>
                            </div>
                        `;
        });
        html += `
                        </div>
                    </div>
                `;
      }

      // 添加优惠券详情
      if (single.appliedCoupons.length > 0) {
        html += `
                    <div style="padding: 15px; border-bottom: 1px solid #eee;">
                        <div style="display: flex; align-items: center; margin-bottom: 10px;">
                            <span style="font-size: 14px; margin-right: 8px;">🎫</span>
                            <span style="font-weight: 600; color: #333;">应用优惠券</span>
                        </div>
                        <div style="max-height: 100px; overflow-y: auto;">
                `;
        single.appliedCoupons.forEach((coupon, index) => {
          html += `
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 6px 10px; margin-bottom: 6px; background: #fff8e1; border-radius: 4px; border-left: 3px solid #ff9800;">
                                <span style="font-size: 12px; color: #333; flex: 1;">${coupon.description}</span>
                                <span style="font-size: 12px; font-weight: bold; color: #ff6b35;">-¥${coupon.discountAmount.toFixed(2)}</span>
                            </div>
                        `;
        });
        html += `
                        </div>
                    </div>
                `;
      }

      // 添加批量优惠概览（显示关键档位）
      if (results.batch && results.batch.length > 1) {
        const keyQuantities = [1, 2, 3, 5, 10, 20, 50, 100].filter(qty =>
          results.batch.some(item => item.quantity === qty)
        );

        if (keyQuantities.length > 0) {
          const optimalQty = optimal.optimalQuantity; // 使用optimal字段
          html += `
                    <div style="padding: 15px;">
                        <div style="display: flex; align-items: center; margin-bottom: 12px;">
                            <span style="font-size: 14px; margin-right: 8px;">📊</span>
                            <span style="font-weight: 600; color: #333;">数量档位价格</span>
                        </div>
                        <div style="max-height: 150px; overflow-y: auto;">
                `;

          keyQuantities.forEach(qty => {
            const item = results.batch.find(r => r.quantity === qty);
            if (item && item.finalUnitPrice) {
              const isOptimal = qty === optimalQty;
              const savingsPerUnit = single.originalPrice - item.finalUnitPrice;
              html += `
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 10px; margin-bottom: 6px; background: ${isOptimal ? '#e8f5e8' : '#f8f9fa'}; border-radius: 4px; ${isOptimal ? 'border-left: 3px solid #28a745; box-shadow: 0 1px 3px rgba(40,167,69,0.2);' : ''}">
                                <div style="display: flex; align-items: center;">
                                    <div style="font-weight: 600; color: #333; margin-right: 8px;">${qty}件</div>
                                    ${isOptimal ? '<span style="background: #28a745; color: white; font-size: 10px; padding: 2px 6px; border-radius: 10px; font-weight: bold;">最优</span>' : ''}
                                </div>
                                <div style="text-align: right;">
                                    <div style="font-weight: bold; color: ${isOptimal ? '#28a745' : '#333'};">¥${item.finalUnitPrice.toFixed(2)}</div>
                                    <div style="font-size: 10px; color: #666;">省¥${savingsPerUnit.toFixed(2)}/件</div>
                                </div>
                            </div>
                        `;
            }
          });
          html += `
                        </div>
                    </div>
                `;
        }
      }

      displayArea.innerHTML = html;

      // 自动关闭功能
      if (CONFIG.autoCloseDelay > 0) {
        setTimeout(() => {
          if (document.getElementById('jd-promotion-display')) {
            document.getElementById('jd-promotion-display').remove();
          }
        }, CONFIG.autoCloseDelay);
      }
    }
  }

})();
