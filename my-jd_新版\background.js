// 京东商品追踪插件 - 后台脚本
// 专注于API请求、转链、商品追踪等后台服务

// API配置
const API_CONFIG = {
  baseUrl: 'https://zzz7.top/api',          
  healthUrl: 'https://zzz7.top/health',     
  timeout: 10000
};

console.log('📱 京东插件后台服务已启动 - API服务版本');

// 显示价格更新通知（保留通用通知功能）
function showPriceUpdateNotification(productData) {
  chrome.notifications.create({
    type: 'basic',
    iconUrl: 'icons/icon48.png',
    title: '价格变动提醒',
    message: `${productData.title}\n价格: ¥${productData.price}\n${productData.priceChange > 0 ? '涨价' : '降价'}: ¥${Math.abs(productData.priceChange)}`
  });
}

// API请求封装
async function apiRequest(endpoint, options = {}) {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), API_CONFIG.timeout);
  
  try {
    const response = await fetch(`${API_CONFIG.baseUrl}${endpoint}`, {
      signal: controller.signal,
      headers: {
        'Content-Type': 'application/json',
        'X-Extension-Id': chrome.runtime.id,
        ...options.headers
      },
      ...options
    });
    
    clearTimeout(timeoutId);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    clearTimeout(timeoutId);
    if (error.name === 'AbortError') {
      throw new Error('请求超时');
    }
    throw error;
  }
}

// 从京东页面提取商品信息
function extractProductInfoFromUrl(url) {
  const skuMatch = url.match(/\/(\d+)\.html/);
  if (!skuMatch) {
    throw new Error('无法从URL中提取商品SKU');
  }
  return {
    sku: skuMatch[1],
    url: url
  };
}

// 构建京东API URL（统一函数，避免重复代码）
function buildJdApiUrl(skuId) {
  return `http://japi.jingtuitui.com/api/get_goods_link?appid=2504011836194311&appkey=5516658d1c3ec236d35df07851d1d082&v=&unionid=2003821643&positionid=&gid=${skuId}`;
}

// ⚠️ 注意：getOrCreateProduct 和 createProduct 方法已移除
// 原因：京东API现在只返回转链结果，不再提供商品详情
// 商品信息现在通过 content_script.js 和 数据库交互.js 的 checkProductPage 方法
// 直接从页面元素提取并保存到数据库

// 更新商品价格
async function updateProductPrice(sku) {
  return await apiRequest(`/products/${sku}/update-price`, {
    method: 'PUT'
  });
}

// 添加用户跟踪
async function addUserTracking(sku, targetPrice = null) {
  const userId = await getUserId();
  return await apiRequest('/tracking', {
    method: 'POST',
    body: JSON.stringify({
      userId: userId,
      sku: sku,
      targetPrice: targetPrice
    })
  });
}

// 获取用户ID（基于插件ID生成）
async function getUserId() {
  return new Promise((resolve) => {
    chrome.storage.local.get(['userId'], (result) => {
      if (result.userId) {
        resolve(result.userId);
      } else {
        const userId = 'ext_' + chrome.runtime.id + '_' + Date.now();
        chrome.storage.local.set({ userId }, () => {
          resolve(userId);
        });
      }
    });
  });
}

// 消息监听器
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  // 处理转换链接请求（保持兼容性）
  if (request.type === 'convertLink') {
    handleConvertLink(request.url)
      .then(data => sendResponse({ success: true, data }))
      .catch(error => {
        console.error('转换链接失败:', error);
        sendResponse({ 
          success: false, 
          error: error.message 
        });
      });
    return true;
  }
  
  // 处理商品追踪请求
  if (request.type === 'trackProduct') {
    handleTrackProduct(request.url, request.targetPrice)
      .then(data => sendResponse({ success: true, data }))
      .catch(error => {
        console.error('追踪商品失败:', error);
        sendResponse({ 
          success: false, 
          error: error.message 
        });
      });
    return true;
  }
  
  // 处理价格更新请求
  if (request.type === 'updatePrice') {
    handleUpdatePrice(request.url)
      .then(data => sendResponse({ success: true, data }))
      .catch(error => {
        console.error('更新价格失败:', error);
        sendResponse({ 
          success: false, 
          error: error.message 
        });
      });
    return true;
  }
  
  // 处理获取追踪列表请求
  if (request.type === 'getTrackedProducts') {
    handleGetTrackedProducts()
      .then(data => sendResponse({ success: true, data }))
      .catch(error => {
        console.error('获取追踪列表失败:', error);
        sendResponse({
          success: false,
          error: error.message
        });
      });
    return true;
  }

  // 处理API请求（用于避免Mixed Content问题）
  if (request.action === 'makeApiRequest') {
    handleApiRequest(request.url, request.options)
      .then(data => sendResponse(data))
      .catch(error => {
        console.error('API请求失败:', error);
        sendResponse({
          success: false,
          error: error.message
        });
      });
    return true;
  }
});

// 处理API请求（用于避免Mixed Content问题）
async function handleApiRequest(url, options = {}) {
  try {
    console.log('Background处理API请求:', url, options);

    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'X-Extension-Id': chrome.runtime.id,
        ...options.headers
      }
    });

    if (response.ok) {
      const data = await response.json();
      console.log('Background API请求成功:', data);
      return data;
    } else {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
  } catch (error) {
    console.error('Background API请求失败:', error);
    throw error;
  }
}

// 处理转换链接
async function handleConvertLink(url) {
  try {
    console.log('[Background] 开始处理转链请求:', url);

    // 首先尝试调用京东转链API
    const linkResult = await fallbackToJdApi(url);
    console.log('[Background] 京东转链API结果:', linkResult);

    // 注意：商品信息获取现在由 content_script.js 自动处理
    // 不再需要在这里手动获取商品信息

    // 返回转链结果
    return linkResult;

  } catch (error) {
    console.error('[Background] 转链处理失败:', error);
    // 如果转链失败，返回原始URL
    return {
      originalUrl: url,
      error: error.message
    };
  }
}

// 处理商品追踪 - 已废弃，现在由页面端直接处理
// 原因：商品信息现在通过页面元素直接提取，不再需要后台处理
async function handleTrackProduct(url, targetPrice) {
  console.log('⚠️ handleTrackProduct已废弃，商品追踪现在由页面端处理');
  throw new Error('商品追踪功能已转移到页面端处理');
}

// 处理价格更新 - 已废弃，现在由页面端自动处理
// 原因：价格更新现在通过 checkProductPage API 自动进行
async function handleUpdatePrice(url) {
  console.log('⚠️ handleUpdatePrice已废弃，价格更新现在由页面端自动处理');
  throw new Error('价格更新功能已转移到页面端自动处理');
}

// 处理获取追踪列表
async function handleGetTrackedProducts() {
  const userId = await getUserId();
  return await apiRequest(`/tracking/user/${userId}`);
}

// 回退到京东API（用于API服务器不可用时）
async function fallbackToJdApi(url) {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 10000);

  try {
    // 提取商品ID
    const skuMatch = url.match(/(\d{6,})/);
    if (!skuMatch) {
      throw new Error('无法从URL中提取商品ID');
    }    const skuId = skuMatch[1];
    
    // 使用新的API接口
    const apiUrl = buildJdApiUrl(skuId);
    
    console.log('[Background] 调用新转链API:', apiUrl);
    
    const response = await fetch(apiUrl, {
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const result = await response.json();
    console.log('[Background] 新API响应:', result);
    
    return result;
  } catch (error) {
    clearTimeout(timeoutId);
    console.error('[Background] 新转链API调用失败:', error);
    throw error;
  }
}

// 页面变化监听（用于自动检测商品页面）
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    // 检测是否是京东商品页面
    const isJdProductPage = /https?:\/\/item\.jd\.com\/\d+\.html/.test(tab.url);
    
    if (isJdProductPage) {
      console.log('检测到京东商品页面，商品信息将由 content_script.js 自动处理');
      // 注意：商品信息提取和数据库操作现在由 content_script.js 和 数据库交互.js 自动处理
      // 不再需要在后台脚本中手动处理
    }
  }
});

// 插件启动时初始化
chrome.runtime.onStartup.addListener(() => {
  console.log('京东商品追踪插件启动 - 后台API服务已就绪');
});

chrome.runtime.onInstalled.addListener(() => {
  console.log('京东商品追踪插件安装完成 - 后台API服务已就绪');
});

console.log('🎯 Background.js 初始化完成 - 专注于API服务和转链功能');

