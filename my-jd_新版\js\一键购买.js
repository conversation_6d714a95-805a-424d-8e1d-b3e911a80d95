(function () {
    const QuickBuy = {
        // 添加用户输入的数量缓存
        userInputQuantity: 1,
        // 优惠数据缓存（来自优惠算法模块）
        promotionData: null,

        // 检查商品是否已下柜
        isProductDelisted() {
            const delistedTip = document.querySelector('.itemover-tip');
            if (delistedTip && delistedTip.textContent.includes('该商品已下柜')) {
                return true;
            }
            return false;
        },

        // 获取商品ID（直接从URL获取）
        getProductId() {
            try {
                // 从URL路径中提取SKU: /12345.html 或 /12345/
                const match = location.pathname.match(/\/(\d+)(?:\.html)?(?:\/|$)/);
                if (match && match[1]) {
                    return match[1];
                }
                
                // 备用方案：从完整URL中提取
                const urlMatch = location.href.match(/\/(\d+)\.html/);
                if (urlMatch && urlMatch[1]) {
                    return urlMatch[1];
                }
                
                // 兜底方案：从pageConfig获取
                if (window.pageConfig && window.pageConfig.product && window.pageConfig.product.skuid) {
                    return window.pageConfig.product.skuid;
                }
                
                return null;
            } catch (error) {
                console.error('LTBY一键购买: 从URL提取SKU失败:', error);
                return null;
            }
        },

        // 监听键盘输入数字
        initQuantityListener() {
            document.addEventListener('input', (event) => {
                const target = event.target;
                if (target.matches('#buy-num')) {
                    const value = parseInt(target.value);
                    this.userInputQuantity = value && value > 0 ? value : 1;
                }
            });
        },

        // 监听优惠算法模块事件
        initPromotionListener() {
            document.addEventListener('JdPromotionCalculated', (event) => {
                if (event.detail) {
                    this.promotionData = event.detail;
                    console.log('LTBY一键购买: 收到优惠算法数据', this.promotionData);
                    
                    // 更新按钮显示（如果需要显示价格信息）
                    this.updateButtonDisplay();
                }
            });
        },

        // 更新按钮显示
        updateButtonDisplay() {
            const optimalBtn = document.querySelector('#optimal-buy-btn');
            if (optimalBtn && this.promotionData) {
                const optimalQuantity = this.getOptimalQuantity();
                const optimalUnitPrice = this.getOptimalUnitPrice();
                
                if (optimalQuantity && optimalUnitPrice) {
                    optimalBtn.textContent = `最优购买(${optimalQuantity}件 ¥${optimalUnitPrice.toFixed(2)})`;
                } else {
                    optimalBtn.textContent = '最优购买';
                }
            }
        },

        // 获取购买数量
        getQuantity() {
            // 优先从输入框实时获取，缓存作为备用
            const buyNumInput = document.querySelector('#buy-num');
            if (buyNumInput && buyNumInput.value) {
                const value = parseInt(buyNumInput.value);
                if (value && value > 0) {
                    this.userInputQuantity = value;
                    return value;
                }
            }
            return this.userInputQuantity;
        },

        // 获取最优购买数量（从优惠算法模块获取）
        getOptimalQuantity() {
            if (this.promotionData && this.promotionData.results && this.promotionData.results.optimal && this.promotionData.results.optimal.optimalQuantity) {
                return this.promotionData.results.optimal.optimalQuantity;
            }
            return null; // 如果没有优惠数据，返回null而不是用户输入的数量
        },

        // 获取最优单价（从优惠算法模块获取）
        getOptimalUnitPrice() {
            if (this.promotionData && this.promotionData.results && this.promotionData.results.optimal && this.promotionData.results.optimal.optimalUnitPrice) {
                return this.promotionData.results.optimal.optimalUnitPrice;
            }
            return null;
        },

        // 获取最优总价（从优惠算法模块获取）
        getOptimalTotalPrice() {
            if (this.promotionData && this.promotionData.results && this.promotionData.results.optimal && this.promotionData.results.optimal.optimalTotalPrice) {
                return this.promotionData.results.optimal.optimalTotalPrice;
            }
            return null;
        },

        // 重写购买方法 - 优化速度
        async openBuyNow(quantityOverride) {
            // 检查商品是否已下柜
            if (this.isProductDelisted()) {
                alert('该商品已下柜，无法购买！');
                return;
            }

            try {
                const productId = this.getProductId();
                const quantity = quantityOverride || this.getQuantity();

                if (!productId) {
                    if (!sessionStorage.getItem('oneClickBuyNextAction')) {
                        alert('无法获取商品ID，请刷新页面重试');
                    }
                    return;
                }

                console.log(`LTBY一键购买: 开始购买流程 - 商品ID: ${productId}, 数量: ${quantity}`);

                // 构建加入购物车URL
                const timestamp = Date.now();
                const addToCartUrl = `https://cart.jd.com/gate.action?btg=1&pid=${productId}&pcount=${quantity}&ptype=1&ktype=2&_=${timestamp}`;
                let orderUrl = 'https://trade.jd.com/shopping/order/getOrderInfo.action';

                // 自动下单数据传递
                const autoOrderFlow = sessionStorage.getItem('isAutoOrderFlow_v3');
                const expectedPrice = sessionStorage.getItem('autoOrderExpectedUnitPrice_v3');
                const expectedQuantityFromStorage = sessionStorage.getItem('autoOrderExpectedQuantity_v3');
                const autoProductId = sessionStorage.getItem('autoOrderProductId');

                if (autoOrderFlow === 'true' && expectedPrice && expectedQuantityFromStorage && autoProductId) {
                    const params = new URLSearchParams();
                    params.append('isAutoOrderFlow', 'true');
                    params.append('expectedUnitPrice', expectedPrice);
                    params.append('expectedQuantity', expectedQuantityFromStorage);
                    params.append('autoOrderProductId', autoProductId);
                    orderUrl += `?${params.toString()}`;
                }

                // 使用Image对象快速发送请求
                const img = new Image();
                img.onload = img.onerror = () => {
                    console.log('LTBY一键购买: 加入购物车请求已发送');
                };
                img.src = addToCartUrl;

                // 立即打开订单页面
                setTimeout(() => {
                    const orderWindow = window.open(orderUrl, '_blank');
                    if (orderWindow) {
                        orderWindow.focus();
                        console.log('LTBY一键购买: 购买流程完成，订单页面已打开');
                    } else {
                        alert('无法打开新标签页，请检查浏览器弹窗拦截设置');
                    }
                }, 200);

            } catch (error) {
                console.error('LTBY一键购买: 购买流程出错:', error);
                alert('购买过程中出现错误，请稍后重试');
            }
        },

        // 最优价格购买（使用优惠算法模块的最优数量）
        async openOptimalBuyNow() {
            // 检查商品是否已下柜
            if (this.isProductDelisted()) {
                alert('该商品已下柜，无法购买！');
                return;
            }

            const optimalQuantity = this.getOptimalQuantity();
            if (!optimalQuantity || optimalQuantity < 1) {
                console.log('LTBY一键购买: 暂无优惠算法数据，使用默认数量1进行购买');
                return this.openBuyNow(1); // 使用默认数量1
            }

            console.log(`LTBY一键购买: 使用最优数量 ${optimalQuantity} 进行购买`);
            return this.openBuyNow(optimalQuantity);
        },

        // 插入一键购买按钮
        insertQuickBuyButton() {
            // 检查商品是否已下柜
            if (this.isProductDelisted()) {
                console.log('LTBY一键购买: 商品已下柜，不插入购买按钮');
                return false;
            }

            // 检查是否已存在，避免重复插入
            if (document.querySelector('#ltby-quick-buy-root')) {
                console.log('LTBY一键购买: 按钮已存在，跳过插入');
                return true;
            }

            // 根据域名判断插入位置
            const currentHost = window.location.hostname;
            let insertTarget = null;
            let insertMethod = 'append';

            if (currentHost.includes('jd.hk')) {
                // 国际站：在 id="qingguan" 的上方平级插入
                console.log('LTBY一键购买: 检测到国际站环境');
                const qingguanElement = document.querySelector('#qingguan');
                
                if (qingguanElement) {
                    insertTarget = qingguanElement;
                    insertMethod = 'insertBefore';
                    console.log('LTBY一键购买: 找到国际站插入点 #qingguan');
                } else {
                    console.log('LTBY一键购买: 未找到国际站插入点 #qingguan，等待加载...');
                    return false;
                }
            } else {
                // 中国站：使用原有逻辑
                console.log('LTBY一键购买: 检测到中国站环境');
                const chooseWrapper = document.querySelector('.choose-btns-wrapper');
                if (chooseWrapper) {
                    insertTarget = chooseWrapper;
                    const appBtn = chooseWrapper.querySelector('#J_choose_app_btn');
                    if (appBtn) {
                        insertMethod = 'insertAfter';
                        insertTarget = appBtn;
                        console.log('LTBY一键购买: 找到 #J_choose_app_btn，将插入到其后面');
                    } else {
                        insertMethod = 'append';
                        console.log('LTBY一键购买: 未找到 #J_choose_app_btn，追加到 .choose-btns-wrapper 末尾');
                    }
                    console.log('LTBY一键购买: 找到中国站插入点 .choose-btns-wrapper');
                } else {
                    console.log('LTBY一键购买: 未找到中国站插入点，等待加载...');
                    return false;
                }
            }

            if (!insertTarget) {
                console.log('LTBY一键购买: 未找到适合的插入点，等待动态加载...');
                return false;
            }

            try {
                // 创建按钮容器
                const btnContainer = document.createElement('div');
                btnContainer.id = 'ltby-quick-buy-root';
                btnContainer.className = 'ltby-quick-buy-container';

                btnContainer.innerHTML = `
                    <div class="ltby-quick-buy-wrapper">
                        <div class="ltby-brand">
                            <img src="${chrome.runtime.getURL('images/icon16.png')}" alt="LTBY助手">
                            <span>LTBY助手</span>
                        </div>
                        <div class="ltby-buttons">
                            <button type="button" id="quick-buy-btn" class="ltby-btn ltby-btn-primary">
                                一键购买
                            </button>
                             <button type="button" id="touch-screen-buy-btn" class="ltby-btn ltby-btn-primary">
                                触屏购买
                            </button>
                            <button type="button" id="optimal-buy-btn" class="ltby-btn ltby-btn-success">
                                最优购买
                            </button>
                        </div>
                    </div>
                `;

                // 事件委托
                btnContainer.addEventListener('click', (e) => {
                    const target = e.target;
                    if (target.closest('#quick-buy-btn')) {
                        this.openBuyNow();
                    } else if (target.closest('#optimal-buy-btn')) { 
                        this.openOptimalBuyNow();
                    } else if (target.closest('#touch-screen-buy-btn')) {
                        this.openTouchScreenBuyPage();
                    }
                });

                // 根据插入方法进行插入
                if (insertMethod === 'append') {
                    insertTarget.appendChild(btnContainer);
                } else if (insertMethod === 'insertAfter') {
                    if (insertTarget.nextSibling) {
                        insertTarget.parentNode.insertBefore(btnContainer, insertTarget.nextSibling);
                    } else {
                        insertTarget.parentNode.appendChild(btnContainer);
                    }
                } else if (insertMethod === 'insertBefore') {
                    insertTarget.parentNode.insertBefore(btnContainer, insertTarget);
                }

                console.log('LTBY一键购买: 按钮插入成功，插入方法:', insertMethod, '域名:', currentHost);
                return true;

            } catch (error) {
                console.error('LTBY一键购买: 按钮插入失败:', error);
                return false;
            }
        },

        // 验证并修正按钮位置
        validateAndFixButtonPosition() {
            const existingButton = document.querySelector('#ltby-quick-buy-root');
            if (!existingButton) {
                return false;
            }

            const currentHost = window.location.hostname;
            let isPositionCorrect = false;

            if (currentHost.includes('jd.hk')) {
                const qingguanElement = document.querySelector('#qingguan');
                if (qingguanElement && existingButton.parentNode === qingguanElement.parentNode) {
                    const siblings = Array.from(qingguanElement.parentNode.children);
                    const buttonIndex = siblings.indexOf(existingButton);
                    const qingguanIndex = siblings.indexOf(qingguanElement);
                    isPositionCorrect = buttonIndex < qingguanIndex;
                }
            } else {
                const targetContainer = document.querySelector('.choose-btns-wrapper');
                isPositionCorrect = targetContainer && targetContainer.contains(existingButton);
            }

            if (!isPositionCorrect) {
                console.log('LTBY一键购买: 按钮位置不正确，尝试重新定位...', '域名:', currentHost);
                existingButton.remove();
                this.insertQuickBuyButton();
                return true;
            }

            return true;
        },

        // 打开触屏购买页面
        openTouchScreenBuyPage() {
            const productId = this.getProductId();
            const quantity = this.getQuantity();

            if (!productId) {
                alert('未能获取商品ID，无法进行触屏购买。');
                return;
            }

            if (!quantity || isNaN(quantity) || quantity < 1) {
                alert('请输入有效的商品数量。');
                return;
            }

            const productUrl = `https://item.m.jd.com/product/${productId}.html`;
            const commlist = `${productId},,${quantity},${productId},${quantity},0,0`;

            const params = new URLSearchParams({
                sceneval: '2',
                bid: '',
                wdref: productUrl,
                scene: 'jd',
                isCanEdit: '1',
                EncryptInfo: '',
                Token: '',
                commlist: commlist,
                locationid: '',
                type: '0',
                lg: '0',
                supm: '0',
                favorablerate: '99',
                jxsid: ''
            });

            const targetUrl = `https://trade.m.jd.com/pay?${params.toString()}`;
            console.log('触屏购买URL:', targetUrl);
            window.open(targetUrl, '_blank');
        },

        // 兼容性方法
        performQuickBuy() {
            if (this.isProductDelisted()) {
                alert('该商品已下柜，无法购买！');
                return;
            }

            const productId = this.getProductId();
            const quantity = this.getQuantity();

            if (!productId) {
                alert('无法获取商品ID，请刷新页面重试');
                return;
            }

            console.log(`LTBY一键购买: 开始购买流程 - 商品ID: ${productId}, 数量: ${quantity}`);

            const timestamp = Date.now();
            const addToCartUrl = `https://cart.jd.com/gate.action?btg=1&pid=${productId}&pcount=${quantity}&ptype=1&ktype=2&_=${timestamp}`;
            const orderUrl = 'https://trade.jd.com/shopping/order/getOrderInfo.action';

            const img = new Image();
            img.onload = img.onerror = () => {
                console.log('LTBY一键购买: 加入购物车请求已发送');
            };
            img.src = addToCartUrl;

            setTimeout(() => {
                const orderWindow = window.open(orderUrl, '_blank');
                if (orderWindow) {
                    orderWindow.focus();
                    console.log('LTBY一键购买: 购买流程完成，订单页面已打开');
                } else {
                    alert('无法打开新标签页，请检查浏览器弹窗拦截设置');
                }
            }, 200);
        },

        // 初始化事件监听
        init() {
            if (this.isProductDelisted()) {
                console.log('LTBY一键购买: 商品已下柜，跳过初始化');
                return;
            }

            // 初始化数量监听器
            this.initQuantityListener();
            
            // 初始化优惠算法模块事件监听器
            this.initPromotionListener();

            // 监听页面变化
            const observer = new MutationObserver((mutations) => {
                for (let mutation of mutations) {
                    if (mutation.type === 'childList') {
                        for (let node of mutation.addedNodes) {
                            if (node.nodeType === 1) {
                                // 检测商品下柜
                                if (node.classList?.contains('itemover-tip') ||
                                    node.querySelector?.('.itemover-tip')) {
                                    if (this.isProductDelisted()) {
                                        const existingButton = document.querySelector('#ltby-quick-buy-root');
                                        if (existingButton) {
                                            existingButton.remove();
                                            console.log('LTBY一键购买: 检测到商品下柜，已移除购买按钮');
                                        }
                                    }
                                    return;
                                }

                                // 根据域名检测不同的动态加载区域
                                const currentHost = window.location.hostname;
                                let shouldReinsertButton = false;

                                if (currentHost.includes('jd.hk')) {
                                    if (node.classList?.contains('choose-btns') && node.classList?.contains('clearfix') ||
                                        node.querySelector?.('.choose-btns.clearfix') ||
                                        node.classList?.contains('choose-amount') ||
                                        node.querySelector?.('.choose-amount')) {
                                        console.log('LTBY一键购买: 检测到国际站动态购物车区域加载，尝试重新插入按钮');
                                        shouldReinsertButton = true;
                                    }
                                } else {
                                    if (node.classList?.contains('choose-btns-wrapper') ||
                                        node.querySelector?.('.choose-btns-wrapper')) {
                                        console.log('LTBY一键购买: 检测到中国站动态购物车区域加载，尝试重新插入按钮');
                                        shouldReinsertButton = true;
                                    }
                                }

                                if (shouldReinsertButton) {
                                    setTimeout(() => {
                                        if (!document.querySelector('#ltby-quick-buy-root')) {
                                            this.insertQuickBuyButton();
                                        } else {
                                            this.validateAndFixButtonPosition();
                                        }
                                    }, 100);
                                }
                            }
                        }
                    }
                }
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });

            console.log('LTBY一键购买: 初始化完成');
        }
    };

    // 自动初始化函数
    function autoInit() {
        console.log('LTBY: 开始初始化一键购买功能...');

        QuickBuy.init();

        // 智能插入策略
        function tryInsertButton() {
            let attempts = 0;
            const maxAttempts = 20;

            function attempt() {
                if (QuickBuy.isProductDelisted()) {
                    console.log('LTBY: 商品已下柜，停止尝试插入按钮');
                    return;
                }

                if (attempts >= maxAttempts) {
                    console.log('LTBY: 达到最大尝试次数，插入可能失败');
                    return;
                }

                attempts++;

                if (QuickBuy.validateAndFixButtonPosition()) {
                    console.log('LTBY: 按钮位置验证/修正完成');
                    return;
                }

                const insertSuccess = QuickBuy.insertQuickBuyButton();
                
                if (!insertSuccess) {
                    const delay = Math.min(200 + attempts * 50, 800);
                    console.log(`LTBY: 按钮插入尝试 ${attempts}/${maxAttempts} 失败，${delay}ms 后重试`);
                    
                    requestAnimationFrame(() => {
                        setTimeout(attempt, delay);
                    });
                } else {
                    console.log(`LTBY: 按钮插入成功，尝试次数: ${attempts}`);
                }
            }
            
            attempt();
        }

        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(tryInsertButton, 100);
            });
        } else {
            tryInsertButton();
        }

        // 定期检查按钮位置
        let checkCount = 0;
        const maxChecks = 12;
        const checkInterval = setInterval(() => {
            checkCount++;
            
            if (checkCount >= maxChecks) {
                clearInterval(checkInterval);
                console.log('LTBY: 按钮位置检查已完成，停止定期检查');
                
                if (!document.querySelector('#ltby-quick-buy-root')) {
                    console.warn('LTBY: 警告 - 按钮可能插入失败，请检查页面结构是否有变化');
                }
                return;
            }
            
            if (!QuickBuy.isProductDelisted()) {
                const existingButton = document.querySelector('#ltby-quick-buy-root');
                if (!existingButton) {
                    console.log(`LTBY: 定期检查发现按钮丢失，重新插入 (${checkCount}/${maxChecks})`);
                    QuickBuy.insertQuickBuyButton();
                } else {
                    QuickBuy.validateAndFixButtonPosition();
                }
            } else {
                clearInterval(checkInterval);
                console.log('LTBY: 商品已下柜，停止按钮位置检查');
            }
        }, 1000);
    }

    // 自动运行
    autoInit();

    // 导出到全局
    window.OneClickBuy = QuickBuy;
})();
