(function () {
    const QuickBuy = {
        // 添加用户输入的数量缓存
        userInputQuantity: 1,
        lastActivityBuyQuantity: 0, // 新增：用于存储上次活动购买的数量

        // 检查商品是否已下柜
        isProductDelisted() {
            const delistedTip = document.querySelector('.itemover-tip');
            if (delistedTip && delistedTip.textContent.includes('该商品已下柜')) {
                // // console.log('LTBY一键购买: 检测到商品已下柜，停止运行');
                return true;
            }
            return false;
        },        // 获取商品ID（使用统一元素提取器）
        getProductId() {
            // 使用统一元素提取器
            if (window.unifiedExtractor) {
                const skuId = window.unifiedExtractor.getSkuId();
                if (skuId) {
                    return skuId;
                }
            }

            // 备用方案：从其他位置获取
            // 从 pageConfig 获取
            if (window.pageConfig && window.pageConfig.product && window.pageConfig.product.skuid) {
                return window.pageConfig.product.skuid;
            }
            // 从商品编号元素获取
            const skuEl = document.querySelector('[data-id="skuID"]');
            if (skuEl) {
                const match = skuEl.textContent.match(/\d{6,}/);
                if (match) return match[0];
            }
            // 从URL获取
            const urlMatch = location.pathname.match(/(\d{6,})/);
            return urlMatch ? urlMatch[1] : null;
        },

        // 监听键盘输入数字
        initQuantityListener() {
            // 使用事件委托，减少重复绑定
            document.addEventListener('input', (event) => {
                const target = event.target;
                if (target.matches('#buy-num')) {
                    const value = parseInt(target.value);
                    this.userInputQuantity = value && value > 0 ? value : 1;
                    // // console.log(`LTBY一键购买: 数量更新为: ${this.userInputQuantity}`);
                }
            });
        },

        // 获取购买数量
        getQuantity() {
            // 优先从输入框实时获取，缓存作为备用
            const buyNumInput = document.querySelector('#buy-num');
            if (buyNumInput && buyNumInput.value) {
                const value = parseInt(buyNumInput.value);
                if (value && value > 0) {
                    this.userInputQuantity = value;
                    return value;
                }
            }
            return this.userInputQuantity;
        },

        // 新增方法：获取活动价购买数量 (异步，带重试)
        async getActivityQuantity(maxAttempts = 10, delay = 500) { // maxAttempts = 10, delay = 500ms => 5 seconds total
            for (let i = 0; i < maxAttempts; i++) {
                let attemptLog = `(尝试 ${i + 1}/${maxAttempts})`;                // 尝试从 unifiedExtractor 获取，它可能已经从第三方插件获取了数据
                if (window.unifiedExtractor && typeof window.unifiedExtractor.getCompleteProductData === 'function') {
                    const productData = window.unifiedExtractor.getCompleteProductData();
                    let activityPriceFromExtractor = null;
                    let hasValidActivityPrice = false;

                    // 检查京东活动价
                    if (productData && productData.price && typeof productData.price.activityPrice === 'number' && productData.price.activityPrice >= 0) {
                        activityPriceFromExtractor = productData.price.activityPrice;
                        hasValidActivityPrice = true;
                    }
                    // 检查第三方活动价
                    else if (productData && productData.thirdParty && productData.thirdParty.activityPrice !== null && productData.thirdParty.activityPrice !== undefined) {
                        const parsedPrice = parseFloat(String(productData.thirdParty.activityPrice).replace(/[^\d.]/g, ''));
                        if (!isNaN(parsedPrice) && parsedPrice >= 0) {
                            activityPriceFromExtractor = parsedPrice;
                            hasValidActivityPrice = true;
                        }
                    }

                    if (hasValidActivityPrice) {
                        // 尝试从 priceDescription 提取数量
                        let quantityForActivity = 1; // 默认为1

                        // 优先从 thirdParty.priceDescription 提取数量
                        if (productData && productData.thirdParty && productData.thirdParty.priceDescription) {
                            const match = productData.thirdParty.priceDescription.match(/购买(\d+)件/);
                            if (match && match[1]) {
                                const parsedQuantity = parseInt(match[1], 10);
                                if (!isNaN(parsedQuantity) && parsedQuantity > 0) {
                                    quantityForActivity = parsedQuantity;
                                    // console.log(`LTBY一键购买: 从第三方描述提取到数量: ${quantityForActivity} (描述: \"${productData.thirdParty.priceDescription}\") ${attemptLog}`);
                                }
                            }
                        }

                        // 如果第三方描述没有提取到，尝试从 price.priceDescription 提取
                        if (quantityForActivity === 1 && productData && productData.price && productData.price.priceDescription) {
                            const match = productData.price.priceDescription.match(/购买(\d+)件/);
                            if (match && match[1]) {
                                const parsedQuantity = parseInt(match[1], 10);
                                if (!isNaN(parsedQuantity) && parsedQuantity > 0) {
                                    quantityForActivity = parsedQuantity;
                                    // console.log(`LTBY一键购买: 从价格描述提取到数量: ${quantityForActivity} (描述: \"${productData.price.priceDescription}\") ${attemptLog}`);
                                }
                            }
                        }

                        // console.log(`LTBY一键购买: 检测到活动价 ${activityPriceFromExtractor}，活动购买数量: ${quantityForActivity} ${attemptLog}`);
                        this.lastActivityBuyQuantity = quantityForActivity; // 存储起来
                        return quantityForActivity;
                    }
                }
                // 如果 unifiedExtractor 没有提供活动价，或者没有提供数量信息，则尝试旧的 getActivityBuyQuantity
                if (window.unifiedExtractor && typeof window.unifiedExtractor.getActivityBuyQuantity === 'function') {
                    const rawActivityQuantity = window.unifiedExtractor.getActivityBuyQuantity();
                    // console.log(`LTBY一键购买: rawActivityQuantity from unifiedExtractor.getActivityBuyQuantity: '${rawActivityQuantity}' ${attemptLog}`);

                    const parsedQuantity = parseInt(rawActivityQuantity);
                    // console.log(`LTBY一键购买: parsedQuantity: ${parsedQuantity} ${attemptLog}`);

                    if (rawActivityQuantity && parsedQuantity > 0) {
                        // console.log(`LTBY一键购买: 获取到有效活动价购买数量: ${parsedQuantity} ${attemptLog}`);
                        this.lastActivityBuyQuantity = parsedQuantity;
                        return parsedQuantity;
                    } else {
                        // console.warn(`LTBY一键购买: unifiedExtractor.getActivityBuyQuantity() 返回无效值或解析后无效. Raw: '${rawActivityQuantity}', Parsed: ${parsedQuantity} ${attemptLog}`);
                    }
                } else {
                    // console.warn(`LTBY一键购买: unifiedExtractor 或 getActivityBuyQuantity 方法未定义 ${attemptLog}`);
                }

                if (i < maxAttempts - 1) {
                    // console.warn(`LTBY一键购买: 等待 ${delay}ms... ${attemptLog}`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
            // console.warn(`LTBY一键购买: ${maxAttempts}次尝试后仍未能获取到有效的活动价购买数量。将使用默认数量1。`);
            this.lastActivityBuyQuantity = 1; // 获取失败，默认1
            return 1; // 返回1作为备用，由调用方处理提示
        },

        // 新增：供 unifiedExtractor 调用以获取上次活动购买数量
        getLastActivityBuyQuantity() {
            return this.lastActivityBuyQuantity || 0;
        },

        // 重写购买方法 - 优化速度
        async openBuyNow(quantityOverride) {
            // 检查商品是否已下柜
            if (this.isProductDelisted()) {
                alert('该商品已下柜，无法购买！');
                return;
            }

            try {
                const productId = this.getProductId();
                const quantity = quantityOverride || this.getQuantity(); // 使用覆盖数量或默认数量

                if (!productId) {
                    // console.error('LTBY一键购买: 无法获取商品ID');
                    // alert('无法获取商品ID，请刷新页面重试'); // 在自动重试场景下，避免弹窗
                    if (!sessionStorage.getItem('oneClickBuyNextAction')) { // 只在非自动重试时提示
                        alert('无法获取商品ID，请刷新页面重试');
                    }
                    return;
                }

                // console.log(`LTBY一键购买: 开始购买流程 - 商品ID: ${productId}, 数量: ${quantity}`);
                // console.log(`LTBY一键购买 (openBuyNow): 当前 sessionStorage 状态 (跳转前):`);
                // console.log(`  autoOrderFlowKey_v3: ${sessionStorage.getItem('isAutoOrderFlow_v3')}`);
                // console.log(`  autoOrderExpectedUnitPrice_v3: ${sessionStorage.getItem('autoOrderExpectedUnitPrice_v3')}`);
                // console.log(`  autoOrderExpectedQuantity_v3: ${sessionStorage.getItem('autoOrderExpectedQuantity_v3')}`);
                // console.log(`  autoOrderProductId: ${sessionStorage.getItem('autoOrderProductId')}`);

                // 构建加入购物车URL
                const timestamp = Date.now();
                const addToCartUrl = `https://cart.jd.com/gate.action?btg=1&pid=${productId}&pcount=${quantity}&ptype=1&ktype=2&_=${timestamp}`;
                let orderUrl = 'https://trade.jd.com/shopping/order/getOrderInfo.action';

                // --- BEGIN AUTO ORDER DATA PASSING VIA URL ---
                const autoOrderFlow = sessionStorage.getItem('isAutoOrderFlow_v3');
                const expectedPrice = sessionStorage.getItem('autoOrderExpectedUnitPrice_v3');
                const expectedQuantityFromStorage = sessionStorage.getItem('autoOrderExpectedQuantity_v3'); // Renamed to avoid conflict
                const autoProductId = sessionStorage.getItem('autoOrderProductId');

                if (autoOrderFlow === 'true' && expectedPrice && expectedQuantityFromStorage && autoProductId) {
                    const params = new URLSearchParams();
                    params.append('isAutoOrderFlow', 'true');
                    params.append('expectedUnitPrice', expectedPrice);
                    params.append('expectedQuantity', expectedQuantityFromStorage);
                    params.append('autoOrderProductId', autoProductId);
                    orderUrl += `?${params.toString()}`;
                    // console.log(`LTBY一键购买 (openBuyNow): Appended auto order params to URL: ${params.toString()}`);
                }
                // --- END AUTO ORDER DATA PASSING VIA URL ---

                // console.log('LTBY一键购买: 开始并行处理...');

                // 方法1: 使用Image对象快速发送请求（最快）
                const img = new Image();
                img.onload = img.onerror = () => {
                    // console.log('LTBY一键购买: 加入购物车请求已发送');
                };
                img.src = addToCartUrl;

                // 同时立即打开订单页面，减少等待时间
                setTimeout(() => {
                    const orderWindow = window.open(orderUrl, '_blank');
                    if (orderWindow) {
                        orderWindow.focus();
                        // console.log('LTBY一键购买: 购买流程完成，订单页面已打开');
                    } else {
                        // console.error('LTBY一键购买: 无法打开新标签页');
                        alert('无法打开新标签页，请检查浏览器弹窗拦截设置');
                    }
                }, 200); // 仅等待200毫秒

            } catch (error) {
                // console.error('LTBY一键购买: 购买流程出错:', error);
                alert('购买过程中出现错误，请稍后重试');
            }
        },

        // 新增方法：活动价购买
        async openActivityBuyNow() {
            // 检查商品是否已下柜
            if (this.isProductDelisted()) {
                alert('该商品已下柜，无法购买！');
                return;
            }

            try {
                const productId = this.getProductId();
                // console.log('LTBY一键购买: 尝试获取活动价购买数量...');
                const quantity = await this.getActivityQuantity();
                this.lastActivityBuyQuantity = quantity || 0;

                if (!productId) {
                    // console.error('LTBY一键购买: 无法获取商品ID (活动价购买)');
                    alert('无法获取商品ID (活动价购买)，请刷新页面重试');
                    return;
                }

                if (!quantity || quantity < 1) { // 检查活动数量是否有效
                    alert('未能获取活动价购买数量或数量无效。这可能是因为第三方价格插件尚未加载完毕或未能提供有效数据。请稍等片刻重试，或尝试手动输入数量后使用“一键购买”。');
                    // console.warn(`LTBY一键购买: 活动价购买数量无效或获取超时: ${quantity}`);
                    return;
                }

                // console.log(`LTBY一键购买: 开始活动价购买流程 - 商品ID: ${productId}, 数量: ${quantity}`);
                // console.log(`LTBY一键购买 (openActivityBuyNow): 当前 sessionStorage 状态 (跳转前):`);
                // console.log(`  autoOrderFlowKey_v3: ${sessionStorage.getItem('isAutoOrderFlow_v3')}`);
                // console.log(`  autoOrderExpectedUnitPrice_v3: ${sessionStorage.getItem('autoOrderExpectedUnitPrice_v3')}`);
                // console.log(`  autoOrderExpectedQuantity_v3: ${sessionStorage.getItem('autoOrderExpectedQuantity_v3')}`);
                // console.log(`  autoOrderProductId: ${sessionStorage.getItem('autoOrderProductId')}`);

                // 构建加入购物车URL
                const timestamp = Date.now();
                const addToCartUrl = `https://cart.jd.com/gate.action?btg=1&pid=${productId}&pcount=${quantity}&ptype=1&ktype=2&_=${timestamp}`;
                let orderUrl = 'https://trade.jd.com/shopping/order/getOrderInfo.action';

                // --- BEGIN AUTO ORDER DATA PASSING VIA URL ---
                const autoOrderFlow = sessionStorage.getItem('isAutoOrderFlow_v3');
                const expectedPrice = sessionStorage.getItem('autoOrderExpectedUnitPrice_v3');
                // const expectedQuantityFromStorage = sessionStorage.getItem('autoOrderExpectedQuantity_v3'); // quantity is already defined in this function scope
                const autoProductId = sessionStorage.getItem('autoOrderProductId');

                if (autoOrderFlow === 'true' && expectedPrice && autoProductId) { // Quantity for activity buy is 'quantity' var
                    const params = new URLSearchParams();
                    params.append('isAutoOrderFlow', 'true');
                    params.append('expectedUnitPrice', expectedPrice);
                    params.append('expectedQuantity', quantity.toString()); // Use the 'quantity' determined by getActivityQuantity
                    params.append('autoOrderProductId', autoProductId);
                    orderUrl += `?${params.toString()}`;
                    // console.log(`LTBY一键购买 (openActivityBuyNow): Appended auto order params to URL: ${params.toString()}`);
                }
                // --- END AUTO ORDER DATA PASSING VIA URL ---

                // console.log('LTBY一键购买: 开始并行处理 (活动价)...');

                const img = new Image();
                img.onload = img.onerror = () => {
                    // console.log('LTBY一键购买: 加入购物车请求已发送 (活动价)');
                };
                img.src = addToCartUrl;

                setTimeout(() => {
                    const orderWindow = window.open(orderUrl, '_blank');
                    if (orderWindow) {
                        orderWindow.focus();
                        // console.log('LTBY一键购买: 活动价购买流程完成，订单页面已打开');
                    } else {
                        // console.error('LTBY一键购买: 无法打开新标签页 (活动价)');
                        alert('无法打开新标签页，请检查浏览器弹窗拦截设置');
                    }
                }, 200);

            } catch (error) {
                // console.error('LTBY一键购买: 活动价购买流程出错:', error);
                alert('活动价购买过程中出现错误，请稍后重试');
            }
        },

        // 插入一键购买按钮
        insertQuickBuyButton() {
            // 检查商品是否已下柜
            if (this.isProductDelisted()) {
                console.log('LTBY一键购买: 商品已下柜，不插入购买按钮');
                return false;
            }

            // 检查是否已存在，避免重复插入
            if (document.querySelector('#ltby-quick-buy-root')) {
                console.log('LTBY一键购买: 按钮已存在，跳过插入');
                return true;
            }

            // 优先查找京东动态固定的购物车区域
            let insertTarget = null;
            let insertMethod = 'append'; // 默认追加到容器末尾

            // 查找动态固定的购物车容器（唯一插入位置）
            const chooseWrapper = document.querySelector('.choose-btns-wrapper');
            if (chooseWrapper) {
                insertTarget = chooseWrapper;
                // 检查是否有 #J_choose_app_btn，优先插入到其后面
                const appBtn = chooseWrapper.querySelector('#J_choose_app_btn');
                if (appBtn) {
                    insertMethod = 'insertAfter'; // 插入到 #J_choose_app_btn 之后
                    insertTarget = appBtn;
                    console.log('LTBY一键购买: 找到 #J_choose_app_btn，将插入到其后面');
                } else {
                    insertMethod = 'append'; // 追加到容器末尾
                    console.log('LTBY一键购买: 未找到 #J_choose_app_btn，追加到 .choose-btns-wrapper 末尾');
                }
                console.log('LTBY一键购买: 找到动态购物车容器 .choose-btns-wrapper');
            }

            // 如果没有找到目标容器，直接返回失败，不使用备用方案
            if (!insertTarget) {
                console.log('LTBY一键购买: 未找到 .choose-btns-wrapper 容器，等待动态加载...');
                return false;
            }

            try {
                // 创建按钮容器
                const btnContainer = document.createElement('div');
                btnContainer.id = 'ltby-quick-buy-root';
                btnContainer.className = 'ltby-quick-buy-container';

                btnContainer.innerHTML = `
                    <div class="ltby-quick-buy-wrapper">
                        <div class="ltby-brand">
                            <img src="${chrome.runtime.getURL('images/icon16.png')}" alt="LTBY助手">
                            <span>LTBY助手</span>
                        </div>
                        <div class="ltby-buttons">
                            <button type="button" id="quick-buy-btn" class="ltby-btn ltby-btn-primary">
                                一键购买
                            </button>
                             <button type="button" id="touch-screen-buy-btn" class="ltby-btn ltby-btn-primary">
                                触屏购买
                            </button>
                            <button type="button" id="activity-buy-btn" class="ltby-btn ltby-btn-success">
                                活动价购买
                            </button>
                        </div>
                    </div>
                `;

                // 事件委托，提升性能
                btnContainer.addEventListener('click', (e) => {
                    const target = e.target;
                    if (target.closest('#quick-buy-btn')) {
                        this.openBuyNow();
                    } else if (target.closest('#activity-buy-btn')) { 
                        this.openActivityBuyNow();
                    } else if (target.closest('#touch-screen-buy-btn')) {
                        this.openTouchScreenBuyPage();
                    }
                });

                // 根据插入方法进行插入
                if (insertMethod === 'append') {
                    // 追加到目标容器末尾
                    insertTarget.appendChild(btnContainer);
                } else if (insertMethod === 'after') {
                    // 插入到目标元素后面
                    if (insertTarget.nextSibling) {
                        insertTarget.parentNode.insertBefore(btnContainer, insertTarget.nextSibling);
                    } else {
                        insertTarget.parentNode.appendChild(btnContainer);
                    }
                } else if (insertMethod === 'insertAfter') {
                    // 插入到指定元素后面（新增，用于插入到 #J_choose_app_btn 之后）
                    if (insertTarget.nextSibling) {
                        insertTarget.parentNode.insertBefore(btnContainer, insertTarget.nextSibling);
                    } else {
                        insertTarget.parentNode.appendChild(btnContainer);
                    }
                }

                console.log('LTBY一键购买: 按钮插入成功，插入方法:', insertMethod);
                return true;

            } catch (error) {
                console.error('LTBY一键购买: 按钮插入失败:', error);
                return false;
            }
        },

        // 验证并修正按钮位置
        validateAndFixButtonPosition() {
            const existingButton = document.querySelector('#ltby-quick-buy-root');
            if (!existingButton) {
                return false; // 按钮不存在，无法验证位置
            }

            // 检查按钮是否在正确的容器中
            const chooseWrapper = document.querySelector('.choose-btns-wrapper');
            if (!chooseWrapper) {
                return false; // 目标容器不存在
            }

            // 检查按钮是否已经在正确位置
            const buttonInCorrectContainer = chooseWrapper.contains(existingButton);
            
            if (!buttonInCorrectContainer) {
                console.log('LTBY: 按钮位置不正确，尝试重新定位...');
                
                // 移除现有按钮
                existingButton.remove();
                
                // 重新插入到正确位置
                this.insertQuickBuyButton();
                
                return true; // 位置已修正
            }

            return true; // 位置正确
        },

        // 新增方法：打开触屏购买页面
        openTouchScreenBuyPage() {
            const productId = this.getProductId();
            const quantity = this.getQuantity();

            if (!productId) {
                // console.error('未能获取商品ID');
                alert('未能获取商品ID，无法进行触屏购买。');
                return;
            }

            if (!quantity || isNaN(quantity) || quantity < 1) {
                alert('请输入有效的商品数量。');
                return;
            }

            const productUrl = `https://item.m.jd.com/product/${productId}.html`;
            const commlist = `${productId},,${quantity},${productId},${quantity},0,0`; // 修正quantity的模板字符串语法

            // 构建触屏购买URL
            // 参考信息: commlist=10082056215100,,1,10082056215100,1,0,0
            // wdref=https://item.m.jd.com/product/100012345678.html (示例)
            const params = new URLSearchParams({
                sceneval: '2',
                bid: '', // 根据抓包信息，此参数为空
                wdref: productUrl, // 商品详情页的移动版URL
                scene: 'jd',
                isCanEdit: '1',
                EncryptInfo: '', // 根据抓包信息，此参数为空
                Token: '', // 根据抓包信息，此参数为空
                commlist: commlist,
                locationid: '', // 根据抓包信息，此参数为空或不关键
                type: '0', // 根据抓包信息
                lg: '0', // 根据抓包信息
                supm: '0', // 根据抓包信息
                favorablerate: '99', // 根据抓包信息
                jxsid: '' // 根据抓包信息，此参数为空或不关键
            });

            const targetUrl = `https://trade.m.jd.com/pay?${params.toString()}`;
            // console.log('触屏购买URL:', targetUrl);
            window.open(targetUrl, '_blank');
        },

        performQuickBuy() {
            // 检查商品是否已下柜
            if (this.isProductDelisted()) {
                alert('该商品已下柜，无法购买！');
                return;
            }

            const productId = this.getProductId();
            const quantity = this.getQuantity();

            if (!productId) {
                // console.error('LTBY一键购买: 无法获取商品ID');
                alert('无法获取商品ID，请刷新页面重试');
                return;
            }

            // console.log(`LTBY一键购买: 开始购买流程 - 商品ID: ${productId}, 数量: ${quantity}`);

            // 构建加入购物车URL
            const timestamp = Date.now();
            const addToCartUrl = `https://cart.jd.com/gate.action?btg=1&pid=${productId}&pcount=${quantity}&ptype=1&ktype=2&_=${timestamp}`;
            const orderUrl = 'https://trade.jd.com/shopping/order/getOrderInfo.action';

            // console.log('LTBY一键购买: 开始并行处理...');

            // 方法1: 使用Image对象快速发送请求（最快）
            const img = new Image();
            img.onload = img.onerror = () => {
                // console.log('LTBY一键购买: 加入购物车请求已发送');
            };
            img.src = addToCartUrl;

            // 同时立即打开订单页面，减少等待时间
            setTimeout(() => {
                const orderWindow = window.open(orderUrl, '_blank');
                if (orderWindow) {
                    orderWindow.focus();
                    // console.log('LTBY一键购买: 购买流程完成，订单页面已打开');
                } else {
                    // console.error('LTBY一键购买: 无法打开新标签页');
                    alert('无法打开新标签页，请检查浏览器弹窗拦截设置');
                }
            }, 200); // 仅等待200毫秒
        },

        // 初始化事件监听
        init() {
            // 检查商品是否已下柜
            if (this.isProductDelisted()) {
                // console.log('LTBY一键购买: 商品已下柜，跳过初始化');
                return;
            }

            // 初始化数量监听器
            this.initQuantityListener();

            // 监听快捷键 - 这部分逻辑已移至自动下单模块 (js/自动下单.js) 统一管理
            // document.addEventListener('keydown', (event) => {
            //     // ArrowDown for regular "一键购买" (no modifiers)
            //     if (event.key === 'ArrowDown' && !event.ctrlKey && !event.shiftKey && !event.metaKey) {
            //         event.preventDefault();
            //         this.openBuyNow();
            //     }
            //     // Ctrl + X for "活动价购买"
            //     else if (event.ctrlKey && (event.key === 'x' || event.key === 'X')) {
            //         event.preventDefault();
            //         // console.log('LTBY一键购买: Ctrl+X快捷键触发活动价购买');
            //         this.openActivityBuyNow();
            //     }
            // });

            // 监听页面变化，检测商品下柜状态和动态购物车区域
            const observer = new MutationObserver((mutations) => {
                for (let mutation of mutations) {
                    if (mutation.type === 'childList') {
                        for (let node of mutation.addedNodes) {
                            if (node.nodeType === 1) {
                                // 检测商品下柜
                                if (node.classList?.contains('itemover-tip') ||
                                    node.querySelector?.('.itemover-tip')) {
                                    if (this.isProductDelisted()) {
                                        // 移除已插入的购买按钮
                                        const existingButton = document.querySelector('#ltby-quick-buy-root');
                                        if (existingButton) {
                                            existingButton.remove();
                                            console.log('LTBY一键购买: 检测到商品下柜，已移除购买按钮');
                                        }
                                    }
                                    return;
                                }

                                // 检测动态加载的购物车区域
                                if (node.classList?.contains('choose-btns-wrapper') ||
                                    node.querySelector?.('.choose-btns-wrapper')) {
                                    console.log('LTBY一键购买: 检测到动态购物车区域加载，尝试重新插入按钮');

                                    // 延迟一点时间确保DOM稳定
                                    setTimeout(() => {
                                        // 如果按钮还没有插入，尝试插入
                                        if (!document.querySelector('#ltby-quick-buy-root')) {
                                            this.insertQuickBuyButton();
                                        } else {
                                            // 按钮已存在，验证位置是否正确
                                            this.validateAndFixButtonPosition();
                                        }
                                    }, 100);
                                }
                            }
                        }
                    }
                }
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });

            // 检查是否有来自自动下单模块的重试请求 (移到 unifiedExtractor)
            // this.checkForAutoOrderRetry();

            // console.log('LTBY一键购买: 初始化完成');
        }
    };

    // 自动初始化函数
    function autoInit() {
        console.log('LTBY: 开始初始化一键购买功能...');

        // 立即初始化，不等待DOM
        QuickBuy.init();

        // 使用更智能的插入策略
        function tryInsertButton() {
            let attempts = 0;
            const maxAttempts = 20; // 增加尝试次数以应对复杂的动态加载

            function attempt() {
                // 检查商品是否已下柜
                if (QuickBuy.isProductDelisted()) {
                    console.log('LTBY: 商品已下柜，停止尝试插入按钮');
                    return;
                }

                if (attempts >= maxAttempts) {
                    console.log('LTBY: 达到最大尝试次数，插入可能失败');
                    return;
                }

                attempts++;

                // 先验证现有按钮位置
                if (QuickBuy.validateAndFixButtonPosition()) {
                    console.log('LTBY: 按钮位置验证/修正完成');
                    return;
                }

                // 如果没有按钮，尝试插入
                const insertSuccess = QuickBuy.insertQuickBuyButton();
                
                if (!insertSuccess) {
                    // 插入失败，继续尝试
                    const delay = Math.min(200 + attempts * 50, 800); // 渐进式延迟，最大800ms
                    console.log(`LTBY: 按钮插入尝试 ${attempts}/${maxAttempts} 失败，${delay}ms 后重试`);
                    
                    // 使用requestAnimationFrame优化性能
                    requestAnimationFrame(() => {
                        setTimeout(attempt, delay);
                    });
                } else {
                    console.log(`LTBY: 按钮插入成功，尝试次数: ${attempts}`);
                }
            }
            
            // 立即尝试一次
            attempt();
        }

        // 等待DOM稳定后开始插入
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(tryInsertButton, 100); // DOM加载完成后稍微延迟
            });
        } else {
            // DOM已加载完成，立即开始
            tryInsertButton();
        }

        // 有限次数验证按钮位置（京东页面一般10秒内完全加载完成）
        let checkCount = 0;
        const maxChecks = 12; // 增加检查次数，最多检查12次（12秒）
        const checkInterval = setInterval(() => {
            checkCount++;
            
            if (checkCount >= maxChecks) {
                clearInterval(checkInterval);
                console.log('LTBY: 按钮位置检查已完成，停止定期检查');
                
                // 最后检查一次按钮是否存在
                if (!document.querySelector('#ltby-quick-buy-root')) {
                    console.warn('LTBY: 警告 - 按钮可能插入失败，请检查页面结构是否有变化');
                }
                return;
            }
            
            if (!QuickBuy.isProductDelisted()) {
                // 验证按钮是否存在且位置正确
                const existingButton = document.querySelector('#ltby-quick-buy-root');
                if (!existingButton) {
                    console.log(`LTBY: 定期检查发现按钮丢失，重新插入 (${checkCount}/${maxChecks})`);
                    QuickBuy.insertQuickBuyButton();
                } else {
                    // 验证位置是否正确
                    QuickBuy.validateAndFixButtonPosition();
                }
            } else {
                // 如果商品已下柜，提前结束检查
                clearInterval(checkInterval);
                console.log('LTBY: 商品已下柜，停止按钮位置检查');
            }
        }, 1000); // 每1秒检查一次
    }    // 自动运行
    autoInit();

    // 导出到全局以便测试
    window.OneClickBuy = QuickBuy;
})();
