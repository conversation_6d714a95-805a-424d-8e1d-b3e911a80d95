//在触摸屏页，选择位置，创建一个按钮，快速跳转，跳转到pc页
// 1. 创建一个按钮，快速跳转到pc页、

const QuickJump = {
    // 检查是否为移动端域名
    isMobilePage() {
        return location.hostname === 'item.m.jd.com';
    },

    // 移动端链接转PC端链接
    convertMobileToPc(url) {
        // 提取商品ID
        const skuId = url.match(/\/product\/(\d+)\.html/)?.[1];
        return skuId ? `https://item.jd.com/${skuId}.html` : null;
    },

    preventLoginRedirect() {
        // 阻止登录跳转
        if (location.pathname.includes('/plogin/')) {
            history.back();
            return true;
        }
        return false;
    },

    // 创建跳转按钮
    createJumpButton() {
        // 只在移动端域名下显示
        if (!this.isMobilePage()) return;
        
        // 处理登录跳转
        if (this.preventLoginRedirect()) return;

        const button = document.createElement('div');
        button.className = 'ltby-pc-jump';
        button.innerHTML = '<span>电脑版</span>';
        
        const style = document.createElement('style');
        style.textContent = `
            .ltby-pc-jump {
                position: fixed;
                left: 50px;
                top: 12px;
                padding: 4px 12px;
                background: #e93b3d;
                border-radius: 15px;
                color: #fff;
                font-size: 13px;
                z-index: 999;
                border: 1px solid #fff;
                box-shadow: 0 1px 3px rgba(0,0,0,0.2);
                cursor: pointer;
            }
            .ltby-pc-jump:active {
                transform: scale(0.95);
            }
        `;
        document.head.appendChild(style);

        // 添加点击事件
        button.addEventListener('click', () => {
            const pcUrl = this.convertMobileToPc(location.href);
            if (pcUrl) window.location.href = pcUrl;
        });

        document.body.appendChild(button);
    },

    init() {
        // 页面加载完成后立即执行
        this.createJumpButton();
        
        // 监听URL变化,处理可能的登录跳转
        let lastUrl = location.href;
        new MutationObserver(() => {
            if (location.href !== lastUrl) {
                lastUrl = location.href;
                this.preventLoginRedirect();
            }
        }).observe(document, {subtree: true, childList: true});
    }
};

// 自动初始化
QuickJump.init();
