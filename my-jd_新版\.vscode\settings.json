{
    // Git配置
    "git.enableSmartCommit": true,
    "git.autofetch": true,
    "git.confirmSync": false,
    "git.postCommitCommand": "none",
    "git.showPushSuccessNotification": true,
    "git.ignoreLegacyWarning": true,
    "scm.inputFontSize": 14,
    // 自动保存配置
    "files.autoSave": "afterDelay",
    "files.autoSaveDelay": 1000,
    "files.saveConflictResolution": "overwriteFileOnDisk",
    "files.hotExit": "onExitAndWindowClose",
    "workbench.editor.restoreViewState": true,
    // 编辑器自动化
    "editor.formatOnSave": true,
    "editor.formatOnPaste": true,
    "editor.formatOnType": false,
    "editor.codeActionsOnSave": {
        "source.fixAll": "explicit",
        "source.organizeImports": "explicit"
    },
    "editor.trimAutoWhitespace": true,
    "files.trimTrailingWhitespace": true,
    "files.insertFinalNewline": true,
    "files.trimFinalNewlines": true,
    // 工作区配置
    "explorer.confirmDelete": false,
    "explorer.confirmDragAndDrop": false,
    "workbench.editor.enablePreview": false,
    "workbench.editor.closeOnFileDelete": true,
    "workbench.editor.revealIfOpen": true,
    "extensions.ignoreRecommendations": false,
    // 终端配置
    "terminal.integrated.defaultProfile.windows": "PowerShell",
    "terminal.integrated.enableMultiLinePasteWarning": false,
    // 特定文件类型配置
    "[javascript]": {
        "editor.defaultFormatter": "vscode.typescript-language-features",
        "editor.tabSize": 2,
        "editor.insertSpaces": true
    },
    "[json]": {
        "editor.defaultFormatter": "vscode.json-language-features",
        "editor.tabSize": 2
    },
    "[markdown]": {
        "editor.wordWrap": "on",
        "editor.quickSuggestions": {
            "comments": "off",
            "strings": "off",
            "other": "off"
        }
    }
}
