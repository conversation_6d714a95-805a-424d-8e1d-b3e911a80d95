const QRCodeManager = {
    qrContainer: null,

    async init() {
        // 等待页面加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.tryInitialize());
        } else {
            this.tryInitialize();
        }
    },

    async tryInitialize() {
        const preview = document.querySelector('#preview');
        if (!preview) return;

        this.insertQRCode();

        const pcUrl = this.getPcUrl();
        // console.log('[QRCode] 获取到页面URL:', pcUrl);

        if (pcUrl && window.LinkConverter) {
            // console.log('[QRCode] 等待转链初始化');
            // 等待转链初始化完成
            await window.LinkConverter.init(pcUrl);
            const currentLink = window.LinkConverter.getCurrentLink();

            if (currentLink) {
                // console.log('[QRCode] 使用转链结果:', currentLink);
                this.updateConvertedQRCode(currentLink);
            }
        }
    },

    updateConvertedQRCode(link) {
        // console.log('[QRCode] 使用链接生成二维码:', link);
        const convertedQRCode = document.getElementById("converted-qrcode");
        if (convertedQRCode && window.QRCode) {
            convertedQRCode.innerHTML = '';
            new QRCode(convertedQRCode, link);
        }
    },

    getPcUrl() {
        const canonical = document.querySelector('link[rel="canonical"]');
        if (canonical) {
            let url = canonical.getAttribute('href');
            return url.startsWith('//') ? 'https:' + url : url;
        }
        return location.href;
    },

    getMobileUrl() {
        // 从meta标签获取触屏链接
        const meta = document.querySelector('meta[http-equiv="mobile-agent"]');
        if (meta) {
            let content = meta.getAttribute('content');
            const match = content && content.match(/url=(.*)$/);
            if (match) {
                let url = match[1];
                return url.startsWith('//') ? 'https:' + url : url;
            }
        }
        // 如果没有找到meta标签,则尝试构造移动端链接
        const skuId = location.pathname.match(/(\d+)\.html/)?.[1];
        if (skuId) {
            return `https://item.m.jd.com/product/${skuId}.html`;
        }
        return '';
    },

    insertQRCode() {
        const preview = document.querySelector('#preview');
        if (!preview || preview.nextElementSibling?.classList.contains('ltby-qrcode-container')) return;

        this.qrContainer = document.createElement('div');
        this.qrContainer.className = 'ltby-qrcode-container ltby-qrcode-align-left';
        this.qrContainer.innerHTML = `
            <div class="ltby-qrcode-wrap">
                <div class="ltby-qrcode-item">
                    <div class="qrcode-title">扫码访问</div>
                    <div id="original-qrcode" class="qrcode-box"></div>
                </div>
                <div class="ltby-qrcode-item">
                    <div class="qrcode-title">手Q/微信二维码</div>
                    <div id="converted-qrcode" class="qrcode-box"></div>
                </div>
            </div>
        `;

        preview.parentNode.insertBefore(this.qrContainer, preview.nextSibling);

        // 立即生成原始二维码
        if (window.QRCode) {
            const mobileUrl = this.getMobileUrl();
            if (mobileUrl) {
                new QRCode(document.getElementById("original-qrcode"), mobileUrl);
            }
        }
    },

    removeQRCode() {
        this.qrContainer?.remove();
        this.qrContainer = null;
    }
};

// 将QRCodeManager暴露到全局window对象
if (typeof window !== 'undefined') {
    window.QRCodeManager = QRCodeManager;
}

// 自动初始化
QRCodeManager.init();
