const QRCodeManager = {
    qrContainer: null,

    async init() {
        // 等待页面加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.tryInitialize());
        } else {
            this.tryInitialize();
        }
    },

    async tryInitialize() {
        const preview = document.querySelector('#preview');
        if (!preview) return;

        this.insertQRCode();

        const pcUrl = this.getPcUrl();
        // console.log('[QRCode] 获取到页面URL:', pcUrl);

        if (pcUrl && window.LinkConverter) {
            // console.log('[QRCode] 等待转链初始化');
            // 等待转链初始化完成
            await window.LinkConverter.init(pcUrl);
            const currentLink = window.LinkConverter.getCurrentLink();

            if (currentLink) {
                // console.log('[QRCode] 使用转链结果:', currentLink);
                this.updateConvertedQRCode(currentLink);
            }
        }
    },

    updateConvertedQRCode(link) {
        // console.log('[QRCode] 使用链接生成二维码:', link);
        const convertedQRCode = document.getElementById("converted-qrcode");
        if (convertedQRCode && window.QRCode) {
            convertedQRCode.innerHTML = '';
            new QRCode(convertedQRCode, link);
        }
    },

    getPcUrl() {
        // 直接基于当前URL，去掉.html后的所有内容
        let url = location.href;
        const htmlIndex = url.indexOf('.html');
        if (htmlIndex !== -1) {
            url = url.substring(0, htmlIndex);
        }
        return url;
    },

    getMobileUrl() {
        // 直接基于当前URL构造移动端链接，不再依赖meta标签
        const skuId = location.pathname.match(/(\d+)\.html/)?.[1];
        if (skuId) {
            return `https://item.m.jd.com/product/${skuId}.html`;
        }
        
        // 如果无法从URL提取SKU，直接将当前URL转换为触屏URL
        let url = location.href;
        // 将PC端域名替换为移动端域名
        if (url.includes('item.jd.com/')) {
            url = url.replace('item.jd.com/', 'item.m.jd.com/product/');
        }
        return url;
    },

    insertQRCode() {
        const preview = document.querySelector('#preview');
        if (!preview || preview.nextElementSibling?.classList.contains('ltby-qrcode-container')) return;

        this.qrContainer = document.createElement('div');
        this.qrContainer.className = 'ltby-qrcode-container ltby-qrcode-align-left';
        this.qrContainer.innerHTML = `
            <div class="ltby-qrcode-wrap">
                <div class="ltby-qrcode-item">
                    <div class="qrcode-title">扫码访问</div>
                    <div id="original-qrcode" class="qrcode-box"></div>
                </div>
                <div class="ltby-qrcode-item">
                    <div class="qrcode-title">手Q/微信二维码</div>
                    <div id="converted-qrcode" class="qrcode-box"></div>
                </div>
            </div>
        `;

        preview.parentNode.insertBefore(this.qrContainer, preview.nextSibling);

        // 立即生成原始二维码
        if (window.QRCode) {
            const mobileUrl = this.getMobileUrl();
            if (mobileUrl) {
                new QRCode(document.getElementById("original-qrcode"), mobileUrl);
            }
        }
    },

    removeQRCode() {
        this.qrContainer?.remove();
        this.qrContainer = null;
    }
};

// 将QRCodeManager暴露到全局window对象
if (typeof window !== 'undefined') {
    window.QRCodeManager = QRCodeManager;
}

// 自动初始化
QRCodeManager.init();
