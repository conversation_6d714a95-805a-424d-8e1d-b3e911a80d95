// 优惠分析悬浮窗 - 集成优惠算法模块
(function() {
    'use strict';

    // 防止重复注入
    if (window.jdPromotionFloatingPanelLoaded) {
        return;
    }
    window.jdPromotionFloatingPanelLoaded = true;

    console.log('[JD优惠分析] 🚀 悬浮窗脚本开始加载');

    // 等待优惠算法模块加载
    function waitForCalculator() {
        return new Promise((resolve) => {
            if (window.JDPromotionCalculator) {
                resolve();
                return;
            }

            const checkInterval = setInterval(() => {
                if (window.JDPromotionCalculator) {
                    clearInterval(checkInterval);
                    resolve();
                }
            }, 100);

            // 超时处理
            setTimeout(() => {
                clearInterval(checkInterval);
                console.error('[JD优惠分析] ❌ 优惠算法模块加载超时');
                resolve(); // 即使超时也继续执行
            }, 5000);
        });
    }

    // 注入悬浮窗HTML和CSS
    function injectFloatingPanel() {
        // 注入CSS
        const cssLink = document.createElement('link');
        cssLink.rel = 'stylesheet';
        cssLink.href = chrome.runtime.getURL('styles/purchase-analysis.css');
        document.head.appendChild(cssLink);

        // 创建悬浮窗HTML
        const floatingPanelHTML = `
            <div id="purchase-analysis-root-container">
                <!-- 简化的仪表板 - 点击展开详细分析 -->
                <div id="dashboard-container">
                    <div class="dashboard-header">
                        <span>优惠分析</span>
                        <button id="toggle-dashboard" style="background: none; border: none; color: white; cursor: pointer;">▼</button>
                    </div>
                    <div class="dashboard-content">
                        <div class="dashboard-item">
                            <span class="dashboard-label">最优单价</span>
                            <span class="dashboard-value" id="dashboard-best-price">计算中...</span>
                        </div>
                        <div class="dashboard-item">
                            <span class="dashboard-label">最优购买数</span>
                            <span class="dashboard-value" id="dashboard-best-quantity">计算中...</span>
                        </div>
                        <div class="dashboard-item">
                            <span class="dashboard-label">最优总价</span>
                            <span class="dashboard-value" id="dashboard-total-price">计算中...</span>
                        </div>
                        <div class="dashboard-item">
                            <span class="dashboard-label">节省金额</span>
                            <span class="dashboard-value" id="dashboard-savings">计算中...</span>
                        </div>
                    </div>
                </div>

                <!-- 详细分析面板 - 默认隐藏 -->
                <div id="purchase-analysis-container" class="hidden">
                    <div class="header">
                        <span>购买分析报告</span>
                        <div>
                            <button id="copy-text-btn">复制文案</button>
                            <button id="add-to-cart-btn">加车购买</button>
                        </div>
                        <button id="close-analysis-btn">×</button>
                    </div>
                    <div class="content">
                        <div class="summary">
                            <div class="summary-item">
                                <p>最优单价/原价</p>
                                <p><span id="best-price">¥0.00</span> / <span id="original-price">¥0.00</span></p>
                            </div>
                            <div class="summary-item">
                                <p>最优购买数</p>
                                <p><span id="best-quantity">×1</span> / <a href="#" id="quick-add-cart">快速加车</a></p>
                            </div>
                            <div class="summary-item">
                                <p>最优总价/原价</p>
                                <p><span id="total-best-price">¥0.00</span> / <span id="total-original-price">¥0.00</span></p>
                            </div>
                        </div>
                        
                        <div class="sub-summary">
                            <span id="unit-price-info">单位价格信息计算中...</span>
                        </div>

                        <div class="promotions">
                            <p>参与促销&优惠券:</p>
                            <ul id="promotion-list">
                                <li>正在分析优惠信息...</li>
                            </ul>
                        </div>

                        <div class="details">
                            <div class="tabs">
                                <button class="tab-link active">详细分析表</button>
                                <div class="switch-view">
                                    数量范围:
                                    <select id="quantity-range">
                                        <option value="1-10">1-10件</option>
                                        <option value="1-20">1-20件</option>
                                        <option value="1-50">1-50件</option>
                                    </select>
                                </div>
                            </div>
                            <div id="details-table" class="tab-content">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>数量</th>
                                            <th>到手单价</th>
                                            <th>到手总价</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="analysis-table-body">
                                        <tr>
                                            <td colspan="4" style="text-align: center;">正在计算优惠信息...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 将HTML插入到页面中
        const container = document.createElement('div');
        container.innerHTML = floatingPanelHTML;
        document.body.appendChild(container.firstElementChild);

        console.log('[JD优惠分析] ✅ 悬浮窗HTML已注入');
    }

    // 京东优惠分析悬浮窗主逻辑类
    class JDPromotionFloatingPanel {
        constructor() {
            this.calculator = null;
            this.currentProductData = null;
            this.analysisResults = {};
            this.isDetailPanelVisible = false;
            this.dataMonitorInterval = null;
            
            this.init();
        }

        async init() {
            // 等待优惠算法模块加载
            await waitForCalculator();
            
            if (window.JDPromotionCalculator) {
                this.calculator = new window.JDPromotionCalculator();
                console.log('[JD优惠分析] ✅ 优惠算法模块已加载');
            } else {
                console.error('[JD优惠分析] ❌ 优惠算法模块加载失败');
                this.showError('优惠算法模块加载失败');
                return;
            }

            this.bindEvents();
            this.makeContainerDraggable();
            this.startProductDataMonitoring();
        }

        bindEvents() {
            // 仪表板点击展开/收起
            const dashboardContainer = document.getElementById('dashboard-container');
            if (dashboardContainer) {
                dashboardContainer.addEventListener('click', (e) => {
                    if (e.target.id !== 'toggle-dashboard') {
                        this.toggleDetailPanel();
                    }
                });
            }

            // 切换按钮
            const toggleBtn = document.getElementById('toggle-dashboard');
            if (toggleBtn) {
                toggleBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.toggleDetailPanel();
                });
            }

            // 关闭按钮
            const closeBtn = document.getElementById('close-analysis-btn');
            if (closeBtn) {
                closeBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.hideDetailPanel();
                });
            }

            // 复制文案按钮
            const copyBtn = document.getElementById('copy-text-btn');
            if (copyBtn) {
                copyBtn.addEventListener('click', () => {
                    this.copyPromotionText();
                });
            }

            // 加车购买按钮
            const addCartBtn = document.getElementById('add-to-cart-btn');
            if (addCartBtn) {
                addCartBtn.addEventListener('click', () => {
                    this.addToCart();
                });
            }

            // 数量范围选择
            const quantityRange = document.getElementById('quantity-range');
            if (quantityRange) {
                quantityRange.addEventListener('change', (e) => {
                    this.updateAnalysisTable(e.target.value);
                });
            }
        }

        makeContainerDraggable() {
            const container = document.getElementById('purchase-analysis-root-container');
            const header = document.querySelector('.dashboard-header');
            
            if (!container || !header) return;

            let isDragging = false;
            let currentX;
            let currentY;
            let initialX;
            let initialY;
            let xOffset = 0;
            let yOffset = 0;

            header.addEventListener('mousedown', (e) => {
                if (e.target.tagName === 'BUTTON') return;
                
                initialX = e.clientX - xOffset;
                initialY = e.clientY - yOffset;
                isDragging = true;
                header.style.cursor = 'grabbing';
            });

            document.addEventListener('mousemove', (e) => {
                if (isDragging) {
                    e.preventDefault();
                    currentX = e.clientX - initialX;
                    currentY = e.clientY - initialY;
                    xOffset = currentX;
                    yOffset = currentY;
                    
                    container.style.transform = `translate(${currentX}px, ${currentY}px)`;
                }
            });

            document.addEventListener('mouseup', () => {
                isDragging = false;
                header.style.cursor = 'grab';
            });
        }

        startProductDataMonitoring() {
            // 立即检测一次
            this.detectProductData();
            
            // 定期检查页面变化
            this.dataMonitorInterval = setInterval(() => {
                this.detectProductData();
            }, 3000);

            // 监听全局数据事件（如果有的话）
            window.addEventListener('jd-product-data-updated', (event) => {
                console.log('[JD优惠分析] 📡 接收到产品数据更新事件:', event.detail);
                if (event.detail && event.detail.data) {
                    this.currentProductData = event.detail.data;
                    this.calculatePromotions();
                }
            });
        }

        detectProductData() {
            try {
                // 方法1: 尝试从全局变量获取
                if (window.pageConfig && window.pageConfig.product) {
                    const productData = window.pageConfig.product;
                    if (JSON.stringify(productData) !== JSON.stringify(this.currentProductData)) {
                        this.currentProductData = productData;
                        this.calculatePromotions();
                        return;
                    }
                }

                // 方法2: 尝试从页面元素提取基本信息
                const basicData = this.extractBasicProductData();
                if (basicData && basicData.price) {
                    if (JSON.stringify(basicData) !== JSON.stringify(this.currentProductData)) {
                        this.currentProductData = basicData;
                        this.calculatePromotions();
                    }
                }
            } catch (error) {
                console.log('[JD优惠分析] 获取商品数据失败:', error);
            }
        }

        extractBasicProductData() {
            // 从京东页面提取基本商品数据
            try {
                // 获取商品价格
                const priceSelectors = [
                    '.p-price .price',
                    '.summary-price .p-price .price', 
                    '.price-now',
                    '.p-price-now'
                ];
                
                let price = null;
                for (const selector of priceSelectors) {
                    const priceElement = document.querySelector(selector);
                    if (priceElement) {
                        const priceText = priceElement.textContent.replace(/[^\d.]/g, '');
                        price = parseFloat(priceText);
                        if (price && price > 0) break;
                    }
                }

                if (!price) return null;

                // 构造基本的商品数据结构
                return {
                    price: {
                        p: price
                    },
                    preferenceInfo: {
                        coupons: [],
                        promotions: []
                    },
                    dataSource: 'page_extraction'
                };
            } catch (error) {
                console.error('[JD优惠分析] 提取页面数据失败:', error);
                return null;
            }
        }

        calculatePromotions() {
            if (!this.currentProductData || !this.calculator) {
                this.showError('数据不完整');
                return;
            }

            try {
                console.log('[JD优惠分析] 🧮 开始计算优惠信息...');
                
                // 计算1-20件的优惠情况
                this.analysisResults = {};
                let bestResult = null;
                let bestQuantity = 1;

                for (let quantity = 1; quantity <= 20; quantity++) {
                    const result = this.calculator.calculateOptimalDiscount(this.currentProductData, quantity);
                    this.analysisResults[quantity] = result;

                    // 找到最优购买数量（最低单价）
                    if (!bestResult || result.finalUnitPrice < bestResult.finalUnitPrice) {
                        bestResult = result;
                        bestQuantity = quantity;
                    }
                }

                console.log('[JD优惠分析] ✅ 优惠计算完成，最优数量:', bestQuantity);
                
                this.updateDashboard(bestResult, bestQuantity);
                if (this.isDetailPanelVisible) {
                    this.updateDetailPanel(bestResult, bestQuantity);
                }
                
            } catch (error) {
                console.error('[JD优惠分析] 计算优惠失败:', error);
                this.showError('计算优惠信息失败: ' + error.message);
            }
        }

        updateDashboard(bestResult, bestQuantity) {
            if (!bestResult) return;

            const elements = {
                'dashboard-best-price': `¥${bestResult.finalUnitPrice.toFixed(2)}`,
                'dashboard-best-quantity': `×${bestQuantity}`,
                'dashboard-total-price': `¥${bestResult.finalPrice.toFixed(2)}`,
                'dashboard-savings': `¥${((bestResult.originalPrice * bestQuantity) - bestResult.finalPrice).toFixed(2)}`
            };

            Object.entries(elements).forEach(([id, text]) => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = text;
                }
            });
        }

        showError(message) {
            const errorElements = [
                'dashboard-best-price',
                'dashboard-best-quantity', 
                'dashboard-total-price',
                'dashboard-savings'
            ];

            errorElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = '错误';
                    element.style.color = '#ff0000';
                }
            });
            
            console.error('[JD优惠分析] 错误:', message);
        }

        toggleDetailPanel() {
            const panel = document.getElementById('purchase-analysis-container');
            const toggleBtn = document.getElementById('toggle-dashboard');
            
            if (!panel || !toggleBtn) return;

            if (this.isDetailPanelVisible) {
                this.hideDetailPanel();
            } else {
                this.showDetailPanel();
            }
        }

        showDetailPanel() {
            const panel = document.getElementById('purchase-analysis-container');
            const toggleBtn = document.getElementById('toggle-dashboard');
            
            if (panel && toggleBtn) {
                panel.classList.remove('hidden');
                toggleBtn.textContent = '▲';
                this.isDetailPanelVisible = true;
                
                // 更新详细面板内容
                const bestQuantity = this.getBestQuantity();
                const bestResult = this.analysisResults[bestQuantity];
                if (bestResult) {
                    this.updateDetailPanel(bestResult, bestQuantity);
                }
            }
        }

        hideDetailPanel() {
            const panel = document.getElementById('purchase-analysis-container');
            const toggleBtn = document.getElementById('toggle-dashboard');
            
            if (panel && toggleBtn) {
                panel.classList.add('hidden');
                toggleBtn.textContent = '▼';
                this.isDetailPanelVisible = false;
            }
        }

        getBestQuantity() {
            let bestQuantity = 1;
            let bestUnitPrice = Infinity;
            
            Object.entries(this.analysisResults).forEach(([quantity, result]) => {
                if (result && result.finalUnitPrice < bestUnitPrice) {
                    bestUnitPrice = result.finalUnitPrice;
                    bestQuantity = parseInt(quantity);
                }
            });
            
            return bestQuantity;
        }

        updateDetailPanel(bestResult, bestQuantity) {
            // 实现详细面板更新逻辑
            // 这里可以添加更多详细信息的更新
            console.log('[JD优惠分析] 更新详细面板:', bestResult, bestQuantity);
        }

        copyPromotionText() {
            const bestQuantity = this.getBestQuantity();
            const result = this.analysisResults[bestQuantity];
            
            if (!result) {
                alert('暂无优惠数据可复制');
                return;
            }

            const text = this.generatePromotionText(result, bestQuantity);
            
            navigator.clipboard.writeText(text).then(() => {
                alert('优惠文案已复制到剪贴板');
            }).catch(() => {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('优惠文案已复制到剪贴板');
            });
        }

        generatePromotionText(result, quantity) {
            const savings = (result.originalPrice * quantity) - result.finalPrice;
            
            return `🎉 京东优惠分析
📦 最优购买：${quantity}件
💰 到手单价：¥${result.finalUnitPrice.toFixed(2)}
💵 到手总价：¥${result.finalPrice.toFixed(2)}
🎁 节省金额：¥${savings.toFixed(2)}`;
        }

        addToCart() {
            const bestQuantity = this.getBestQuantity();
            console.log(`[JD优惠分析] 准备加车 ${bestQuantity} 件商品`);
            alert(`准备加车 ${bestQuantity} 件商品（功能开发中）`);
        }

        destroy() {
            if (this.dataMonitorInterval) {
                clearInterval(this.dataMonitorInterval);
            }
        }
    }

    // 初始化函数
    async function initFloatingPanel() {
        try {
            // 等待DOM加载完成
            if (document.readyState === 'loading') {
                await new Promise(resolve => {
                    document.addEventListener('DOMContentLoaded', resolve);
                });
            }

            // 注入悬浮窗
            injectFloatingPanel();

            // 等待一小段时间确保DOM元素已插入
            await new Promise(resolve => setTimeout(resolve, 100));

            // 初始化悬浮面板
            window.jdPromotionPanel = new JDPromotionFloatingPanel();
            
            console.log('[JD优惠分析] 🎉 悬浮窗初始化完成');

        } catch (error) {
            console.error('[JD优惠分析] ❌ 初始化失败:', error);
        }
    }

    // 启动初始化
    initFloatingPanel();

})();
