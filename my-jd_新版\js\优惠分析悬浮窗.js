// 优惠分析悬浮窗 - 集成优惠算法模块
(function() {
    'use strict';

    // 防止重复注入
    if (window.jdPromotionFloatingPanelLoaded) {
        return;
    }
    window.jdPromotionFloatingPanelLoaded = true;

    console.log('[JD优惠分析] 🚀 悬浮窗脚本开始加载');

    // 等待优惠算法模块加载
    function waitForCalculator() {
        return new Promise((resolve) => {
            // 检查多种可能的全局变量位置
            function checkCalculator() {
                return window.JDPromotionCalculator ||
                       (typeof JDPromotionCalculator !== 'undefined' ? JDPromotionCalculator : null);
            }

            const calculator = checkCalculator();
            if (calculator) {
                console.log('[JD优惠分析] ✅ 优惠算法模块已存在');
                resolve(calculator);
                return;
            }

            let attempts = 0;
            const maxAttempts = 50; // 5秒超时

            const checkInterval = setInterval(() => {
                attempts++;
                const calc = checkCalculator();

                if (calc) {
                    clearInterval(checkInterval);
                    console.log('[JD优惠分析] ✅ 优惠算法模块加载成功');
                    resolve(calc);
                } else if (attempts >= maxAttempts) {
                    clearInterval(checkInterval);
                    console.error('[JD优惠分析] ❌ 优惠算法模块加载超时');
                    // 尝试动态加载
                    loadCalculatorModule().then(resolve);
                }
            }, 100);
        });
    }

    // 动态加载优惠算法模块
    function loadCalculatorModule() {
        return new Promise((resolve) => {
            console.log('[JD优惠分析] 🔄 尝试动态加载优惠算法模块...');

            // 检查是否已经有script标签
            const existingScript = document.querySelector('script[src*="优惠算法模块"]');
            if (existingScript) {
                console.log('[JD优惠分析] 📜 优惠算法模块脚本已存在');
                // 等待一下再检查
                setTimeout(() => {
                    const calc = window.JDPromotionCalculator || (typeof JDPromotionCalculator !== 'undefined' ? JDPromotionCalculator : null);
                    resolve(calc);
                }, 500);
                return;
            }

            // 创建script标签加载模块
            const script = document.createElement('script');
            script.src = chrome.runtime.getURL('js/优惠算法模块.js');
            script.onload = () => {
                console.log('[JD优惠分析] ✅ 优惠算法模块脚本加载完成');
                setTimeout(() => {
                    const calc = window.JDPromotionCalculator || (typeof JDPromotionCalculator !== 'undefined' ? JDPromotionCalculator : null);
                    if (calc) {
                        console.log('[JD优惠分析] ✅ 优惠算法模块类可用');
                    } else {
                        console.error('[JD优惠分析] ❌ 优惠算法模块类仍不可用');
                    }
                    resolve(calc);
                }, 100);
            };
            script.onerror = () => {
                console.error('[JD优惠分析] ❌ 优惠算法模块脚本加载失败');
                resolve(null);
            };

            document.head.appendChild(script);
        });
    }

    // 注入悬浮窗HTML和CSS
    function injectFloatingPanel() {
        // 注入CSS
        const cssLink = document.createElement('link');
        cssLink.rel = 'stylesheet';
        cssLink.href = chrome.runtime.getURL('styles/purchase-analysis.css');
        document.head.appendChild(cssLink);

        // 创建悬浮窗HTML
        const floatingPanelHTML = `
            <div id="purchase-analysis-root-container">
                <!-- 简化的仪表板 - 点击展开详细分析 -->
                <div id="dashboard-container">
                    <div class="dashboard-header">
                        <span>优惠分析</span>
                        <button id="toggle-dashboard" style="background: none; border: none; color: white; cursor: pointer;">▼</button>
                    </div>
                    <div class="dashboard-content">
                        <div class="dashboard-item">
                            <span class="dashboard-label">最优单价</span>
                            <span class="dashboard-value" id="dashboard-best-price">计算中...</span>
                        </div>
                        <div class="dashboard-item">
                            <span class="dashboard-label">最优购买数</span>
                            <span class="dashboard-value" id="dashboard-best-quantity">计算中...</span>
                        </div>
                        <div class="dashboard-item">
                            <span class="dashboard-label">最优总价</span>
                            <span class="dashboard-value" id="dashboard-total-price">计算中...</span>
                        </div>
                        <div class="dashboard-item">
                            <span class="dashboard-label">节省金额</span>
                            <span class="dashboard-value" id="dashboard-savings">计算中...</span>
                        </div>
                    </div>
                </div>

                <!-- 详细分析面板 - 默认隐藏 -->
                <div id="purchase-analysis-container" class="hidden">
                    <div class="header">
                        <span>购买分析报告</span>
                        <div>
                            <button id="copy-text-btn">复制文案</button>
                            <button id="add-to-cart-btn">加车购买</button>
                        </div>
                        <button id="close-analysis-btn">×</button>
                    </div>
                    <div class="content">
                        <div class="summary">
                            <div class="summary-item">
                                <p>最优单价/原价</p>
                                <p><span id="best-price">¥0.00</span> / <span id="original-price">¥0.00</span></p>
                            </div>
                            <div class="summary-item">
                                <p>最优购买数</p>
                                <p><span id="best-quantity">×1</span> / <a href="#" id="quick-add-cart">快速加车</a></p>
                            </div>
                            <div class="summary-item">
                                <p>最优总价/原价</p>
                                <p><span id="total-best-price">¥0.00</span> / <span id="total-original-price">¥0.00</span></p>
                            </div>
                        </div>
                        
                        <div class="sub-summary">
                            <span id="unit-price-info">单位价格信息计算中...</span>
                        </div>

                        <div class="promotions">
                            <p>参与促销&优惠券:</p>
                            <ul id="promotion-list">
                                <li>正在分析优惠信息...</li>
                            </ul>
                        </div>

                        <div class="details">
                            <div class="tabs">
                                <button class="tab-link active">详细分析表</button>
                                <div class="switch-view">
                                    数量范围:
                                    <select id="quantity-range">
                                        <option value="1-10">1-10件</option>
                                        <option value="1-20">1-20件</option>
                                        <option value="1-50">1-50件</option>
                                    </select>
                                </div>
                            </div>
                            <div id="details-table" class="tab-content">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>数量</th>
                                            <th>到手单价</th>
                                            <th>到手总价</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="analysis-table-body">
                                        <tr>
                                            <td colspan="4" style="text-align: center;">正在计算优惠信息...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 将HTML插入到页面中
        const container = document.createElement('div');
        container.innerHTML = floatingPanelHTML;
        document.body.appendChild(container.firstElementChild);

        console.log('[JD优惠分析] ✅ 悬浮窗HTML已注入');
    }

    // 京东优惠分析悬浮窗主逻辑类
    class JDPromotionFloatingPanel {
        constructor() {
            this.calculator = null;
            this.currentProductData = null;
            this.analysisResults = {};
            this.isDetailPanelVisible = false;
            this.dataMonitorInterval = null;
            
            this.init();
        }

        async init() {
            // 等待优惠算法模块加载
            const CalculatorClass = await waitForCalculator();

            if (CalculatorClass) {
                try {
                    this.calculator = new CalculatorClass();
                    console.log('[JD优惠分析] ✅ 优惠算法模块已加载并实例化');
                } catch (error) {
                    console.error('[JD优惠分析] ❌ 优惠算法模块实例化失败:', error);
                    this.showError('优惠算法模块实例化失败');
                    return;
                }
            } else {
                console.error('[JD优惠分析] ❌ 优惠算法模块加载失败');
                this.showError('优惠算法模块加载失败');
                return;
            }

            this.bindEvents();
            this.makeContainerDraggable();
            this.startProductDataMonitoring();
        }

        bindEvents() {
            // 仪表板点击展开/收起
            const dashboardContainer = document.getElementById('dashboard-container');
            if (dashboardContainer) {
                dashboardContainer.addEventListener('click', (e) => {
                    if (e.target.id !== 'toggle-dashboard') {
                        this.toggleDetailPanel();
                    }
                });
            }

            // 切换按钮
            const toggleBtn = document.getElementById('toggle-dashboard');
            if (toggleBtn) {
                toggleBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.toggleDetailPanel();
                });
            }

            // 关闭按钮
            const closeBtn = document.getElementById('close-analysis-btn');
            if (closeBtn) {
                closeBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.hideDetailPanel();
                });
            }

            // 复制文案按钮
            const copyBtn = document.getElementById('copy-text-btn');
            if (copyBtn) {
                copyBtn.addEventListener('click', () => {
                    this.copyPromotionText();
                });
            }

            // 加车购买按钮
            const addCartBtn = document.getElementById('add-to-cart-btn');
            if (addCartBtn) {
                addCartBtn.addEventListener('click', () => {
                    this.addToCart();
                });
            }

            // 数量范围选择
            const quantityRange = document.getElementById('quantity-range');
            if (quantityRange) {
                quantityRange.addEventListener('change', (e) => {
                    this.updateAnalysisTable(e.target.value);
                });
            }
        }

        makeContainerDraggable() {
            const container = document.getElementById('purchase-analysis-root-container');
            const header = document.querySelector('.dashboard-header');
            
            if (!container || !header) return;

            let isDragging = false;
            let currentX;
            let currentY;
            let initialX;
            let initialY;
            let xOffset = 0;
            let yOffset = 0;

            header.addEventListener('mousedown', (e) => {
                if (e.target.tagName === 'BUTTON') return;
                
                initialX = e.clientX - xOffset;
                initialY = e.clientY - yOffset;
                isDragging = true;
                header.style.cursor = 'grabbing';
            });

            document.addEventListener('mousemove', (e) => {
                if (isDragging) {
                    e.preventDefault();
                    currentX = e.clientX - initialX;
                    currentY = e.clientY - initialY;
                    xOffset = currentX;
                    yOffset = currentY;
                    
                    container.style.transform = `translate(${currentX}px, ${currentY}px)`;
                }
            });

            document.addEventListener('mouseup', () => {
                isDragging = false;
                header.style.cursor = 'grab';
            });
        }

        startProductDataMonitoring() {
            // 立即检测一次
            this.detectProductData();
            
            // 定期检查页面变化
            this.dataMonitorInterval = setInterval(() => {
                this.detectProductData();
            }, 3000);

            // 监听全局数据事件（如果有的话）
            window.addEventListener('jd-product-data-updated', (event) => {
                console.log('[JD优惠分析] 📡 接收到产品数据更新事件:', event.detail);
                if (event.detail && event.detail.data) {
                    this.currentProductData = event.detail.data;
                    this.calculatePromotions();
                }
            });
        }

        detectProductData() {
            try {
                // 方法1: 尝试从全局变量获取
                const globalSources = [
                    () => window.pageConfig?.product,
                    () => window._JdJrTdRiskFpInfo,
                    () => window.skuObj,
                    () => window.cat && window.cat[0] ? { skuId: window.cat[0] } : null
                ];

                for (const getSource of globalSources) {
                    try {
                        const productData = getSource();
                        if (productData && this.isValidProductData(productData)) {
                            if (JSON.stringify(productData) !== JSON.stringify(this.currentProductData)) {
                                console.log('[JD优惠分析] 📡 从全局变量获取到产品数据');
                                this.currentProductData = productData;
                                this.calculatePromotions();
                                return;
                            }
                        }
                    } catch (e) {
                        // 忽略单个数据源的错误
                    }
                }

                // 方法2: 尝试从页面元素提取基本信息
                const basicData = this.extractBasicProductData();
                if (basicData && basicData.price && basicData.price.p > 0) {
                    const dataChanged = !this.currentProductData ||
                                      this.currentProductData.price?.p !== basicData.price.p;

                    if (dataChanged) {
                        console.log('[JD优惠分析] 📡 从页面元素提取到产品数据');
                        this.currentProductData = basicData;
                        this.calculatePromotions();
                    }
                }
            } catch (error) {
                console.log('[JD优惠分析] 获取商品数据失败:', error);
            }
        }

        isValidProductData(data) {
            return data && (
                (data.price && data.price.p > 0) ||
                (data.skuObj && data.skuObj.price && data.skuObj.price.p > 0) ||
                (data.skuId && typeof data.skuId === 'string')
            );
        }

        extractBasicProductData() {
            // 从京东页面提取基本商品数据
            try {
                // 获取商品价格 - 扩展更多选择器
                const priceSelectors = [
                    '.p-price .price',
                    '.summary-price .p-price .price',
                    '.price-now',
                    '.p-price-now',
                    '.J-p-price .price',
                    '.price .p-price',
                    '[data-price]',
                    '.jd-price',
                    '.current-price'
                ];

                let price = null;
                let priceElement = null;

                for (const selector of priceSelectors) {
                    try {
                        priceElement = document.querySelector(selector);
                        if (priceElement) {
                            let priceText = priceElement.textContent || priceElement.innerText || '';

                            // 如果是data-price属性
                            if (selector === '[data-price]') {
                                priceText = priceElement.getAttribute('data-price') || priceText;
                            }

                            // 清理价格文本，只保留数字和小数点
                            priceText = priceText.replace(/[^\d.]/g, '');
                            price = parseFloat(priceText);

                            if (price && price > 0) {
                                console.log(`[JD优惠分析] 📊 从选择器 "${selector}" 提取到价格: ¥${price}`);
                                break;
                            }
                        }
                    } catch (e) {
                        // 忽略单个选择器的错误
                        continue;
                    }
                }

                if (!price || price <= 0) {
                    console.log('[JD优惠分析] ⚠️ 未能提取到有效价格');
                    return null;
                }

                // 尝试获取SKU ID
                let skuId = null;
                try {
                    // 从URL提取
                    const urlMatch = window.location.href.match(/\/(\d+)\.html/);
                    if (urlMatch) {
                        skuId = urlMatch[1];
                    }

                    // 从全局变量提取
                    if (!skuId && window.pageConfig?.product?.skuId) {
                        skuId = window.pageConfig.product.skuId;
                    }
                } catch (e) {
                    // 忽略SKU提取错误
                }

                // 构造基本的商品数据结构
                const productData = {
                    price: {
                        p: price
                    },
                    skuId: skuId,
                    preferenceInfo: {
                        coupons: [],
                        promotions: []
                    },
                    dataSource: 'page_extraction',
                    extractedAt: Date.now()
                };

                console.log('[JD优惠分析] ✅ 成功提取基本商品数据:', productData);
                return productData;

            } catch (error) {
                console.error('[JD优惠分析] 提取页面数据失败:', error);
                return null;
            }
        }

        calculatePromotions() {
            if (!this.currentProductData) {
                console.log('[JD优惠分析] ⚠️ 没有商品数据，跳过计算');
                this.showError('等待商品数据...');
                return;
            }

            if (!this.calculator) {
                console.log('[JD优惠分析] ⚠️ 计算器未初始化，跳过计算');
                this.showError('计算器未就绪...');
                return;
            }

            try {
                console.log('[JD优惠分析] 🧮 开始计算优惠信息...', this.currentProductData);

                // 验证数据结构
                const price = this.currentProductData.price?.p ||
                             this.currentProductData.skuObj?.price?.p;

                if (!price || price <= 0) {
                    console.error('[JD优惠分析] ❌ 无效的商品价格:', price);
                    this.showError('商品价格无效');
                    return;
                }

                // 计算1-20件的优惠情况
                this.analysisResults = {};
                let bestResult = null;
                let bestQuantity = 1;
                let calculationErrors = 0;

                for (let quantity = 1; quantity <= 20; quantity++) {
                    try {
                        const result = this.calculator.calculateOptimalDiscount(this.currentProductData, quantity);

                        if (result && typeof result.finalUnitPrice === 'number' && result.finalUnitPrice > 0) {
                            this.analysisResults[quantity] = result;

                            // 找到最优购买数量（最低单价）
                            if (!bestResult || result.finalUnitPrice < bestResult.finalUnitPrice) {
                                bestResult = result;
                                bestQuantity = quantity;
                            }
                        } else {
                            console.warn(`[JD优惠分析] ⚠️ 数量 ${quantity} 计算结果异常:`, result);
                            calculationErrors++;
                        }
                    } catch (quantityError) {
                        console.error(`[JD优惠分析] ❌ 数量 ${quantity} 计算失败:`, quantityError);
                        calculationErrors++;
                    }
                }

                if (Object.keys(this.analysisResults).length === 0) {
                    console.error('[JD优惠分析] ❌ 所有数量计算都失败了');
                    this.showError('优惠计算全部失败');
                    return;
                }

                console.log(`[JD优惠分析] ✅ 优惠计算完成，最优数量: ${bestQuantity}, 错误: ${calculationErrors}/20`);
                console.log('[JD优惠分析] 📊 最优结果:', bestResult);

                this.updateDashboard(bestResult, bestQuantity);
                if (this.isDetailPanelVisible) {
                    this.updateDetailPanel(bestResult, bestQuantity);
                }

            } catch (error) {
                console.error('[JD优惠分析] 计算优惠失败:', error);
                this.showError('计算优惠信息失败: ' + error.message);
            }
        }

        updateDashboard(bestResult, bestQuantity) {
            if (!bestResult) return;

            const elements = {
                'dashboard-best-price': `¥${bestResult.finalUnitPrice.toFixed(2)}`,
                'dashboard-best-quantity': `×${bestQuantity}`,
                'dashboard-total-price': `¥${bestResult.finalPrice.toFixed(2)}`,
                'dashboard-savings': `¥${((bestResult.originalPrice * bestQuantity) - bestResult.finalPrice).toFixed(2)}`
            };

            Object.entries(elements).forEach(([id, text]) => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = text;
                }
            });
        }

        showError(message) {
            const errorMappings = {
                'dashboard-best-price': '计算中...',
                'dashboard-best-quantity': '等待中...',
                'dashboard-total-price': '分析中...',
                'dashboard-savings': '处理中...'
            };

            // 根据错误类型显示不同的状态
            if (message.includes('等待') || message.includes('数据')) {
                Object.entries(errorMappings).forEach(([id, text]) => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.textContent = text;
                        element.style.color = '#666';
                    }
                });
            } else {
                // 真正的错误
                Object.keys(errorMappings).forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.textContent = '错误';
                        element.style.color = '#ff0000';
                    }
                });
            }

            console.error('[JD优惠分析] 错误:', message);
        }

        toggleDetailPanel() {
            const panel = document.getElementById('purchase-analysis-container');
            const toggleBtn = document.getElementById('toggle-dashboard');
            
            if (!panel || !toggleBtn) return;

            if (this.isDetailPanelVisible) {
                this.hideDetailPanel();
            } else {
                this.showDetailPanel();
            }
        }

        showDetailPanel() {
            const panel = document.getElementById('purchase-analysis-container');
            const toggleBtn = document.getElementById('toggle-dashboard');
            
            if (panel && toggleBtn) {
                panel.classList.remove('hidden');
                toggleBtn.textContent = '▲';
                this.isDetailPanelVisible = true;
                
                // 更新详细面板内容
                const bestQuantity = this.getBestQuantity();
                const bestResult = this.analysisResults[bestQuantity];
                if (bestResult) {
                    this.updateDetailPanel(bestResult, bestQuantity);
                }
            }
        }

        hideDetailPanel() {
            const panel = document.getElementById('purchase-analysis-container');
            const toggleBtn = document.getElementById('toggle-dashboard');
            
            if (panel && toggleBtn) {
                panel.classList.add('hidden');
                toggleBtn.textContent = '▼';
                this.isDetailPanelVisible = false;
            }
        }

        getBestQuantity() {
            let bestQuantity = 1;
            let bestUnitPrice = Infinity;
            
            Object.entries(this.analysisResults).forEach(([quantity, result]) => {
                if (result && result.finalUnitPrice < bestUnitPrice) {
                    bestUnitPrice = result.finalUnitPrice;
                    bestQuantity = parseInt(quantity);
                }
            });
            
            return bestQuantity;
        }

        updateDetailPanel(bestResult, bestQuantity) {
            // 实现详细面板更新逻辑
            // 这里可以添加更多详细信息的更新
            console.log('[JD优惠分析] 更新详细面板:', bestResult, bestQuantity);
        }

        copyPromotionText() {
            const bestQuantity = this.getBestQuantity();
            const result = this.analysisResults[bestQuantity];
            
            if (!result) {
                alert('暂无优惠数据可复制');
                return;
            }

            const text = this.generatePromotionText(result, bestQuantity);
            
            navigator.clipboard.writeText(text).then(() => {
                alert('优惠文案已复制到剪贴板');
            }).catch(() => {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('优惠文案已复制到剪贴板');
            });
        }

        generatePromotionText(result, quantity) {
            const savings = (result.originalPrice * quantity) - result.finalPrice;
            
            return `🎉 京东优惠分析
📦 最优购买：${quantity}件
💰 到手单价：¥${result.finalUnitPrice.toFixed(2)}
💵 到手总价：¥${result.finalPrice.toFixed(2)}
🎁 节省金额：¥${savings.toFixed(2)}`;
        }

        addToCart() {
            const bestQuantity = this.getBestQuantity();
            console.log(`[JD优惠分析] 准备加车 ${bestQuantity} 件商品`);
            alert(`准备加车 ${bestQuantity} 件商品（功能开发中）`);
        }

        destroy() {
            if (this.dataMonitorInterval) {
                clearInterval(this.dataMonitorInterval);
            }
        }
    }

    // 初始化函数
    async function initFloatingPanel() {
        try {
            console.log('[JD优惠分析] 🚀 开始初始化悬浮窗...');

            // 等待DOM加载完成
            if (document.readyState === 'loading') {
                console.log('[JD优惠分析] ⏳ 等待DOM加载完成...');
                await new Promise(resolve => {
                    document.addEventListener('DOMContentLoaded', resolve);
                });
            }

            // 检查是否在京东商品页面
            if (!window.location.href.includes('item.jd.com') &&
                !window.location.href.includes('item.jd.hk')) {
                console.log('[JD优惠分析] ℹ️ 非京东商品页面，跳过初始化');
                return;
            }

            console.log('[JD优惠分析] 🔧 注入悬浮窗HTML...');
            // 注入悬浮窗
            injectFloatingPanel();

            // 等待一小段时间确保DOM元素已插入
            await new Promise(resolve => setTimeout(resolve, 200));

            // 检查悬浮窗是否成功注入
            const container = document.getElementById('purchase-analysis-root-container');
            if (!container) {
                console.error('[JD优惠分析] ❌ 悬浮窗HTML注入失败');
                return;
            }

            console.log('[JD优惠分析] 🎛️ 初始化悬浮面板逻辑...');
            // 初始化悬浮面板
            window.jdPromotionPanel = new JDPromotionFloatingPanel();

            console.log('[JD优惠分析] 🎉 悬浮窗初始化完成');

        } catch (error) {
            console.error('[JD优惠分析] ❌ 初始化失败:', error);
        }
    }

    // 启动初始化
    initFloatingPanel();

})();
