// 优惠分析悬浮窗 - 简化版（用于调试）
(function() {
    'use strict';

    // 防止重复注入
    if (window.jdPromotionFloatingPanelLoaded) {
        return;
    }
    window.jdPromotionFloatingPanelLoaded = true;

    console.log('[JD优惠分析-简化版] 🚀 悬浮窗脚本开始加载');

    // 注入悬浮窗HTML和CSS
    function injectFloatingPanel() {
        console.log('[JD优惠分析-简化版] 🔧 开始注入悬浮窗...');
        
        // 注入CSS
        const cssLink = document.createElement('link');
        cssLink.rel = 'stylesheet';
        cssLink.href = chrome.runtime.getURL('styles/purchase-analysis.css');
        document.head.appendChild(cssLink);
        console.log('[JD优惠分析-简化版] ✅ CSS已注入');

        // 创建简化的悬浮窗HTML
        const floatingPanelHTML = `
            <div id="purchase-analysis-root-container" style="position: fixed; top: 20px; right: 20px; z-index: 10000; background: white; border: 1px solid #ccc; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); font-family: Arial, sans-serif; width: 220px;">
                <div id="dashboard-container">
                    <div class="dashboard-header" style="background: #007bff; color: white; padding: 8px 12px; border-radius: 8px 8px 0 0; cursor: grab; user-select: none;">
                        <span>京东优惠分析</span>
                        <button id="toggle-dashboard" style="float: right; background: none; border: none; color: white; cursor: pointer;">▼</button>
                    </div>
                    <div class="dashboard-content" style="padding: 12px;">
                        <div class="dashboard-item" style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                            <span class="dashboard-label" style="font-size: 12px; color: #666;">商品价格</span>
                            <span class="dashboard-value" id="dashboard-price" style="font-size: 12px; font-weight: bold; color: #333;">检测中...</span>
                        </div>
                        <div class="dashboard-item" style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                            <span class="dashboard-label" style="font-size: 12px; color: #666;">计算状态</span>
                            <span class="dashboard-value" id="dashboard-status" style="font-size: 12px; font-weight: bold; color: #333;">初始化中...</span>
                        </div>
                        <div class="dashboard-item" style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                            <span class="dashboard-label" style="font-size: 12px; color: #666;">最优单价</span>
                            <span class="dashboard-value" id="dashboard-best-price" style="font-size: 12px; font-weight: bold; color: #007bff;">等待中...</span>
                        </div>
                        <div class="dashboard-item" style="display: flex; justify-content: space-between;">
                            <span class="dashboard-label" style="font-size: 12px; color: #666;">推荐数量</span>
                            <span class="dashboard-value" id="dashboard-best-quantity" style="font-size: 12px; font-weight: bold; color: #007bff;">等待中...</span>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 将HTML插入到页面中
        const container = document.createElement('div');
        container.innerHTML = floatingPanelHTML;
        document.body.appendChild(container.firstElementChild);

        console.log('[JD优惠分析-简化版] ✅ 悬浮窗HTML已注入');
    }

    // 简化的悬浮窗控制类
    class SimpleJDPromotionPanel {
        constructor() {
            this.calculator = null;
            this.currentPrice = null;
            this.isCalculatorReady = false;
            
            this.init();
        }

        async init() {
            console.log('[JD优惠分析-简化版] 🎛️ 开始初始化...');
            
            this.updateStatus('正在加载模块...');
            
            // 等待优惠算法模块
            await this.waitForCalculator();
            
            // 绑定事件
            this.bindEvents();
            
            // 开始监控数据
            this.startDataMonitoring();
            
            console.log('[JD优惠分析-简化版] ✅ 初始化完成');
        }

        async waitForCalculator() {
            console.log('[JD优惠分析-简化版] ⏳ 等待优惠算法模块...');
            
            // 检查是否已经存在
            if (window.JDPromotionCalculator) {
                this.calculator = new window.JDPromotionCalculator();
                this.isCalculatorReady = true;
                this.updateStatus('模块已就绪');
                console.log('[JD优惠分析-简化版] ✅ 优惠算法模块已存在');
                return;
            }

            // 等待模块加载
            let attempts = 0;
            const maxAttempts = 30;
            
            while (attempts < maxAttempts) {
                await new Promise(resolve => setTimeout(resolve, 200));
                attempts++;
                
                if (window.JDPromotionCalculator) {
                    try {
                        this.calculator = new window.JDPromotionCalculator();
                        this.isCalculatorReady = true;
                        this.updateStatus('模块已就绪');
                        console.log('[JD优惠分析-简化版] ✅ 优惠算法模块加载成功');
                        return;
                    } catch (error) {
                        console.error('[JD优惠分析-简化版] ❌ 模块实例化失败:', error);
                        this.updateStatus('模块错误');
                        return;
                    }
                }
            }
            
            console.error('[JD优惠分析-简化版] ❌ 优惠算法模块加载超时');
            this.updateStatus('模块加载失败');
        }

        bindEvents() {
            // 拖拽功能
            const header = document.querySelector('.dashboard-header');
            const container = document.getElementById('purchase-analysis-root-container');
            
            if (header && container) {
                let isDragging = false;
                let currentX, currentY, initialX, initialY, xOffset = 0, yOffset = 0;

                header.addEventListener('mousedown', (e) => {
                    if (e.target.tagName === 'BUTTON') return;
                    
                    initialX = e.clientX - xOffset;
                    initialY = e.clientY - yOffset;
                    isDragging = true;
                    header.style.cursor = 'grabbing';
                });

                document.addEventListener('mousemove', (e) => {
                    if (isDragging) {
                        e.preventDefault();
                        currentX = e.clientX - initialX;
                        currentY = e.clientY - initialY;
                        xOffset = currentX;
                        yOffset = currentY;
                        
                        container.style.transform = `translate(${currentX}px, ${currentY}px)`;
                    }
                });

                document.addEventListener('mouseup', () => {
                    isDragging = false;
                    header.style.cursor = 'grab';
                });
            }
        }

        startDataMonitoring() {
            console.log('[JD优惠分析-简化版] 📡 开始监控商品数据...');
            
            // 立即检测一次
            this.detectProductData();
            
            // 定期检测
            setInterval(() => {
                this.detectProductData();
            }, 3000);
        }

        detectProductData() {
            try {
                // 尝试从页面提取价格
                const priceSelectors = [
                    '.p-price .price',
                    '.summary-price .p-price .price',
                    '.price-now',
                    '.J-p-price .price'
                ];
                
                let price = null;
                for (const selector of priceSelectors) {
                    const element = document.querySelector(selector);
                    if (element) {
                        const priceText = element.textContent.replace(/[^\d.]/g, '');
                        price = parseFloat(priceText);
                        if (price && price > 0) {
                            break;
                        }
                    }
                }

                if (price && price !== this.currentPrice) {
                    this.currentPrice = price;
                    this.updatePrice(price);
                    
                    if (this.isCalculatorReady) {
                        this.calculateOptimal(price);
                    } else {
                        this.updateStatus('等待模块...');
                    }
                }
            } catch (error) {
                console.error('[JD优惠分析-简化版] 数据检测失败:', error);
            }
        }

        calculateOptimal(price) {
            if (!this.calculator) {
                this.updateStatus('计算器未就绪');
                return;
            }

            try {
                this.updateStatus('计算中...');
                
                // 构造简单的商品数据
                const productData = {
                    price: { p: price },
                    preferenceInfo: {
                        coupons: [],
                        promotions: []
                    }
                };

                // 计算1-10件的最优方案
                let bestPrice = price;
                let bestQuantity = 1;

                for (let quantity = 1; quantity <= 10; quantity++) {
                    try {
                        const result = this.calculator.calculateOptimalDiscount(productData, quantity);
                        if (result && result.finalUnitPrice < bestPrice) {
                            bestPrice = result.finalUnitPrice;
                            bestQuantity = quantity;
                        }
                    } catch (calcError) {
                        console.warn(`[JD优惠分析-简化版] 数量 ${quantity} 计算失败:`, calcError);
                    }
                }

                this.updateResults(bestPrice, bestQuantity);
                this.updateStatus('计算完成');
                
                console.log(`[JD优惠分析-简化版] ✅ 计算完成: 最优单价 ¥${bestPrice.toFixed(2)}, 数量 ${bestQuantity}`);
                
            } catch (error) {
                console.error('[JD优惠分析-简化版] 计算失败:', error);
                this.updateStatus('计算失败');
            }
        }

        updatePrice(price) {
            const element = document.getElementById('dashboard-price');
            if (element) {
                element.textContent = `¥${price.toFixed(2)}`;
                element.style.color = '#333';
            }
        }

        updateStatus(status) {
            const element = document.getElementById('dashboard-status');
            if (element) {
                element.textContent = status;
            }
        }

        updateResults(bestPrice, bestQuantity) {
            const priceElement = document.getElementById('dashboard-best-price');
            const quantityElement = document.getElementById('dashboard-best-quantity');
            
            if (priceElement) {
                priceElement.textContent = `¥${bestPrice.toFixed(2)}`;
                priceElement.style.color = '#28a745';
            }
            
            if (quantityElement) {
                quantityElement.textContent = `×${bestQuantity}`;
                quantityElement.style.color = '#28a745';
            }
        }
    }

    // 初始化函数
    async function initSimplePanel() {
        try {
            console.log('[JD优惠分析-简化版] 🚀 开始初始化...');
            
            // 等待DOM
            if (document.readyState === 'loading') {
                await new Promise(resolve => {
                    document.addEventListener('DOMContentLoaded', resolve);
                });
            }

            // 检查页面
            if (!window.location.href.includes('item.jd.com') && 
                !window.location.href.includes('item.jd.hk')) {
                console.log('[JD优惠分析-简化版] ℹ️ 非京东商品页面');
                return;
            }

            // 注入悬浮窗
            injectFloatingPanel();
            
            // 等待DOM插入
            await new Promise(resolve => setTimeout(resolve, 100));
            
            // 初始化面板
            window.simpleJdPromotionPanel = new SimpleJDPromotionPanel();
            
            console.log('[JD优惠分析-简化版] 🎉 初始化完成');
            
        } catch (error) {
            console.error('[JD优惠分析-简化版] ❌ 初始化失败:', error);
        }
    }

    // 启动
    initSimplePanel();

})();
