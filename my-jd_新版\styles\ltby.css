/* 基础容器样式 */
.ltby-container {
    margin: 0;
    padding: 0;
    width: 100%;
    color: #666;
    font: 12px/150% tahoma,arial,Microsoft YaHei,Hiragino Sans GB,"\u5b8b\u4f53",sans-serif;
}

/* 统一「购买分析」和「快速复制」容器样式 */
.ltby-link-group {
    display: flex;
    align-items: flex-start;
    padding: 8px 12px;  /* 统一内边距 */
    border-bottom: 1px solid rgba(0,0,0,0.03);
}

.ltby-link-group:last-child {
    border-bottom: none;
}

.ltby-link-group-title {
    min-width: 56px;  /* 固定标题宽度 */
    margin-right: 10px;
    color: #666;
    line-height: 24px;
}

.ltby-link-group ul {
    flex: 1;
    margin: 0;
    padding: 0;
    list-style: none;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
    min-height: 26px; /* 确保最小高度 */
}

.ltby-link-group ul li {
    height: 26px; /* 统一高度 */
    line-height: 26px;
    margin: 0;
    padding: 0;
    white-space: nowrap;
    display: flex;
    align-items: center;
}

/* 特殊处理包含按钮的li */
.ltby-link-group ul li:has(#focus-product-btn) {
    height: 26px;
    padding: 2px 0;
}

/* 复制链接相关样式 */
.ltby-copy-link {
    color: #1976d2 !important;  /* 修复语法错误，将!重要改为!important */
    cursor: pointer;
    font-size: 12px;
    line-height: 24px;
    height: 24px;
    padding: 0 4px;
    text-decoration: none;
    background: none !important;  /* 修复语法错误 */
    transition: color 0.2s;
}

.ltby-copy-link:hover {
    color: #4096ff !important;  /* 修复语法错误 */
    text-decoration: underline;
}

/* 复制提示样式 */
.ltby-copy-tip {
    position: fixed;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 2px;
    font-size: 12px;
    pointer-events: none;
    z-index: 9999;
}

/* 更新按钮样式 */
#update-market-price-btn {
    background-color: #1890ff;
    color: white;
    border: 1px solid #1890ff;
    padding: 0 10px;
    height: 24px;
    line-height: 22px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
    box-sizing: border-box;
    font-weight: normal;
}

#update-market-price-btn:hover {
    background-color: #40a9ff;
    border-color: #40a9ff;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}

#update-market-price-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(24, 144, 255, 0.3);
}

#new-market-price {
    width: 80px;
    height: 24px;
    padding: 0 6px;
    border: 1px solid #d9d9d9;
    border-radius: 3px;
    font-size: 12px;
    margin: 0; /* 移除间距，使用gap控制 */
    box-sizing: border-box;
    transition: all 0.2s ease;
}

#new-market-price:focus {
    border-color: #40a9ff;
    outline: none;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 更新时间和关注按钮同行显示优化 */

/* 更新时间样式 */
#update-time {
    font-size: 12px;
    color: #1890ff;
    font-weight: normal;
    margin-right: 8px;
    line-height: 24px; /* 与按钮高度对齐 */
}

/* 关注按钮样式优化 - 增强选择器优先级 */
button.focus-btn {
    /* 未关注状态：淡绿色 */
    background-color: #f6ffed !important;
    color: #52c41a !important;
    border: 1px solid #b7eb8f !important;
    padding: 0 8px;
    height: 24px;
    line-height: 22px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
    box-sizing: border-box;
    vertical-align: middle;
    font-weight: normal !important;
}

button.focus-btn:hover {
    background-color: #d9f7be !important;
    border-color: #95de64 !important;
    color: #389e0d !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(82, 196, 26, 0.2);
}

button.focus-btn.focused {
    /* 已关注状态：红色 */
    background-color: #fff2f0 !important;
    border-color: #ffccc7 !important;
    color: #ff4d4f !important;
    font-weight: 500 !important;
}

button.focus-btn.focused:hover {
    background-color: #ffe7e6 !important;
    border-color: #ffa39e !important;
    color: #cf1322 !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(255, 77, 79, 0.2);
}

/* 修正二维码容器宽度和定位，适配左侧图片区 */
.ltby-qrcode-container {
    width: 100%;
    max-width: 352px;
    margin: 12px auto 0 auto;
    padding: 0;
    background: transparent;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    position: static;
    z-index: 1;
}

/* 靠左显示二维码容器 */
.ltby-qrcode-container.ltby-qrcode-align-left {
    width: 352px;
    max-width: 100%;
    margin: 12px 0 0 0;
    padding: 0;
    background: transparent;
    box-sizing: border-box;
    display: block;
    position: static;
    z-index: 1;
}

.ltby-qrcode-wrap {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 0;
    margin: 0;
    background: transparent;
}

.ltby-qrcode-item {
    width: 160px;
    min-width: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

/* 二维码标题 */
.qrcode-title {
    font-size: 12px;
    color: #666;
    line-height: 20px;
    margin-bottom: 8px;
    text-align: center;
}

/* 二维码框 */
.qrcode-box {
    width: 134px;
    height: 134px;
    padding: 8px;
    background: #fff;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: content-box;
}

/* 二维码图像 */
.qrcode-box canvas,
.qrcode-box img {
    width: 134px;
    height: 134px;
    display: block;
}

/* Ant Design 风格按钮样式 */
.ant-btn {
    --drip-wave-shadow-color: #fa2c19;
    --scroll-bar: 0;
    -webkit-font-smoothing: antialiased;
    font-family: -apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';
    box-sizing: border-box;
    outline: none;
    position: relative;
    display: inline-flex;
    gap: 8px;
    align-items: center;
    justify-content: center;
    font-weight: 400;
    white-space: nowrap;
    text-align: center;
    border: 1px solid transparent;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    user-select: none;
    touch-action: manipulation;
}

.ant-btn-primary {
    background: #1677ff;
    color: #fff;
    box-shadow: 0 2px 0 rgba(5,145,255,0.1);
}

.ant-btn-primary:hover {
    background: #4096ff;
}

.ant-btn-sm {
    font-size: 14px;
    height: 24px;
    width: 72px;
    padding: 0;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ant-btn-sm span {
    display: inline-block;
    width: 56px;
    height: 19px;
    line-height: 19px;
    text-align: center;
}

.ant-space {
    display: inline-flex;
    align-items: center;
}

.ant-space-gap-row-small {
    gap: 8px;
}

.css-240cud {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

.ant-flex {
    display: flex;
}

.ant-flex-align-center {
    align-items: center;
}

.ant-avatar {
    position: relative;
    display: inline-block;
    overflow: hidden;
    color: #fff;
    white-space: nowrap;
    background: #ccc;
    border-radius: 2px;
}

.ant-avatar-image {
    background: transparent;
}

.ltby-icon-wrap {
    --drip-wave-shadow-color: #fa2c19;
    --scroll-bar: 0;
    font: 12px/150% tahoma,arial,Microsoft YaHei,Hiragino Sans GB,"\u5b8b\u4f53",sans-serif;
    -webkit-font-smoothing: antialiased;
    color: #666;
    display: flex;
    margin: 0;
    padding: 0;
    align-items: center;
}

.ltby-icon-wrap .ant-avatar {
    width: 22px;
    height: 22px;
    margin: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 快速复制区域样式 */
.quick-copy-group {
    margin: 0;
    padding: 0;  /* 移除所有内边距 */
    display: flex;
    align-items: flex-start;
    background: none;
}

/* 可选的渐变动画 */
@keyframes fadeInOut {
    0% { opacity: 0; transform: translateY(10px); }
    20% { opacity: 1; transform: translateY(0); }
    80% { opacity: 1; transform: translateY(0); }
    100% { opacity: 0; transform: translateY(-10px); }
}

/* 内容包装器样式 */
.ltby-content-wrapper {
    border: 1px solid rgba(0, 0, 0, 0.06);
    border-radius: 4px;
    margin: 5px 12px;  /* 减小外边距 */
    width: calc(100% - 24px);  /* 相应调整宽度计算 */
    box-sizing: border-box;
    background: #fff;
    flex: 1;
    padding: 0 0 0 12px; /* 左侧添加内边距 */
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

/* 调整内部组件边距,保持内部间距合理 */
.ltby-content-wrapper .ltby-link-group {
    margin: 0;
    padding: 8px 24px 8px 0; /* 移除左内边距，保留右内边距 */
    border-bottom: 1px solid rgba(0, 0, 0, 0.02);
}

.ltby-content-wrapper .ltby-link-group:last-child {
    border-bottom: none;
    padding-bottom: 12px;
}

/* 添加价格显示样式 */
[data-id="market-price"] {
    color: #f04848;
    font-weight: bold;
    font-size: 14px;
    position: relative;
    cursor: help;
}

[data-id="market-price"]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    padding: 4px 8px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
}

[data-id="estimated-profit"] {
    padding: 0 8px;
    border-radius: 2px;
    font-weight: 500;
}

[data-id="estimated-profit"].profit-positive {
    background-color: rgba(255, 95, 87, 0.1);
    color: #ff5f57;
}

[data-id="estimated-profit"].profit-negative {
    background-color: rgba(82, 196, 26, 0.1);
    color: #52c41a;
}

/* 容器包装样式 */
.ltby-container-wrap {
    border: 1px solid #e6e6e6;
    border-radius: 4px;
    padding: 8px 0;  /* 移除左右内边距 */
    margin: 3px 0;  /* 减小上下间距，移除左右边距 */
    background-color: #fff;
    box-shadow: 0 1px 2px rgba(0,0,0,0.02);
}

.ltby-container-wrap .ltby-link-group {
    margin: 0;  /* 移除所有外边距 */
    padding: 0;  /* 移除所有内边距 */
}

/* 内容盒子统一样式 */
.ltby-content-box {
    border: 1px solid #e6e6e6;
    border-radius: 4px;
    background: #fff;
    margin: 3px 0;
    padding: 0;  /* 移除默认内边距 */
    box-shadow: 0 1px 2px rgba(0,0,0,0.02);
}

.ltby-content-box .ltby-link-group {
    margin: 0;
    padding: 8px 12px;  /* 修改：统一内边距 */
    border-bottom: 1px solid rgba(0,0,0,0.03);
    justify-content: flex-start;
}

.ltby-content-box .ltby-link-group:last-child {
    border-bottom: none;
}

/* 一键购买按钮样式 */
.ltby-quick-buy-container {
    margin: 10px 0;
    position: relative;
    z-index: 1000;
}

.ltby-quick-buy-wrapper {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px;
    border-radius: 6px;
    border: 1px solid #e8e8e8;
    background: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.06);
}

.ltby-brand {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #666;
    font-size: 12px;
    font-weight: 500;
}

.ltby-brand img {
    width: 18px;
    height: 18px;
    border-radius: 3px;
}

.ltby-buttons {
    display: flex;
    gap: 8px;
}

.ltby-btn {
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 80px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 一键购买按钮 - 淡红色背景 */
.ltby-btn-primary {
    background: linear-gradient(135deg, #ffe6e6, #ffcccc);
    color: #d32f2f;
    border: 1px solid #ffb3b3;
}

.ltby-btn-primary:hover {
    background: linear-gradient(135deg, #ffcccc, #ffb3b3);
    color: #c62828;
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(211, 47, 47, 0.2);
}

.ltby-btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 1px 3px rgba(211, 47, 47, 0.3);
}

/* 活动价购买按钮 - 淡绿色背景 */
.ltby-btn-success {
    background: linear-gradient(135deg, #e8f5e8, #d4edda);
    color: #2e7d32;
    border: 1px solid #c3e6cb;
}

.ltby-btn-success:hover {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #1b5e20;
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(46, 125, 50, 0.2);
}

.ltby-btn-success:active {
    transform: translateY(0);
    box-shadow: 0 1px 3px rgba(46, 125, 50, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .ltby-quick-buy-wrapper {
        flex-direction: column;
        gap: 8px;
        padding: 10px;
    }

    .ltby-buttons {
        width: 100%;
        justify-content: center;
    }

    .ltby-btn {
        flex: 1;
        min-width: 0;
    }
}

/* 加载动画 */
@keyframes ltby-fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.ltby-quick-buy-container {
    animation: ltby-fadeIn 0.3s ease-out;
}

/* 价格走势指示器样式 - 适配京东页面布局 */
.price-trend-indicator {
    display: inline-flex;
    align-items: center;
    padding: 8px 12px;
    margin-right: 15px;
    background: #fff;
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.06);
    font-size: 12px;
    height: 24px;
    line-height: 24px;
    vertical-align: top;
}

.price-trend-indicator:hover {
    border-color: #1890ff;
    box-shadow: 0 4px 12px rgba(24,144,255,0.15);
    transform: translateY(-1px);
}

.trend-content {
    display: flex;
    align-items: center;
    gap: 6px;
    white-space: nowrap;
}

.trend-icon {
    font-size: 14px;
    line-height: 1;
}

.trend-info {
    display: flex;
    flex-direction: column;
    gap: 1px;
}

.trend-text {
    font-weight: 500;
    line-height: 1.2;
    font-size: 12px;
}

.trend-detail {
    font-size: 10px;
    color: #999;
    line-height: 1.2;
}

/* 价格走势图样式 */
.price-trend-chart {
    position: fixed;
    width: 450px;
    background: #fff;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    box-shadow: 0 8px 24px rgba(0,0,0,0.12);
    z-index: 10000;
    padding: 16px;
    font-size: 12px;
}

.price-trend-chart.hidden {
    display: none;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
}

.chart-header h3 {
    margin: 0;
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.chart-stats {
    display: flex;
    gap: 12px;
    font-size: 11px;
    color: #666;
}

.chart-stats span {
    white-space: nowrap;
}

.chart-container {
    margin: 12px 0;
    text-align: center;
}

#price-chart-canvas {
    border: 1px solid #f0f0f0;
    border-radius: 4px;
}

.chart-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 12px;
    padding-top: 8px;
    border-top: 1px solid #f0f0f0;
    font-size: 11px;
}

.current-price {
    font-weight: 500;
}

.price-value {
    color: #ff4d4f;
    font-weight: bold;
}

.data-period {
    color: #999;
}

/* 走势状态颜色 */
.trend-text.rising {
    color: #ff7875 !important;
}

.trend-text.falling {
    color: #52c41a !important;
}

.trend-text.stable {
    color: #1890ff !important;
}

.trend-text.lowest {
    color: #ff4d4f !important;
    animation: pulse 2s infinite;
}

/* 最低价闪烁动画 */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* 适配京东页面的item样式 */
.price-trend-indicator.item {
    margin-right: 15px;
    float: left;
}

/* 价格走势模块样式 - 参考第三方插件的设计 */
.price-trend-ui {
    display: inline-block;
    margin-right: 8px;
    vertical-align: top;
}

/* LTBY 价格走势插件样式 - 完全模仿第三方插件 */
#ltby-price-trend-root {
    display: inline-block;
    margin-right: 8px;
    vertical-align: top;
}

#ltby-price-trend-root .ltby-crx-ant-spin-nested-loading {
    position: relative;
    display: inline-block;
}

#ltby-price-trend-root .ltby-crx-ant-spin-container {
    display: inline-block;
}

#ltby-price-trend-root .acss-mhdbxu {
    display: inline-flex;
    align-items: center;
}

/* 修复缺失的标签样式 */
#ltby-price-trend-root .ltby-crx-ant-tag {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    line-height: 18px;
    border: 1px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    user-select: none;
}

/* 绿色主题 - 区分于第三方的红色 */
#ltby-price-trend-root .ltby-crx-ant-tag-green-inverse {
    background: #f6ffed;
    border-color: #b7eb8f;
    color: #389e0d;
}

#ltby-price-trend-root .ltby-crx-ant-tag:hover {
    opacity: 0.85;
    transform: scale(1.02);
}

/* 图标样式 */
#ltby-price-trend-root .anticon {
    display: inline-flex;
    align-items: center;
    font-size: 14px;
}

#ltby-price-trend-root .anticon svg {
    width: 1em;
    height: 1em;
}

/* 响应式适配 */
@media (max-width: 768px) {
    #ltby-price-trend-root .ltby-crx-ant-tag {
        padding: 1px 6px;
        font-size: 11px;
        gap: 2px;
    }

    #ltby-price-trend-root .anticon {
        font-size: 12px;
    }
}

/* 与第三方插件的兼容性 - 确保并排显示 */
#ltby-price-trend-root + #ks-history-price-root,
#ks-history-price-root + #ltby-price-trend-root {
    margin-left: 8px;
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
    .price-trend-tag {
        background: rgba(255, 255, 255, 0.08);
        border-color: rgba(255, 255, 255, 0.15);
        color: rgba(255, 255, 255, 0.85);
    }

    .price-trend-chart {
        background: #1f1f1f;
        border-color: #303030;
        color: rgba(255, 255, 255, 0.85);
    }
}

/* LTBY 价格走势弹窗样式 - 完全模仿第三方插件 */
.ltby-crx-ant-popover {
    position: fixed;
    z-index: 10000;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: 14px;
    line-height: 1.5715;
    color: rgba(0, 0, 0, 0.88);
    box-sizing: border-box;
}

.ltby-crx-ant-popover-hidden {
    display: none;
}

.ltby-crx-ant-popover-arrow {
    position: absolute;
    z-index: 1;
    display: block;
    width: 16px;
    height: 16px;
    overflow: hidden;
    background: transparent;
    pointer-events: none;
}

.ltby-crx-ant-popover-arrow::before {
    position: absolute;
    top: -7px;
    left: -7px;
    width: 14px;
    height: 14px;
    background: #fff;
    border: 1px solid rgba(0, 0, 0, 0.06);
    border-radius: 2px;
    content: "";
    transform: rotate(45deg);
}

.ltby-crx-ant-popover-content {
    position: relative;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.ltby-crx-ant-popover-inner {
    padding: 12px;
    background: #fff;
}

.ltby-crx-ant-popover-title {
    min-width: 177px;
    margin-bottom: 8px;
    color: rgba(0, 0, 0, 0.88);
    font-weight: 600;
    border-bottom: 1px solid rgba(5, 5, 5, 0.06);
    padding-bottom: 8px;
}

.ltby-crx-ant-popover-inner-content {
    color: rgba(0, 0, 0, 0.88);
}

/* Flex 布局样式 */
.ltby-crx-ant-flex {
    display: flex;
}

.ltby-crx-ant-flex-align-center {
    align-items: center;
}

.ltby-crx-ant-flex-align-stretch {
    align-items: stretch;
}

.ltby-crx-ant-flex-vertical {
    flex-direction: column;
}

/* 头像样式 */
.ltby-crx-ant-avatar {
    position: relative;
    display: inline-block;
    overflow: hidden;
    color: #fff;
    white-space: nowrap;
    text-align: center;
    vertical-align: middle;
    background: #ccc;
    border-radius: 50%;
}

.ltby-crx-ant-avatar-square {
    border-radius: 6px;
}

.ltby-crx-ant-avatar-image {
    background: transparent;
}

.ltby-crx-ant-avatar img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Space 间距组件样式 */
.ltby-crx-ant-space {
    display: inline-flex;
    align-items: flex-start;
}

.ltby-crx-ant-space-horizontal {
    flex-direction: row;
}

.ltby-crx-ant-space-align-center {
    align-items: center;
}

.ltby-crx-ant-space-gap-row-small {
    row-gap: 8px;
}

.ltby-crx-ant-space-gap-col-small {
    column-gap: 8px;
}

.ltby-crx-ant-space-item {
    flex: none;
}

/* 按钮样式 */
.ltby-crx-ant-btn {
    position: relative;
    display: inline-flex;
    gap: 8px;
    align-items: center;
    justify-content: center;
    font-weight: 400;
    white-space: nowrap;
    text-align: center;
    background-image: none;
    border: 1px solid transparent;
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.02);
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    user-select: none;
    touch-action: manipulation;
    height: 32px;
    padding: 4px 15px;
    font-size: 14px;
    border-radius: 6px;
    color: rgba(0, 0, 0, 0.88);
    background: #fff;
    border-color: #d9d9d9;
}

.ltby-crx-ant-btn-sm {
    height: 24px;
    padding: 0px 7px;
    font-size: 12px;
    border-radius: 4px;
}

.ltby-crx-ant-btn:hover {
    color: #4096ff;
    border-color: #4096ff;
}

.ltby-crx-ant-btn.ltby-active {
    color: #1677ff;
    border-color: #1677ff;
    background: #e6f4ff;
}

/* 历史价格内容样式 */
.ltby-hooks-history-price {
    width: 449px;
}

.ltby-hooks-history-price-header {
    margin-bottom: 16px;
    gap: 8px;
}

.ltby-hooks-history-price-header b {
    font-weight: 600;
    color: #ff4d4f;
}

.ltby-hooks-history-price-slider-links {
    margin: 16px 0;
}

.ltby-hooks-history-price-foot {
    margin-top: 16px;
    padding-top: 12px;
    border-top: 1px solid #f0f0f0;
}

.ltby-info {
    margin-bottom: 8px;
}

.ltby-count b {
    font-weight: 600;
    color: #1677ff;
}

.ltby-links a {
    color: #1677ff;
    text-decoration: none;
}

.ltby-links a:hover {
    color: #4096ff;
    text-decoration: underline;
}

.ltby-hint {
    font-size: 12px;
    color: #999;
    margin-top: 8px;
}

/* 图表样式 */
#ltby-price-chart {
    background: #fff;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .ltby-hooks-history-price {
        width: 320px;
    }

    #ltby-price-chart {
        width: 320px;
        height: 200px;
    }

    .ltby-crx-ant-popover {
        font-size: 12px;
    }
}
