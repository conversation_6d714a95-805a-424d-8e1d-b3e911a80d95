/**
 * 重点关注功能模块 - 简化版
 * 独立运行，只需要SKU，直接与服务器同步关注状态
 */

class SimpleFocusManager {
    constructor() {
        this.currentSkuId = null;
        this.isFocused = false;
        this.focusButton = null;
        this.isInitialized = false;
        this.apiBaseUrl = 'https://zzz7.top/api/products';
    }

    async initialize() {
        try {
            // 直接从URL提取SKU（已解耦统一元素提取器）
            this.currentSkuId = this.extractSkuFromUrl();

            if (!this.currentSkuId) {
                return;
            }


            // 从服务器获取关注状态
            await this.loadFocusStatusFromServer();

            // 绑定现有关注按钮
            await this.bindExistingFocusButton();

            this.isInitialized = true;

        } catch (error) {
        }
    }

    /**
     * 从URL提取SKU（备用方案）
     */
    extractSkuFromUrl() {
        // 备用方案：从URL提取
        const url = location.href;
        const patterns = [
            /\/(\d{6,})\.html/,  // 标准格式: /123456.html
            /\/(\d{6,})/,        // 简化格式: /123456
            /skuId[=:](\d{6,})/i // 参数格式: skuId=123456
        ];

        for (const pattern of patterns) {
            const match = url.match(pattern);
            if (match) {
                return match[1];
            }
        }

        return null;
    }

    /**
     * 测试API响应格式
     */
    async testApiResponse() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/${this.currentSkuId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const result = await response.json();

                if (result.data) {

                    if (result.data.product) {
                    } else {
                    }
                }
            } else {
            }
        } catch (error) {
        }
    }

    /**
     * 从服务器加载关注状态
     */
    async loadFocusStatusFromServer() {
        try {

            const response = await fetch(`${this.apiBaseUrl}/${this.currentSkuId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const result = await response.json();

                if (result.success && result.data && result.data.product) {
                    // 关注状态在 result.data.product.is_tracked 字段
                    const isTracked = result.data.product.is_tracked;

                    // is_tracked 是数字：1表示已关注，0表示未关注
                    this.isFocused = !!(isTracked && isTracked !== 0);

                } else if (response.status === 404) {
                    // 商品不存在，默认未关注
                    this.isFocused = false;
                } else {
                    // 其他情况，默认未关注
                    this.isFocused = false;
                }
            } else {
                // API调用失败，默认未关注
                this.isFocused = false;
            }
        } catch (error) {
            this.isFocused = false;
        }
    }



    /**
     * 绑定现有关注按钮
     */
    async bindExistingFocusButton() {
        try {

            // 等待按钮元素出现（最多等待3秒）
            let attempts = 0;
            const maxAttempts = 30; // 30次 * 100ms = 3秒

            while (attempts < maxAttempts) {
                this.focusButton = document.getElementById('focus-product-btn');
                if (this.focusButton) {
                    break;
                }
                await new Promise(resolve => setTimeout(resolve, 100));
                attempts++;
            }

            if (!this.focusButton) {
                return;
            }


            // 移除可能存在的旧事件监听器
            this.focusButton.replaceWith(this.focusButton.cloneNode(true));
            this.focusButton = document.getElementById('focus-product-btn');

            // 立即更新按钮显示（基于已加载的关注状态）
            this.updateButtonDisplay();

            // 添加点击事件监听器
            this.focusButton.addEventListener('click', async (e) => {
                e.preventDefault();
                e.stopPropagation();
                await this.toggleFocus();
            });

        } catch (error) {
        }
    }



    /**
     * 切换关注状态 - 直接操作，无弹窗
     */
    async toggleFocus() {
        try {

            // 显示加载状态
            this.setLoadingState(true);

            // 调用服务器API切换关注状态
            const response = await fetch(`${this.apiBaseUrl}/${this.currentSkuId}/toggle-tracking`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({})
            });

            if (response.ok) {
                const result = await response.json();

                if (result.success) {
                    // 更新本地状态
                    this.isFocused = result.data.is_tracked;

                    // 更新按钮显示
                    this.updateButtonDisplay();

                    // 显示成功提示
                    this.showMessage(result.data.message, 'success');

                } else {
                    throw new Error(result.error || '操作失败');
                }
            } else {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

        } catch (error) {
            this.showMessage('操作失败，请重试', 'error');
        } finally {
            this.setLoadingState(false);
        }
    }

    /**
     * 设置加载状态
     */
    setLoadingState(loading) {
        if (!this.focusButton) return;

        if (loading) {
            this.focusButton.style.opacity = '0.6';
            this.focusButton.style.cursor = 'not-allowed';

            const textElement = this.focusButton.querySelector('.ltby-focus-text');
            if (textElement) {
                textElement.textContent = '处理中...';
            }
        } else {
            this.focusButton.style.opacity = '1';
            this.focusButton.style.cursor = 'pointer';
        }
    }

    /**
     * 更新按钮显示
     * @param {HTMLElement} [externalButton] - Optional external button element from an external caller.
     */
    updateButtonDisplay(externalButton = null) {
        if (externalButton && !this.focusButton) {
            // If an external button is provided and our internal one isn't set, adopt it.
            this.focusButton = externalButton;
            // Ensure the click handler is attached to this newly adopted button.
            // Use a flag to prevent multiple attachments if initialize() might run again.
            if (!this.focusButton.dataset.ltbyFocusListenerAttached) {
                this.focusButton.addEventListener('click', async (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    await this.toggleFocus();
                });
                this.focusButton.dataset.ltbyFocusListenerAttached = 'true';
            }
        }

        if (!this.focusButton) {
            return;
        }

        // 更新按钮文本和CSS类，让CSS样式生效
        if (this.isFocused) {
            // 已关注状态：使用CSS的focused类
            this.focusButton.textContent = '✓ 已关注';
            this.focusButton.className = 'focus-btn focused';
            // 清除内联样式，让CSS样式生效
            this.focusButton.style.backgroundColor = '';
            this.focusButton.style.color = '';
            this.focusButton.style.border = '';
            this.focusButton.style.fontWeight = '';
        } else {
            // 未关注状态：使用CSS的默认样式
            this.focusButton.textContent = '关注';
            this.focusButton.className = 'focus-btn';
            // 清除内联样式，让CSS样式生效
            this.focusButton.style.backgroundColor = '';
            this.focusButton.style.color = '';
            this.focusButton.style.border = '';
            this.focusButton.style.fontWeight = '';
        }

        // 强制重新渲染按钮样式
        this.focusButton.offsetHeight; // 触发重排

        // 延迟检查样式是否生效
        setTimeout(() => {
            const computedStyle = window.getComputedStyle(this.focusButton);
        }, 100);
    }

    /**
     * 显示消息提示
     */
    showMessage(message, type = 'info') {
        const colors = {
            success: '#52c41a',
            error: '#f5222d',
            info: '#1890ff'
        };

        const messageDiv = document.createElement('div');
        messageDiv.style.cssText = `
            position: fixed;
            top: 100px;
            right: 20px;
            background: ${colors[type]};
            color: white;
            padding: 12px 16px;
            border-radius: 4px;
            z-index: 10001;
            font-size: 14px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        `;
        messageDiv.textContent = message;

        document.body.appendChild(messageDiv);

        // 3秒后自动消失
        setTimeout(() => {
            messageDiv.style.opacity = '0';
            messageDiv.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    document.body.removeChild(messageDiv);
                }
            }, 300);
        }, 3000);
    }

    /**
     * 获取当前关注状态
     */
    getFocusStatus() {
        return {
            isFocused: this.isFocused,
            currentSkuId: this.currentSkuId,
            isInitialized: this.isInitialized
        };
    }
}

// 创建全局实例
window.SimpleFocusManager = SimpleFocusManager;
window.simpleFocusManager = new SimpleFocusManager();

// 为了兼容性，也导出为原有名称
window.FocusProductManager = {
    init: async (skuId) => { // Ensure init is async
        return await window.simpleFocusManager.initialize(); // await the async initialize
    },
    toggleFocus: async () => { // Ensure toggleFocus is async
        return await window.simpleFocusManager.toggleFocus(); // await the async toggleFocus
    },
    updateButtonUI: (buttonElement) => { // Accept buttonElement as parameter
        if (window.simpleFocusManager) {
            window.simpleFocusManager.updateButtonDisplay(buttonElement); // Pass buttonElement
        }
        return true;
    },
    getFocusStatus: () => {
        return window.simpleFocusManager.getFocusStatus();
    }
};

// 自动初始化
(function () {

    // 等待DOM和其他脚本加载完成
    function initializeWhenReady() {
        // 检查是否是京东商品页面
        if (!location.href.includes('item.jd.com')) {
            return;
        }

        // 等待更长时间确保所有元素都已加载
        setTimeout(async () => {
            try {
                await window.simpleFocusManager.initialize();
            } catch (error) {
                // 如果失败，再次尝试
                setTimeout(async () => {
                    try {
                        await window.simpleFocusManager.initialize();
                    } catch (retryError) {
                    }
                }, 2000);
            }
        }, 1000); // 增加等待时间到1秒
    }

    // 等待页面加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeWhenReady);
    } else {
        initializeWhenReady();
    }

    // 监听页面变化，重新初始化
    let lastUrl = location.href;
    const checkUrlChange = () => {
        if (location.href !== lastUrl) {
            lastUrl = location.href;
            setTimeout(async () => {
                try {
                    await window.simpleFocusManager.initialize();
                } catch (error) {
                }
            }, 1000);
        }
    };

    // 定期检查URL变化（适用于SPA应用）
    setInterval(checkUrlChange, 1000);
})();

// 添加全局调试函数
window.debugFocusStatus = async function () {
    if (window.simpleFocusManager) {

        // 检查按钮状态
        if (window.simpleFocusManager.focusButton) {
            const btn = window.simpleFocusManager.focusButton;
        }

        if (window.simpleFocusManager.currentSkuId) {
            await window.simpleFocusManager.testApiResponse();
        }
    } else {
    }
};
