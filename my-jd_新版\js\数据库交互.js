/**
 * 数据库交互模块
 * 负责商品信息的存储、查询、更新和价格趋势分析
 */

// console.log('数据库交互模块开始加载...');

// 避免重复声明
if (typeof window.DatabaseManager !== 'undefined' && window.databaseManager) {
    // console.log('DatabaseManager 已存在，跳过重复声明');
} else {

    class DatabaseManager {
        constructor() {
            // 使用配置文件中的API地址
            this.apiBaseUrl = typeof CURRENT_CONFIG !== 'undefined'
                ? CURRENT_CONFIG.API_BASE_URL.replace('/api', '') + '/api'
                : 'https://zzz7.top/api'; // 使用HTTPS地址
            this.currentProductData = null;
            this.priceHistory = [];
            this.marketPriceInfo = null;
            
            // 添加优惠算法模块数据缓存
            this.promotionData = null;
            
            // 添加等待检查标志
            this.pendingCheck = false;
            
            // 初始化事件监听器
            this.initEventListeners();
            
            console.log('🔗 DatabaseManager 初始化完成，API地址:', this.apiBaseUrl);
        }

        /**
         * 初始化事件监听器
         */
        initEventListeners() {
            // 监听优惠算法模块的JdPromotionCalculated事件
            document.addEventListener('JdPromotionCalculated', (event) => {
                try {
                    console.log('🎯 数据库交互模块收到JdPromotionCalculated事件，立即执行数据库交互');
                    
                    // 直接使用事件数据执行数据库交互，不进行缓存
                    this.handlePromotionDataAndUpdate(event.detail);
                    
                } catch (error) {
                    console.error('� 处理JdPromotionCalculated事件失败:', error);
                }
            });
            
            console.log('🎯 数据库交互模块事件监听器初始化完成');
        }

        /**
         * 根据SKU查询商品信息
         */
        async getProductBySku(skuId) {
            try {
                // console.log('查询商品信息, SKU:', skuId);
                const response = await fetch(`${this.apiBaseUrl}/products/${skuId}`);

                if (response.ok) {
                    const data = await response.json();
                    // console.log('查询到商品信息:', data);
                    return data;
                } else if (response.status === 404) {
                    // console.log('商品不存在于数据库中');
                    return null;
                } else {
                    throw new Error(`查询失败: ${response.status}`);
                }
            } catch (error) {
                console.error('查询商品信息失败:', error);
                return null;
            }
        }

        /**
         * 保存商品信息
         */
        async saveProduct(productData) {
            try {
                // console.log('保存商品信息:', productData);
                const response = await fetch(`${this.apiBaseUrl}/products`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(productData)
                });

                if (response.ok) {
                    const result = await response.json();
                    // console.log('商品信息保存成功:', result);
                    return result;
                } else {
                    throw new Error(`保存失败: ${response.status}`);
                }
            } catch (error) {
                console.error('保存商品信息失败:', error);
                throw error;
            }
        }

        /**
         * 更新商品信息
         */
        async updateProduct(skuId, updateData) {
            try {
                // console.log('更新商品信息:', skuId, updateData);
                const response = await fetch(`${this.apiBaseUrl}/products/${skuId}`, {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(updateData)
                });

                if (response.ok) {
                    const result = await response.json();
                    // console.log('商品信息更新成功:', result);
                    return result;
                } else {
                    throw new Error(`更新失败: ${response.status}`);
                }
            } catch (error) {
                console.error('更新商品信息失败:', error);
                throw error;
            }
        }

        /**
         * 获取商品价格历史
         * 注意：价格历史数据从商品查询或check-page API中获取，不需要单独的端点
         */
        async getPriceHistory(skuId) {
            try {
                console.log('获取价格历史, SKU:', skuId);

                // 如果已经有缓存的价格历史，直接返回
                if (this.priceHistory && this.priceHistory.length > 0) {
                    console.log('使用缓存的价格历史:', this.priceHistory);
                    return this.priceHistory;
                }

                // 通过商品查询API获取价格历史
                const productData = await this.getProductBySku(skuId);
                if (productData.success && productData.data && productData.data.price_history) {
                    console.log('从商品数据获取价格历史:', productData.data.price_history);
                    this.priceHistory = productData.data.price_history;
                    return this.priceHistory;
                }

                console.log('未找到价格历史数据');
                return [];
            } catch (error) {
                console.error('获取价格历史失败:', error);
                return [];
            }
        }

        /**
         * 保存价格历史记录 - 已废弃，由check-page API自动处理
         */
        async savePriceHistory(priceData) {
            console.log('⚠️ savePriceHistory已废弃，价格历史由check-page API自动处理:', priceData);
            // 不执行任何操作，直接返回成功
            return { success: true, message: '价格历史由check-page API自动处理' };
        }

        // 价格趋势分析已移除 - 现在由远程服务器计算

        /**
         * 更新商品关注状态
         */
        async updateFocusStatus(skuId, isFocused, focusReason = '') {
            try {
                console.log('更新关注状态:', skuId, isFocused, focusReason);

                if (isFocused) {
                    // 添加关注
                    const response = await fetch(`${this.apiBaseUrl}/focus-settings`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            sku_id: skuId,
                            focus_type: 'manual',
                            focus_reason: focusReason
                        })
                    });

                    if (response.ok) {
                        // 同时更新商品表的关注状态
                        await this.updateProduct(skuId, { is_focused: 1 });
                        return true;
                    }
                } else {
                    // 取消关注
                    const response = await fetch(`${this.apiBaseUrl}/focus-settings/${skuId}`, {
                        method: 'DELETE'
                    });

                    if (response.ok) {
                        // 同时更新商品表的关注状态
                        await this.updateProduct(skuId, { is_focused: 0 });
                        return true;
                    }
                }

                return false;
            } catch (error) {
                console.error('更新关注状态失败:', error);
                return false;
            }
        }

        /**
         * 根据标题相似度查询市场价
         */
        async getMarketPriceByTitle(title) {
            try {
                console.log('根据标题查询市场价:', title);
                const response = await fetch(`${this.apiBaseUrl}/products/market-price/search`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ title })
                });

                if (response.ok) {
                    const data = await response.json();
                    console.log('标题匹配市场价结果:', data);
                    return data;
                }

                return null;
            } catch (error) {
                console.error('根据标题查询市场价失败:', error);
                return null;
            }
        }

        /**
         * 更新市场价
         */
        async updateMarketPrice(skuId, marketPrice) {
            try {
                console.log('更新市场价:', skuId, marketPrice);
                const updateData = {
                    market_price: marketPrice,
                    market_price_update_time: new Date().toISOString()
                };

                const result = await this.updateProduct(skuId, updateData);
                return result;
            } catch (error) {
                console.error('更新市场价失败:', error);
                throw error;
            }
        }

        // 毛利计算已移除 - 现在由毛利计算模块处理

        /**
         * 直接从页面提取商品信息（新版 - 基于pageConfig和promotionData.originalData）
         */
        extractPageData() {
            console.log('🔍 数据库交互模块开始提取页面数据（已解耦统一元素提取器）...');

            // 直接使用新版方法
            return this.extractPageDataNew();
        }        // 已移除getBestPriceFromPageData方法 - 直接使用getBestPrice()

        /**
         * 获取最优价格（新版 - 基于优惠算法模块和promotionData.originalData）
         */
        getBestPrice() {
            console.log('💰 开始获取最优价格（已解耦统一元素提取器）...');
            
            // 直接使用新版方法
            return this.getBestPriceNew();
        }

        /**
         * 检查是否有有效价格（新版 - 基于新数据源）
         */
        hasValidPrice() {
            // 直接使用新版方法
            return this.hasValidPriceNew();
        }

        /**
         * 新版有效价格检查 - 基于新数据源
         */
        hasValidPriceNew() {
            try {
                const bestPrice = this.getBestPriceNew();
                const isValid = bestPrice && bestPrice.price > 0;
                
                console.log('📊 新版价格有效性检查:', {
                    isValid,
                    price: bestPrice?.price,
                    source: bestPrice?.source
                });
                
                return isValid;
            } catch (error) {
                console.error('💥 新版价格检查失败:', error);
                return false;
            }
        }

        // 已移除hasValidPriceAuto方法 - 统一使用hasValidPrice()

        /**
         * 统一商品页面检查（新版 - 已解耦统一元素提取器）
         */
        async checkProductPage() {
            console.log('🚀 开始统一商品页面检查（已解耦统一元素提取器）...');

            // 直接使用新版方法
            return await this.checkProductPageNew();
        }



        /**
         * 完整商品数据更新（新版 - 已解耦统一元素提取器）
         */
        async performFullUpdate(pageData) {
            console.log('💰 开始完整商品数据更新（已解耦统一元素提取器）...');

            // 直接使用新版方法
            const bestPrice = this.getBestPrice();
            return await this.performFullUpdateNew(pageData, bestPrice);
        }



        /**
         * 检查是否需要更新商品信息（新版 - 已解耦统一元素提取器）
         */
        needsProductInfoUpdate(dbProduct, pageData) {
            // 直接调用新版方法
            return this.needsProductInfoUpdateNew(dbProduct, pageData);
        }

        /**
         * 更新商品信息（新版 - 已解耦统一元素提取器）
         */
        async updateProductInfo(skuId, pageData) {
            // 直接调用新版方法
            return await this.updateProductInfoNew(skuId, pageData);
        }

        /**
         * 切换商品特别关注状态
         */
        async toggleProductTracking(skuId) {
            try {
                console.log('切换商品关注状态:', skuId);

                const response = await fetch(`${this.apiBaseUrl}/products/${skuId}/toggle-tracking`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    console.log('关注状态切换成功:', result);

                    if (result.success) {
                        // 更新本地数据
                        if (this.currentProductData && this.currentProductData.product) {
                            this.currentProductData.product.is_tracked = result.data.is_tracked;
                        }

                        return {
                            success: true,
                            isTracked: result.data.is_tracked,
                            message: result.data.message
                        };
                    } else {
                        throw new Error(result.error || '关注状态切换失败');
                    }
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

            } catch (error) {
                console.error('切换关注状态失败:', error);
                return {
                    success: false,
                    error: error.message,
                    message: '操作失败，请重试'
                };
            }
        }

        /**
         * 更新商品市场价格
         */
        async updateProductMarketPrice(skuId, marketPrice) {
            try {
                console.log('更新商品市场价:', { skuId, marketPrice });

                const response = await fetch(`${this.apiBaseUrl}/products/${skuId}/market-price`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ market_price: parseFloat(marketPrice) })
                });

                if (response.ok) {
                    const result = await response.json();
                    console.log('市场价更新成功:', result);

                    if (result.success) {
                        // 更新本地市场价信息
                        this.marketPriceInfo = {
                            market_price: result.data.market_price,
                            updated_at: result.data.updated_at,
                            profit: result.data.profit
                        };

                        return {
                            success: true,
                            marketPrice: result.data.market_price,
                            profit: result.data.profit,
                            updatedAt: result.data.updated_at,
                            message: result.data.message
                        };
                    } else {
                        throw new Error(result.error || '市场价更新失败');
                    }
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

            } catch (error) {
                console.error('市场价更新失败:', error);
                return {
                    success: false,
                    error: error.message,
                    message: '更新失败，请检查价格格式'
                };
            }
        }

        /**
         * 调试方法：检查全局变量的实际数据
         */
        debugGlobalVariables() {
            console.log('🔍 全局变量调试信息:');
            
            // 检查 pageConfig
            console.log('📋 window.pageConfig:', window.pageConfig);
            if (window.pageConfig) {
                console.log('📋 pageConfig 详细内容:', JSON.stringify(window.pageConfig, null, 2));
                if (window.pageConfig.product) {
                    console.log('📋 pageConfig.product:', JSON.stringify(window.pageConfig.product, null, 2));
                }
            }
            
            // 检查优惠数据
            console.log('💰 this.promotionData:', this.promotionData);
            if (this.promotionData) {
                console.log('💰 promotionData 详细内容:', JSON.stringify(this.promotionData, null, 2));
                if (this.promotionData.originalData) {
                    console.log('💰 promotionData.originalData:', JSON.stringify(this.promotionData.originalData, null, 2));
                }
            }
            
            // 检查全局优惠数据
            console.log('� window.currentPromotionData:', window.currentPromotionData);
            
            // 检查统一产品数据
            console.log('� window.unifiedProductData:', window.unifiedProductData);
            
            // 检查品牌信息
            const brandInfo = this.getBrandFromUnifiedData();
            console.log('🏷️ 提取到的品牌信息:', brandInfo);
        }

        /**
         * 新版数据提取方法 - 基于优惠算法模块API数据和pageConfig
         * 简化版：SKU直接从URL提取，URL使用当前页面链接
         */
        extractPageDataNew() {
            console.log('🔍 数据库交互模块开始提取页面数据（简化版 - SKU从URL提取）...');

            console.log('🔍 调试信息:', {
                hasPromotionData: !!this.promotionData,
                promotionDataKeys: this.promotionData ? Object.keys(this.promotionData) : [],
                currentUrl: window.location.href,
                scriptTagsCount: document.head.querySelectorAll('script').length
            });

            try {
                // 1. 获取当前商品SKU
                const sku = this.getCurrentSKU();
                if (!sku) {
                    console.error('⚠️ 无法获取商品SKU');
                    return null;
                }
                console.log('✅ SKU提取成功:', sku);

                // 2. 检查是否有优惠算法模块的原始数据
                const originalData = this.promotionData?.originalData;
                console.log('🔍 原始数据检查:', {
                    hasOriginalData: !!originalData,
                    originalDataKeys: originalData ? Object.keys(originalData) : [],
                    wareName: originalData?.wareName,
                    name: originalData?.name,
                    p: originalData?.p,
                    wareInfoReadMapKeys: originalData?.wareInfoReadMap ? Object.keys(originalData.wareInfoReadMap) : []
                });

                // 3. 如果没有优惠算法模块数据，等待数据
                if (!originalData) {
                    console.log('⚠️ 优惠算法模块数据尚未就绪，等待数据...');
                    return null; // 返回null，让调用方知道需要等待
                }

                // 4. 从pageConfig获取品类信息
                const categoryInfo = this.getCategoryFromPageConfig();

                // 5. 构建新的数据结构（支持动态品类数组）
                // 标题字段严格按需求：使用wareInfoReadMap.product_name字段
                let productTitle = '';
                if (originalData.wareInfoReadMap && originalData.wareInfoReadMap.product_name) {
                    productTitle = originalData.wareInfoReadMap.product_name;
                    console.log('📊 从wareInfoReadMap.product_name获取标题:', productTitle);
                } else {
                    // 兜底：尝试其他字段
                    productTitle = originalData.wareName || originalData.name || originalData.title;
                    if (!productTitle && originalData.wareInfoReadMap) {
                        productTitle = originalData.wareInfoReadMap.wname || 
                                      originalData.wareInfoReadMap.title || 
                                      originalData.wareInfoReadMap.name ||
                                      originalData.wareInfoReadMap.cn_title;
                    }
                    console.log('📊 使用兜底逻辑获取标题:', productTitle);
                }
                
                // 确保品牌信息从wareInfoReadMap.cn_brand获取
                const brandFromWareInfo = originalData.wareInfoReadMap?.cn_brand || '';
                
                // 构建商品URL - 使用当前页面URL，去掉.html后面的所有内容
                const currentUrl = window.location.href;
                const pcUrl = currentUrl.split('.html')[0] + '.html';
                console.log('🔗 URL处理:', {
                    原始URL: currentUrl,
                    处理后URL: pcUrl
                });
                
                console.log('📊 字段提取详情:', {
                    title: productTitle,
                    titleSource: originalData.wareInfoReadMap?.product_name ? 'wareInfoReadMap.product_name' : '兜底逻辑',
                    brand: brandFromWareInfo,
                    brandSource: 'wareInfoReadMap.cn_brand',
                    sku: sku,
                    skuSource: 'url_direct_extraction',
                    url: pcUrl,
                    urlSource: 'current_page_cleaned',
                    wareInfoReadMapKeys: originalData.wareInfoReadMap ? Object.keys(originalData.wareInfoReadMap) : []
                });
                
                const pageData = {
                    basic: {
                        skuId: sku,
                        title: productTitle || '京东商品', // 确保不为空
                        pcUrl: pcUrl,
                        brand: brandFromWareInfo
                    },
                    category: {
                        level1: categoryInfo.level1 || '',
                        level2: categoryInfo.level2 || '',
                        level3: categoryInfo.level3 || '',
                        level4: categoryInfo.level4 || '',
                        level5: categoryInfo.level5 || '',
                        brand: brandFromWareInfo, // 确保使用同一品牌数据源
                        categories: categoryInfo.categories || [],  // 原始动态数组
                        totalLevels: categoryInfo.totalLevels || 0
                    },
                    price: {
                        originalPrice: parseFloat(originalData.p) || 0,
                        currentPrice: parseFloat(originalData.p) || 0
                    },
                    source: 'promotion_api_data'
                };

                console.log('📊 新版数据提取成功:', pageData);
                
                // 验证关键字段
                if (!pageData.basic.title || pageData.basic.title === '京东商品') {
                    console.warn('⚠️ 商品标题为空或使用默认值，检查数据源:', {
                        productTitle,
                        wareInfoReadMapKeys: originalData.wareInfoReadMap ? Object.keys(originalData.wareInfoReadMap) : [],
                        wareInfoReadMap: originalData.wareInfoReadMap
                    });
                }
                
                return pageData;

            } catch (error) {
                console.error('💥 新版数据提取失败:', error);
                return null;
            }
        }

        /**
         * 获取当前商品SKU
         * 简化版：直接从当前页面URL提取SKU
         */
        getCurrentSKU() {
            const url = window.location.href;
            console.log('🔍 从URL提取SKU:', url);
            
            // 简单正则匹配：提取URL中的数字SKU
            // 匹配 /数字.html 模式，支持各种京东域名
            const match = url.match(/\/(\d+)\.html/);
            if (match && match[1]) {
                const sku = match[1];
                console.log('✅ 从URL提取SKU成功:', sku);
                return sku;
            }
            
            console.error('⚠️ 无法从URL提取SKU，当前URL:', url);
            return null;
        }

        /**
         * 从head中的script标签获取品类信息
         * 直接用正则表达式解析catName数组，不使用eval
         * 优化：只查找 charset="gbk" 的 script 标签，提升效率
         * brand字段单独从promotionData.originalData.wareInfoReadMap.cn_brand获取
         */
        getCategoryFromPageConfig() {
            console.log('📊 开始获取品类信息（只查找 charset="gbk" 的 script 标签）...');
            
            const categoryInfo = {
                brand: '',
                categories: []
            };

            try {
                // 只查找 charset="gbk" 的 script 标签，提升效率
                const scripts = document.head.querySelectorAll('script[charset="gbk"]');
                let catNameArray = [];
                let foundPageConfigScript = false;
                
                console.log(`📊 找到 ${scripts.length} 个 charset="gbk" 的 script 标签`);
                
                for (const script of scripts) {
                    const scriptContent = script.textContent || script.innerText;
                    if (scriptContent && scriptContent.includes('var pageConfig = {')) {
                        foundPageConfigScript = true;
                        console.log('📊 找到pageConfig script标签，长度:', scriptContent.length);
                        
                        // 使用正则表达式直接提取catName数组
                        let catNameMatch = scriptContent.match(/catName\s*:\s*\[([^\]]*)\]/);
                        
                        // 如果第一种方法失败，尝试更宽松的匹配
                        if (!catNameMatch) {
                            catNameMatch = scriptContent.match(/["']?catName["']?\s*:\s*\[([^\]]*)\]/);
                        }
                        
                        if (catNameMatch) {
                            try {
                                const catNameStr = catNameMatch[1];
                                console.log('📊 提取到catName字符串:', catNameStr);
                                
                                // 如果为空数组，跳过
                                if (!catNameStr.trim()) {
                                    console.log('📊 catName为空数组，跳过');
                                    continue;
                                }
                                
                                // 解析数组内容，处理引号包围的字符串
                                const items = catNameStr.split(',').map(item => {
                                    let trimmed = item.trim();
                                    
                                    // 移除外层引号（单引号或双引号）
                                    if (trimmed.length >= 2) {
                                        if ((trimmed.startsWith('"') && trimmed.endsWith('"')) ||
                                            (trimmed.startsWith("'") && trimmed.endsWith("'"))) {
                                            trimmed = trimmed.slice(1, -1);
                                        }
                                    }
                                    
                                    // 处理转义字符
                                    trimmed = trimmed.replace(/\\"/g, '"').replace(/\\'/g, "'").replace(/\\\\/g, '\\');
                                    
                                    return trimmed;
                                }).filter(item => {
                                    // 过滤掉空值、null、undefined等无效项
                                    return item && 
                                           item !== 'null' && 
                                           item !== 'undefined' && 
                                           item !== '' &&
                                           item.trim() !== '';
                                });
                                
                                catNameArray = items;
                                console.log('📊 解析catName成功:', catNameArray);
                                break;
                            } catch (parseError) {
                                console.warn('📊 解析catName数组失败:', parseError);
                                continue;
                            }
                        } else {
                            console.warn('📊 script中未找到catName字段');
                            
                            // 调试信息：查找是否有其他相关字段
                            const possibleMatches = [
                                /category\s*:\s*\[([^\]]*)\]/,
                                /categories\s*:\s*\[([^\]]*)\]/,
                                /catName\s*:\s*"([^"]*)"/, 
                                /catName\s*:\s*'([^']*)'/, 
                                /"catName"\s*:\s*\[([^\]]*)\]/,
                                /'catName'\s*:\s*\[([^\]]*)\]/
                            ];
                            
                            for (const regex of possibleMatches) {
                                const match = scriptContent.match(regex);
                                if (match) {
                                    console.log(`📊 找到可能的替代字段:`, match[0]);
                                    break;
                                }
                            }
                            
                            // 显示pageConfig附近的内容
                            const pageConfigIndex = scriptContent.indexOf('var pageConfig = {');
                            if (pageConfigIndex !== -1) {
                                const contextStart = Math.max(0, pageConfigIndex);
                                const contextEnd = Math.min(scriptContent.length, pageConfigIndex + 500);
                                console.log('📊 pageConfig上下文:', scriptContent.substring(contextStart, contextEnd));
                            }
                        }
                    }
                }
                
                if (!foundPageConfigScript) {
                    console.warn('📊 未找到包含pageConfig的script标签');
                }

                // 设置品类信息
                if (catNameArray.length > 0) {
                    categoryInfo.categories = catNameArray;
                    
                    console.log('📊 从script解析的catName获取品类信息:', {
                        原始catName: catNameArray,
                        处理后categories: categoryInfo.categories,
                        总共级别: categoryInfo.categories.length
                    });
                } else {
                    console.warn('📊 未找到有效的catName数据');
                }

                // 从wareInfoReadMap.cn_brand获取品牌信息
                const brandFromUnifiedData = this.getBrandFromUnifiedData();
                if (brandFromUnifiedData) {
                    categoryInfo.brand = brandFromUnifiedData;
                    console.log('📊 从wareInfoReadMap获取品牌:', categoryInfo.brand);
                }

                // 转换为固定字段格式以兼容现有API
                const result = {
                    level1: categoryInfo.categories[0] || '',
                    level2: categoryInfo.categories[1] || '',
                    level3: categoryInfo.categories[2] || '',
                    level4: categoryInfo.categories[3] || '',
                    level5: categoryInfo.categories[4] || '',
                    brand: categoryInfo.brand,
                    categories: categoryInfo.categories,
                    totalLevels: categoryInfo.categories.length
                };

                console.log('📊 最终品类信息:', result);
                return result;

            } catch (error) {
                console.error('💥 获取品类信息失败:', error);
                return {
                    level1: '', level2: '', level3: '', level4: '', level5: '',
                    brand: '', categories: [], totalLevels: 0
                };
            }
        }

        /**
         * 从promotionData.originalData.wareInfoReadMap.cn_brand获取品牌信息
         * 彻底移除对inject.js统一数据和window变量的依赖
         */
        getBrandFromUnifiedData() {
            try {
                console.log('📊 开始从wareInfoReadMap获取品牌...');
                
                // 只从promotionData.originalData.wareInfoReadMap.cn_brand获取品牌
                const originalData = this.promotionData?.originalData;
                if (originalData && originalData.wareInfoReadMap && originalData.wareInfoReadMap.cn_brand) {
                    const brand = String(originalData.wareInfoReadMap.cn_brand).trim();
                    console.log('📊 从wareInfoReadMap.cn_brand获取品牌:', brand);
                    return brand;
                }

                console.warn('⚠️ 未找到wareInfoReadMap.cn_brand字段');
                console.warn('⚠️ 数据源检查:', {
                    hasPromotionData: !!this.promotionData,
                    hasOriginalData: !!(this.promotionData && this.promotionData.originalData),
                    hasWareInfoReadMap: !!(this.promotionData?.originalData?.wareInfoReadMap),
                    wareInfoReadMapKeys: this.promotionData?.originalData?.wareInfoReadMap ? 
                        Object.keys(this.promotionData.originalData.wareInfoReadMap) : []
                });

                return '';
            } catch (error) {
                console.error('💥 获取品牌信息失败:', error);
                return '';
            }
        }

        // 已移除extractCategoryFromBreadcrumb方法 - 不再使用面包屑提取品类信息

        // 已移除extractModelFromBreadcrumb方法 - model字段不再使用

        /**
         * 新版最优价格获取方法 - 基于优惠算法模块
         * 完全依赖API数据，不再进行DOM价格解析
         */
        getBestPriceNew() {
            console.log('💰 开始获取最优价格（仅依赖API数据）...');

            try {
                // 1. 优先从优惠算法模块获取最优价格
                const promotionData = this.getPromotionData();
                console.log('💰 获取到的促销数据:', promotionData);
                
                if (promotionData && promotionData.optimal && promotionData.optimal.optimalUnitPrice > 0) {
                    console.log('💰 从优惠算法模块获取最优价格:', promotionData.optimal.optimalUnitPrice);
                    return {
                        price: parseFloat(promotionData.optimal.optimalUnitPrice),
                        description: '最优活动价',
                        priceDescription: this.buildPromotionDescription(promotionData.optimal),
                        source: 'promotion_algorithm',
                        quantity: promotionData.optimal.optimalQuantity || 1
                    };
                }

                // 2. 备用：从原始数据获取原价
                if (this.promotionData && this.promotionData.originalData && this.promotionData.originalData.p > 0) {
                    const originalPrice = parseFloat(this.promotionData.originalData.p);
                    console.log('💰 从原始API数据获取价格:', originalPrice);
                    return {
                        price: originalPrice,
                        description: '原价',
                        priceDescription: '',
                        source: 'original_api_data',
                        quantity: 1
                    };
                }

                // 3. 尝试从inject.js统一数据的p字段兜底
                if (window.unifiedData && window.unifiedData.p > 0) {
                    const unifiedPrice = parseFloat(window.unifiedData.p);
                    console.log('💰 从inject.js统一数据获取兜底价格:', unifiedPrice);
                    return {
                        price: unifiedPrice,
                        description: '统一数据价格',
                        priceDescription: '',
                        source: 'unified_data_fallback',
                        quantity: 1
                    };
                }

                // 4. 最后兜底：从单件计算结果获取
                if (promotionData && promotionData.single && promotionData.single.finalUnitPrice > 0) {
                    console.log('💰 从单件计算结果获取价格:', promotionData.single.finalUnitPrice);
                    return {
                        price: parseFloat(promotionData.single.finalUnitPrice),
                        description: '单件活动价',
                        priceDescription: '',
                        source: 'single_promotion',
                        quantity: 1
                    };
                }

                // 5. 最终兜底：尝试从页面元素获取价格
                console.log('💰 尝试从页面元素获取价格兜底...');
                const priceElements = [
                    '.price-current',
                    '.p-price .price',
                    '.jd-price',
                    '.price'
                ];
                
                for (const selector of priceElements) {
                    const element = document.querySelector(selector);
                    if (element) {
                        const priceText = element.textContent?.trim();
                        const priceMatch = priceText?.match(/[\d.]+/);
                        if (priceMatch) {
                            const price = parseFloat(priceMatch[0]);
                            if (price > 0) {
                                console.log('💰 从DOM元素获取兜底价格:', price, '选择器:', selector);
                                return {
                                    price: price,
                                    description: '页面价格',
                                    priceDescription: '',
                                    source: 'dom_fallback',
                                    quantity: 1
                                };
                            }
                        }
                    }
                }

                // 6. 如果所有方法都失败
                console.warn('⚠️ 无法从任何来源获取价格');
                console.warn('⚠️ 价格获取详情:', {
                    hasPromotionData: !!promotionData,
                    hasOptimal: !!(promotionData && promotionData.optimal),
                    optimalUnitPrice: promotionData?.optimal?.optimalUnitPrice,
                    hasOriginalData: !!(this.promotionData && this.promotionData.originalData),
                    originalPrice: this.promotionData?.originalData?.p,
                    hasSingle: !!(promotionData && promotionData.single),
                    singlePrice: promotionData?.single?.finalUnitPrice
                });
                
                return {
                    price: 0,
                    description: 'API数据缺失',
                    priceDescription: '等待API数据加载',
                    source: 'api_missing',
                    quantity: 1
                };

            } catch (error) {
                console.error('💥 价格获取失败:', error);
                return {
                    price: 0,
                    description: '获取失败',
                    priceDescription: '',
                    source: 'error',
                    quantity: 1
                };
            }
        }

        /**
         * 获取优惠算法模块数据
         */
        getPromotionData() {
            try {
                console.log('🔍 开始获取优惠算法模块数据...');
                console.log('🔍 this.promotionData:', this.promotionData);
                console.log('🔍 window.currentPromotionData:', window.currentPromotionData);
                
                // 1. 优先使用事件监听缓存的数据（最准确、最及时）
                if (this.promotionData && this.promotionData.results) {
                    console.log('💰 使用事件缓存的优惠数据');
                    return this.promotionData.results;
                }

                // 2. 备选：检查全局优惠数据（兼容其他模块设置）
                if (window.currentPromotionData && window.currentPromotionData.results) {
                    console.log('💰 使用全局优惠数据');
                    return window.currentPromotionData.results;
                }

                // 3. 兼容：检查旧版事件存储的优惠数据
                if (window.lastPromotionCalculation) {
                    console.log('💰 使用旧版优惠数据');
                    return window.lastPromotionCalculation;
                }

                console.warn('⚠️ 未找到优惠算法模块数据');
                console.warn('⚠️ 调试信息:', {
                    hasPromotionData: !!this.promotionData,
                    hasPromotionResults: !!(this.promotionData && this.promotionData.results),
                    hasGlobalData: !!window.currentPromotionData,
                    hasGlobalResults: !!(window.currentPromotionData && window.currentPromotionData.results),
                    hasLegacyData: !!window.lastPromotionCalculation
                });
                return null;
            } catch (error) {
                console.error('💥 获取优惠数据失败:', error);
                return null;
            }
        }

        /**
         * 构建价格描述文本
         */
        buildPriceDescription(optimalData) {
            try {
                if (!optimalData) return '';

                const parts = [];
                
                // 添加数量信息
                if (optimalData.quantity > 1) {
                    parts.push(`买${optimalData.quantity}件`);
                }

                // 添加单价信息
                if (optimalData.unitPrice) {
                    parts.push(`单价¥${optimalData.unitPrice}`);
                }

                // 添加优惠信息
                if (optimalData.promotions && optimalData.promotions.length > 0) {
                    const promotionNames = optimalData.promotions.map(p => p.description || p.name || '活动优惠');
                    parts.push(promotionNames.join('、'));
                }

                // 如果没有具体优惠信息，但有总价，显示总价信息
                if (parts.length === 0 && optimalData.totalPrice) {
                    parts.push(`总价¥${optimalData.totalPrice}`);
                }

                return parts.join(' ');
            } catch (error) {
                console.error('💥 构建价格描述失败:', error);
                return '';
            }
        }

        /**
         * 构建促销数据的价格描述文本
         */
        buildPromotionDescription(optimalData) {
            try {
                if (!optimalData) return '';

                const parts = [];
                
                // 添加数量信息
                if (optimalData.optimalQuantity > 1) {
                    parts.push(`买${optimalData.optimalQuantity}件`);
                }

                // 添加单价信息
                if (optimalData.optimalUnitPrice) {
                    parts.push(`单价¥${optimalData.optimalUnitPrice.toFixed(2)}`);
                }

                // 添加总价信息
                if (optimalData.optimalTotalPrice) {
                    parts.push(`总价¥${optimalData.optimalTotalPrice.toFixed(2)}`);
                }

                // 添加节省信息
                if (optimalData.savings > 0) {
                    parts.push(`省¥${optimalData.savings.toFixed(2)}`);
                }

                return parts.join(' ');
            } catch (error) {
                console.error('💥 构建促销价格描述失败:', error);
                return '';
            }
        }

        // 已移除extractPriceFromDOM方法 - 完全依赖API数据，不再进行DOM价格解析

        /**
         * 新版统一商品页面检查方法
         * 使用pageConfig和promotionData.originalData.wareInfoReadMap，完全摆脱inject.js依赖
         */
        async checkProductPageNew() {
            try {
                console.log('🚀 开始统一商品页面检查（新版）...');

                // 1. 获取基础页面数据（新版）
                const pageData = this.extractPageDataNew();
                if (!pageData) {
                    console.log('⏳ 优惠算法模块数据尚未就绪，等待数据...');
                    throw new Error('优惠算法模块数据尚未就绪，请稍后重试');
                }
                
                if (!pageData.basic.skuId) {
                    throw new Error('无法提取商品SKU（新版）');
                }

                // 2. 获取最优价格（新版）
                const bestPrice = this.getBestPriceNew();
                if (!bestPrice || bestPrice.price <= 0) {
                    throw new Error('无法获取有效价格（新版）');
                }

                console.log('📊 新版数据获取成功:', {
                    pageData: pageData.basic,
                    bestPrice
                });

                // 3. 执行数据库更新
                return await this.performFullUpdateNew(pageData, bestPrice);

            } catch (error) {
                console.error('💥 新版商品页面检查失败:', error);
                
                // 如果是数据尚未就绪，不发送错误事件，而是等待
                if (error.message.includes('优惠算法模块数据尚未就绪')) {
                    console.log('⏳ 等待优惠算法模块数据，将通过事件监听器重新触发');
                    this.pendingCheck = true; // 设置等待检查标志
                    return {
                        success: false,
                        error: error.message,
                        priceTrend: 'waiting',
                        version: 'new',
                        needsRetry: true
                    };
                }
                
                // 发送错误事件
                document.dispatchEvent(new CustomEvent('databaseFullUpdateError', {
                    detail: { error: error.message, version: 'new' }
                }));

                return {
                    success: false,
                    error: error.message,
                    priceTrend: 'error',
                    version: 'new'
                };
            }
        }

        /**
         * 新版完整更新方法
         */
        async performFullUpdateNew(pageData, bestPrice) {
            try {
                console.log('💰 开始新版完整数据库更新...');

                // 验证数据完整性
                console.log('📋 页面数据检查:', {
                    basic: pageData.basic,
                    category: pageData.category,
                    bestPrice: bestPrice
                });

                // 验证必填字段
                if (!pageData.basic?.skuId) {
                    throw new Error('SKU不能为空');
                }
                if (!pageData.basic?.title) {
                    throw new Error('商品标题不能为空');
                }
                if (!bestPrice?.price || bestPrice.price <= 0) {
                    throw new Error('价格不能为空或为0');
                }

                // 构建完整请求数据（兼容服务器端原有格式）
                const fullData = {
                    sku: String(pageData.basic.skuId), // 确保是字符串
                    url: pageData.basic.pcUrl,
                    title: String(pageData.basic.title), // 确保是字符串
                    current_price: Number(bestPrice.price), // 确保是数字
                    current_price_desc: bestPrice.description || '页面价格',
                    price_description: bestPrice.priceDescription || '',
                    // 使用服务器端期望的旧字段格式
                    category_level1: pageData.category.level1 || null,
                    category_level2: pageData.category.level2 || null,
                    category_level3: pageData.category.level3 || null,
                    // 只发送服务器端支持的字段，暂时去掉level4、level5等扩展字段
                    brand: pageData.category.brand || null,
                    model: null // model字段严格保持为null，不从任何来源获取
                };

                console.log('💰 发送新版完整更新请求:', fullData);
                console.log('🔗 请求URL:', `${this.apiBaseUrl}/products/check-page`);

                // 最后验证构建的数据
                console.log('🔍 最终数据验证:', {
                    hasSku: !!fullData.sku,
                    hasTitle: !!fullData.title,
                    hasPrice: !!fullData.current_price && fullData.current_price > 0,
                    skuType: typeof fullData.sku,
                    titleType: typeof fullData.title,
                    priceType: typeof fullData.current_price,
                    priceValue: fullData.current_price
                });

                const response = await fetch(`${this.apiBaseUrl}/products/check-page`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(fullData)
                });

                console.log('🔍 API响应状态:', response.status, response.statusText);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('❌ API请求失败 - 状态码:', response.status);
                    console.error('❌ API错误响应:', errorText);
                    throw new Error(`API请求失败: ${response.status} ${response.statusText} - ${errorText}`);
                }

                const result = await response.json();
                console.log('💰 新版更新响应:', result);

                if (result.success) {
                    // 保存数据到本地
                    this.currentProductData = result.data;
                    this.priceHistory = result.data.price_history || [];
                    this.marketPriceInfo = result.data.market_price_info;

                    // 检查是否需要更新商品信息
                    if (result.data.product && this.needsProductInfoUpdateNew(result.data.product, pageData)) {
                        console.log('检测到商品信息需要更新（新版），开始更新...');
                        await this.updateProductInfoNew(pageData.basic.skuId, pageData);
                    }

                    const fullResult = {
                        success: true,
                        product: result.data.product,
                        isTracked: result.data.product?.is_tracked || false,
                        priceTrend: result.data.price_trend,
                        priceHistory: result.data.price_history,
                        marketPriceInfo: result.data.market_price_info,
                        isNewProduct: result.data.is_new_product,
                        skuId: pageData.basic.skuId,
                        currentPrice: bestPrice.price,
                        version: 'new',
                        dataSource: bestPrice.source
                    };

                    // 缓存结果供其他模块使用
                    this.lastCheckResult = fullResult;

                    // 发送完整更新完成事件
                    document.dispatchEvent(new CustomEvent('databaseFullUpdateComplete', {
                        detail: fullResult
                    }));

                    console.log('🎉 新版统一商品数据检查完成！');
                    return fullResult;
                } else {
                    console.error('❌ API返回失败结果:', result);
                    throw new Error(`API返回失败: ${result.message || '未知错误'}`);
                }

            } catch (error) {
                console.error('💥 新版完整更新失败详情:', {
                    message: error.message,
                    stack: error.stack,
                    url: `${this.apiBaseUrl}/products/check-page`,
                    requestData: typeof fullData !== 'undefined' ? fullData : '构建失败'
                });
                
                // 尝试简化的兜底逻辑
                console.log('🔄 尝试简化兜底处理...');
                const fallbackResult = {
                    success: true,
                    product: {
                        sku: pageData.basic.skuId,
                        title: pageData.basic.title,
                        current_price: bestPrice.price,
                        current_price_desc: bestPrice.description,
                        is_tracked: false,
                        category_level1: pageData.category.level1,
                        category_level2: pageData.category.level2,
                        category_level3: pageData.category.level3,
                        brand: pageData.category.brand
                    },
                    isTracked: false,
                    priceTrend: 'unknown',
                    priceHistory: [],
                    marketPriceInfo: null,
                    isNewProduct: true,
                    skuId: pageData.basic.skuId,
                    currentPrice: bestPrice.price,
                    version: 'new_fallback',
                    dataSource: bestPrice.source,
                    errorReason: error.message
                };
                
                this.lastCheckResult = fallbackResult;
                console.log('⚠️ 使用兜底结果:', fallbackResult);
                return fallbackResult;
            }
        }

        /**
         * 新版商品信息更新检查
         */
        needsProductInfoUpdateNew(dbProduct, pageData) {
            // 检查是否有占位符或空值
            const hasPlaceholders =
                !dbProduct.title || dbProduct.title.includes('????') ||
                !dbProduct.brand || dbProduct.brand.includes('????') ||
                !dbProduct.category_level1 || dbProduct.category_level1.includes('????') ||
                !dbProduct.category_level2 || dbProduct.category_level2.includes('????') ||
                !dbProduct.category_level3 || dbProduct.category_level3.includes('????');

            // 检查页面数据是否有有效值
            const hasValidPageData =
                pageData.basic.title && !pageData.basic.title.includes('????') &&
                pageData.category.brand && !pageData.category.brand.includes('????') &&
                pageData.category.level1 && !pageData.category.level1.includes('????');

            const needsUpdate = hasPlaceholders && hasValidPageData;

            console.log('新版商品信息更新检查:', {
                hasPlaceholders,
                hasValidPageData,
                needsUpdate,
                dbTitle: dbProduct.title,
                pageTitle: pageData.basic.title,
                dataSource: 'unified_data_new'
            });

            return needsUpdate;
        }

        /**
         * 新版商品信息更新
         */
        async updateProductInfoNew(skuId, pageData) {
            try {
                console.log('新版更新商品信息:', skuId, pageData);

                const updateData = {
                    title: pageData.basic.title,
                    category_level1: pageData.category.level1,
                    category_level2: pageData.category.level2,
                    category_level3: pageData.category.level3,
                    brand: pageData.category.brand,
                    data_source: 'unified_data_new'
                };

                // 移除空值和占位符
                Object.keys(updateData).forEach(key => {
                    if (!updateData[key] || updateData[key].includes('????')) {
                        delete updateData[key];
                    }
                });

                console.log('发送新版商品信息更新:', updateData);

                const response = await fetch(`${this.apiBaseUrl}/products/${skuId}`, {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(updateData)
                });

                if (response.ok) {
                    const result = await response.json();
                    console.log('新版商品信息更新成功:', result);
                    return result;
                } else {
                    const errorText = await response.text();
                    console.error('❌ 新版商品信息更新失败 - 状态码:', response.status);
                    console.error('❌ 错误响应:', errorText);
                    throw new Error(`新版商品信息更新失败: ${response.status} ${response.statusText} - ${errorText}`);
                }

            } catch (error) {
                console.error('新版商品信息更新失败:', error);
                throw error;
            }
        }

        // 已移除checkProductPageAuto方法 - 统一使用checkProductPage()

        /**
         * 测试API连接（临时调试方法）
         */
        async testApiConnection() {
            try {
                console.log('🔍 正在测试API连接...');
                const response = await fetch(`${this.apiBaseUrl}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                console.log('🔍 API连接测试结果:', response.status, response.statusText);
                
                if (response.ok) {
                    const result = await response.text();
                    console.log('✅ API连接成功:', result);
                } else {
                    const errorText = await response.text();
                    console.error('❌ API连接失败:', errorText);
                }
            } catch (error) {
                console.error('❌ API连接测试异常:', error);
            }
        }

        /**
         * 手动调试品类信息（可在控制台调用）
         */
        manualDebugCategory() {
            console.log('🔍 手动调试品类信息开始...');
            
            // 直接调用调试方法
            this.debugGlobalVariables();
            
            // 手动测试品类提取
            const result = this.getCategoryFromPageConfig();
            console.log('🔍 手动调试结果:', result);
            
            return result;
        }

        /**
         * 获取价格趋势的中文显示文本
         * 兼容价格走势模块的调用需求
         */
        getPriceTrendText(priceTrend) {
            try {
                // 价格趋势映射表
                const trendMapping = {
                    // 基础趋势
                    'up': '上涨',
                    'down': '下跌', 
                    'stable': '平稳',
                    'new': '新收',
                    'unknown': '待分析',
                    'waiting': '数据加载中',
                    'error': '分析异常',
                    
                    // 扩展趋势
                    'rising': '上涨',
                    'falling': '下跌',
                    'steady': '平稳',
                    'volatile': '波动',
                    'trend_up': '上涨趋势',
                    'trend_down': '下跌趋势',
                    'price_stable': '价格平稳',
                    'no_data': '暂无数据',
                    
                    // 兼容旧版本
                    'increase': '上涨',
                    'decrease': '下跌',
                    'maintain': '平稳'
                };
                
                // 如果没有传入趋势值，返回默认值
                if (!priceTrend) {
                    console.warn('⚠️ getPriceTrendText: 未传入价格趋势值');
                    return '待分析';
                }
                
                // 转换为小写字符串进行匹配
                const trendKey = String(priceTrend).toLowerCase().trim();
                
                // 查找映射
                const trendText = trendMapping[trendKey];
                
                if (trendText) {
                    console.log('📊 价格趋势文本转换:', {
                        原始值: priceTrend,
                        转换后: trendText
                    });
                    return trendText;
                } else {
                    console.warn('⚠️ 未知的价格趋势值:', priceTrend);
                    // 如果找不到映射，返回原始值
                    return String(priceTrend);
                }
                
            } catch (error) {
                console.error('💥 价格趋势文本转换失败:', error);
                return '分析异常';
            }
        }

        /**
         * 处理优惠数据并立即执行数据库更新
         */
        async handlePromotionDataAndUpdate(promotionEventData) {
            try {
                console.log('🚀 基于优惠算法数据立即执行数据库交互');
                
                // 临时缓存数据供其他方法使用
                this.promotionData = promotionEventData;
                window.currentPromotionData = promotionEventData;
                
                // 直接使用事件数据进行数据库交互
                const result = await this.checkProductPageNew();
                
                console.log('✅ 基于优惠数据的数据库交互完成:', result);
                return result;
                
            } catch (error) {
                console.error('💥 基于优惠数据的数据库交互失败:', error);
                
                // 发送错误事件
                document.dispatchEvent(new CustomEvent('databaseFullUpdateError', {
                    detail: { error: error.message }
                }));
                
                return { success: false, error: error.message };
            }
        }

    }

    // 将类暴露到全局
    window.DatabaseManager = DatabaseManager;

    // 创建全局数据库管理实例
    window.databaseManager = new DatabaseManager();

} // 结束 if 语句

console.log('数据库交互模块加载完成');
