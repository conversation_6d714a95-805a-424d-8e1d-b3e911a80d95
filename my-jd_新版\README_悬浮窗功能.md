# 京东优惠分析悬浮窗功能说明

## 功能概述

京东优惠分析悬浮窗是一个集成了优惠算法模块的实时分析工具，可以在京东商品页面右上角显示悬浮窗，实时计算和展示最优购买方案。

## 主要特性

### 1. 悬浮窗界面
- **位置**: 固定在京东商品页面右上角
- **可拖拽**: 支持鼠标拖拽移动位置
- **响应式**: 支持展开/收起详细分析面板
- **样式**: 现代化UI设计，不影响原页面浏览

### 2. 实时优惠计算
- **自动检测**: 自动获取当前商品的价格和优惠信息
- **批量计算**: 计算1-20件商品的最优购买方案
- **动态更新**: 页面数据变化时自动重新计算
- **多维度分析**: 综合考虑优惠券、促销活动等因素

### 3. 智能推荐
- **最优单价**: 显示能获得最低单价的购买数量
- **最优总价**: 计算对应的总价和节省金额
- **优惠详情**: 列出所有应用的优惠券和促销活动
- **操作建议**: 提供快速加车和购买建议

## 文件结构

```
my-jd_新版/
├── js/
│   ├── 优惠分析悬浮窗.js          # 悬浮窗主逻辑
│   └── 优惠算法模块.js            # 核心计算模块
├── styles/
│   └── purchase-analysis.css      # 悬浮窗样式
├── html/
│   └── 京东优惠分析悬浮窗.html    # 悬浮窗HTML模板
├── test/
│   └── 悬浮窗测试.html           # 功能测试页面
└── manifest.json                  # 扩展配置文件
```

## 使用方法

### 1. 安装扩展
1. 在Chrome浏览器中打开 `chrome://extensions/`
2. 开启"开发者模式"
3. 点击"加载已解压的扩展程序"
4. 选择 `my-jd_新版` 文件夹

### 2. 使用悬浮窗
1. 访问任意京东商品页面（如：`https://item.jd.com/xxxxx.html`）
2. 悬浮窗会自动出现在页面右上角
3. 点击悬浮窗可展开详细分析面板
4. 拖拽悬浮窗标题栏可移动位置

### 3. 功能操作
- **查看最优方案**: 仪表板显示最优购买数量和价格
- **详细分析**: 点击展开查看完整的优惠分析表格
- **复制文案**: 点击"复制文案"按钮生成优惠信息文本
- **快速购买**: 点击"加车购买"按钮（功能开发中）

## 技术实现

### 1. 数据获取
```javascript
// 从页面提取商品数据
extractBasicProductData() {
    const priceSelectors = [
        '.p-price .price',
        '.summary-price .p-price .price'
    ];
    // 提取价格和优惠信息
}
```

### 2. 优惠计算
```javascript
// 使用优惠算法模块计算最优方案
calculatePromotions() {
    for (let quantity = 1; quantity <= 20; quantity++) {
        const result = this.calculator.calculateOptimalDiscount(
            this.currentProductData, 
            quantity
        );
        this.analysisResults[quantity] = result;
    }
}
```

### 3. 界面更新
```javascript
// 更新仪表板显示
updateDashboard(bestResult, bestQuantity) {
    document.getElementById('dashboard-best-price').textContent = 
        `¥${bestResult.finalUnitPrice.toFixed(2)}`;
    // 更新其他显示元素
}
```

## 配置说明

### manifest.json 配置
```json
{
  "content_scripts": [
    {
      "matches": ["*://item.jd.com/*"],
      "js": ["js/优惠分析悬浮窗.js"],
      "run_at": "document_idle"
    }
  ],
  "web_accessible_resources": [
    {
      "resources": [
        "js/优惠算法模块.js",
        "styles/purchase-analysis.css"
      ],
      "matches": ["*://*.jd.com/*"]
    }
  ]
}
```

### CSS 样式配置
```css
#purchase-analysis-root-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    font-family: Arial, sans-serif;
}

.dashboard-header {
    cursor: grab;
    user-select: none;
}
```

## 测试验证

### 1. 功能测试
使用 `test/悬浮窗测试.html` 进行离线测试：
- 模块加载测试
- 悬浮窗显示测试
- 数据计算测试

### 2. 实际测试
在真实的京东商品页面测试：
1. 访问商品页面
2. 检查悬浮窗是否正常显示
3. 验证优惠计算是否准确
4. 测试交互功能是否正常

## 故障排除

### 1. 悬浮窗不显示
- 检查扩展是否正确安装
- 确认页面URL匹配规则
- 查看浏览器控制台错误信息

### 2. 计算结果异常
- 检查优惠算法模块是否正确加载
- 验证商品数据是否正确获取
- 查看计算过程的日志信息

### 3. 样式显示问题
- 确认CSS文件是否正确加载
- 检查是否与页面原有样式冲突
- 验证z-index层级设置

## 开发说明

### 1. 代码结构
- **主类**: `JDPromotionFloatingPanel` - 悬浮窗主控制类
- **计算模块**: `JDPromotionCalculator` - 优惠计算核心
- **事件处理**: 拖拽、点击、数据更新等事件管理

### 2. 扩展功能
- 可以添加更多的数据源支持
- 可以扩展更多的优惠类型计算
- 可以增加更多的交互功能

### 3. 性能优化
- 使用防抖机制避免频繁计算
- 缓存计算结果提高响应速度
- 优化DOM操作减少重绘

## 更新日志

### v1.0.0 (2025-07-03)
- ✅ 实现基础悬浮窗功能
- ✅ 集成优惠算法模块
- ✅ 支持拖拽和展开/收起
- ✅ 实时计算最优购买方案
- ✅ 替换原有的优惠事件测试模块

## 联系支持

如果在使用过程中遇到问题，请：
1. 查看浏览器控制台的错误信息
2. 使用测试页面验证功能
3. 检查扩展配置是否正确
4. 提供详细的错误描述和复现步骤
