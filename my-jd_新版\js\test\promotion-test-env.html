<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>京东插件 - 优惠算法测试环境</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .header {
            border-bottom: 2px solid #e60012;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }

        .input-section {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .input-group {
            flex: 1;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .input-group input,
        .input-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }

        .buttons {
            margin: 20px 0;
            text-align: center;
        }

        .btn {
            padding: 10px 20px;
            margin: 0 10px;
            background: #e60012;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .btn:hover {
            background: #c8000f;
        }

        .btn.secondary {
            background: #666;
        }

        .btn.secondary:hover {
            background: #555;
        }

        .results {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }

        .result-panel {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            background: #fafafa;
        }

        .result-panel h3 {
            margin-top: 0;
            color: #e60012;
        }

        .api-data-input {
            height: 150px;
            font-family: monospace;
            font-size: 12px;
        }

        .output {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }

        .test-cases {
            margin-top: 20px;
        }

        .promotion-item {
            border: 1px solid #ddd;
            margin: 5px 0;
            padding: 10px;
            border-radius: 4px;
            background: #fff;
        }

        .error {
            color: #e60012;
            background: #fff5f5;
            border: 1px solid #e60012;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }

        .success {
            color: #008000;
            background: #f5fff5;
            border: 1px solid #008000;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🧮 京东插件 - 优惠算法测试环境</h1>
            <p>专用于测试真实API数据，完善和验证优惠算法模块的准确性</p>
            <p style="color: #666; font-size: 14px;">✨ 请粘贴京东API返回的促销数据进行测试</p>
        </div>

        <div class="input-section">
            <div class="input-group">
                <label for="original-price">商品原价（元）：</label>
                <input type="number" id="original-price" value="" step="0.01" min="0">
            </div>
            <div class="input-group">
                <label for="max-quantity">购买数量：</label>
                <input type="number" id="max-quantity" value="" min="1" max="200">
            </div>
            <div class="input-group">
                <label for="batch-max">批量计算最大数量：</label>
                <input type="number" id="batch-max" value="50" min="1" max="200">
            </div>
        </div>

        <div class="input-section">
            <div class="input-group">
                <label for="api-data">API数据（JSON格式）：</label>
                <textarea id="api-data" class="api-data-input" placeholder="粘贴京东API返回的促销数据...">

</textarea>
            </div>
            <div class="input-group">
                <label for="manual-promotions">手动添加促销（JSON格式）：</label>
                <textarea id="manual-promotions" class="api-data-input" placeholder='暂时不使用，专注测试API数据'></textarea>
            </div>
        </div>

        <div class="buttons">
            <button class="btn" onclick="calculatePromotions()">🔍 计算最优价格</button>
            <button class="btn" onclick="calculateBatchPrices()">📈 批量价格分析</button>
            <button class="btn secondary" onclick="parseApiData()">📋 解析API数据</button>
            <button class="btn secondary" onclick="runAutomatedTests()">🤖 自动化测试</button>
            <button class="btn secondary" onclick="clearAll()">🗑️ 清空</button>
        </div>

        <div class="results">
            <div class="result-panel">
                <h3>📊 计算结果</h3>
                <div id="calculation-output" class="output">请先输入数据并点击"计算最优价格"</div>
            </div>
            <div class="result-panel">
                <h3>� 批量价格分析</h3>
                <div id="batch-output" class="output">点击"批量价格分析"查看1-N件的价格曲线</div>
            </div>
            <div class="result-panel">
                <h3>�🔧 解析结果</h3>
                <div id="parsing-output" class="output">请先输入API数据并点击"解析API数据"</div>
            </div>
        </div>

        <div class="result-panel" style="margin-top: 20px;">
            <h3>🤖 自动化测试结果</h3>
            <div id="test-output" class="output">点击"自动化测试"按钮运行完整的测试套件</div>
        </div>
    </div>

    <script>
        // 初始化优惠算法模块
        let promotionCalculator;
        window.addEventListener('DOMContentLoaded', function () {
            if (typeof JDPromotionCalculator !== 'undefined') {
                promotionCalculator = new JDPromotionCalculator();
                console.log('✅ 优惠算法模块已加载');
            } else {
                console.error('❌ 优惠算法模块加载失败');
            }
        });
    </script>
    </div>

    <!-- 引入核心算法模块 -->
    <script src="../优惠算法模块.js"></script>

    <script>        // 解析API数据的主函数
        function parseApiData() {
            const apiDataText = document.getElementById('api-data').value.trim();
            const output = document.getElementById('parsing-output');

            if (!apiDataText) {
                output.innerHTML = '<div class="error">请先输入API数据</div>';
                return;
            }

            try {
                const apiData = JSON.parse(apiDataText);
                const parsedResult = parseJDApiData(apiData);
                output.innerHTML = formatParsingResult(parsedResult);
            } catch (error) {
                output.innerHTML = `<div class="error">解析失败: ${error.message}</div>`;
            }
        }

        // 计算促销优惠的主函数
        function calculatePromotions() {
            const apiDataText = document.getElementById('api-data').value.trim();
            const maxQuantity = parseInt(document.getElementById('max-quantity').value) || 1;
            const output = document.getElementById('calculation-output');

            if (!apiDataText) {
                output.innerHTML = '<div class="error">请先输入API数据</div>';
                return;
            }

            try {
                const apiData = JSON.parse(apiDataText);
                // 传递完整的data对象给计算器
                const result = promotionCalculator.calculatePromotions(apiData.data || apiData, maxQuantity);
                output.innerHTML = formatCalculationResult(result);
            } catch (error) {
                output.innerHTML = `<div class="error">计算失败: ${error.message}</div>`;
                console.error('计算错误:', error);
            }
        }        // 格式化计算结果显示
        function formatCalculationResult(result) {
            let html = '<div class="success">✅ 计算完成</div>';

            // 限购信息提示
            if (result.limitExceeded) {
                html += `<div style="background:#ffe6e6; padding:10px; margin:10px 0; border-radius:5px;">`;
                html += `<strong>⚠️ 限购提醒</strong><br>`;
                html += `请求数量: ${result.requestedQuantity}件<br>`;
                html += `限购数量: ${result.limitInfo.limit}件<br>`;
                html += `计算结果: 仅显示限购范围内${result.quantity}件的优惠计算`;
                html += `</div>`;
            }

            html += `<h4>💰 价格信息</h4>`;
            html += `<p>原价: ¥${result.originalPrice}</p>`;
            html += `<p>计算数量: ${result.quantity}件</p>`;
            if (result.limitExceeded) {
                html += `<p style="color:#e60012;">（超出限购部分不计算）</p>`;
            }
            html += `<p>总价: ¥${result.totalPrice}</p>`;
            html += `<p>最终价: ¥${result.finalPrice}</p>`;
            html += `<p>最终单价: ¥${result.finalUnitPrice.toFixed(2)}</p>`;
            html += `<p>总优惠: ¥${result.totalDiscount.toFixed(2)}</p>`;

            if (result.appliedPromotions.length > 0) {
                html += `<h4>🎉 已应用促销（严格按API数组顺序）</h4>`;
                result.appliedPromotions.forEach((promo, index) => {
                    html += `<div class="promotion-item">`;
                    html += `<strong>第${index + 1}步: ${promo.text}</strong><br>`;
                    html += `方法: ${promo.method || promo.shortText || promo.text}<br>`;
                    html += `类型: ${promo.subType}<br>`;
                    html += `优惠金额: ¥${promo.discountAmount.toFixed(2)}<br>`;
                    if (promo.originalIndex !== undefined) {
                        html += `API原始位置: 第${promo.originalIndex + 1}个`;
                    }
                    html += `</div>`;
                });
            }

            if (result.appliedCoupons.length > 0) {
                html += `<h4>🎫 已应用优惠券</h4>`;
                result.appliedCoupons.forEach((coupon, index) => {
                    html += `<div class="promotion-item">`;
                    html += `<strong>优惠券${index + 1}: ${coupon.text}</strong><br>`;
                    html += `类型: ${coupon.subType}<br>`;
                    html += `优惠金额: ¥${coupon.discountAmount.toFixed(2)}`;
                    html += `</div>`;
                });
            }

            if (result.allPromotions.length > 0) {
                html += `<h4>📋 所有解析的促销（API原始顺序）</h4>`;
                result.allPromotions.forEach((promo, index) => {
                    html += `<div class="promotion-item">`;
                    html += `<strong>${index + 1}. ${promo.text}</strong><br>`;
                    html += `方法: ${promo.method || promo.shortText || promo.text}<br>`;
                    html += `类型: ${promo.subType}<br>`;
                    if (promo.threshold) {
                        html += `门槛: ${promo.threshold}<br>`;
                    }
                    if (promo.discount) {
                        html += `折扣率: ${promo.discount}<br>`;
                    }
                    if (promo.reduction) {
                        html += `减免金额: ¥${promo.reduction}<br>`;
                    }
                    if (promo.isEveryFull) {
                        html += `类型: 每满减<br>`;
                    }
                    html += `Value: ${promo.value || '无'}`;
                    html += `</div>`;
                });
            }

            if (result.allCoupons.length > 0) {
                html += `<h4>📋 所有解析的优惠券</h4>`;
                result.allCoupons.forEach((coupon, index) => {
                    html += `<div class="promotion-item">`;
                    html += `<strong>${index + 1}. ${coupon.text}</strong><br>`;
                    html += `类型: ${coupon.subType}<br>`;
                    if (coupon.threshold) {
                        html += `门槛: ¥${coupon.threshold}<br>`;
                    }
                    if (coupon.discount) {
                        html += `折扣率: ${coupon.discount}<br>`;
                    }
                    if (coupon.reduction) {
                        html += `减免金额: ¥${coupon.reduction}`;
                    }
                    html += `</div>`;
                });
            }

            return html;
        }

        // 获取优惠券状态文本的辅助函数
        function getCouponStatusText(status) {
            const statusMap = {
                available: '可用',
                used: '已使用',
                expired: '已过期',
                notStarted: '未开始'
            };
            return statusMap[status] || '未知';
        }

        // 批量价格计算
        function calculateBatchPrices() {
            const apiDataText = document.getElementById('api-data').value.trim();
            const batchMax = parseInt(document.getElementById('batch-max').value) || 50;
            const output = document.getElementById('batch-output');

            if (!apiDataText) {
                output.innerHTML = '<div class="error">请先输入API数据</div>';
                return;
            }

            try {
                const apiData = JSON.parse(apiDataText);
                const batchResult = calculateBatchPricesManual(apiData.data || apiData, batchMax);
                output.innerHTML = formatBatchResult(batchResult);
            } catch (error) {
                output.innerHTML = `<div class="error">批量计算失败: ${error.message}</div>`;
                console.error('批量计算错误:', error);
            }
        }

        // 手动实现批量价格计算
        function calculateBatchPricesManual(productData, maxQuantity) {
            const results = [];
            let optimalQuantity = 1;
            let bestUnitPrice = Infinity;

            // 先检查限购信息
            const limitInfo = promotionCalculator.parseLimitPurchaseInfo(productData);
            let effectiveMaxQuantity = maxQuantity;

            // 如果有限购，只计算到限购数量
            if (limitInfo && limitInfo.limit > 0) {
                effectiveMaxQuantity = Math.min(maxQuantity, limitInfo.limit);
                console.log(`检测到限购${limitInfo.limit}件，批量计算范围调整为${effectiveMaxQuantity}件`);
            }

            for (let qty = 1; qty <= effectiveMaxQuantity; qty++) {
                const result = promotionCalculator.calculatePromotions(productData, qty);
                const unitPrice = result.finalUnitPrice;

                // 如果超出限购，则跳过此数量（实际上不应该发生，因为我们已经调整了范围）
                if (result.limitExceeded) {
                    console.log(`跳过超限数量${qty}`);
                    continue;
                }

                results.push({
                    quantity: qty,
                    unitPrice: unitPrice,
                    totalPrice: result.finalPrice,
                    totalDiscount: result.totalDiscount,
                    appliedPromotions: [...result.appliedCoupons, ...result.appliedPromotions],
                    isOptimal: false,
                    limitExceeded: false,
                    limitInfo: result.limitInfo
                });

                // 比较最优单价
                if (unitPrice < bestUnitPrice) {
                    bestUnitPrice = unitPrice;
                    optimalQuantity = qty;
                }
            }

            // 标记最优数量
            results.forEach(item => {
                if (item.quantity === optimalQuantity) {
                    item.isOptimal = true;
                }
            });

            return {
                success: true,
                basePrice: productData.price ? parseFloat(productData.price.op) : 0,
                currentPrice: productData.price ? parseFloat(productData.price.p) : 0,
                optimalQuantity: optimalQuantity,
                priceRange: results
            };
        }

        // 格式化批量计算结果
        function formatBatchResult(batchResult) {
            let html = '<div class="success">✅ 批量计算完成</div>';

            html += `<h4>📊 价格概览</h4>`;

            html += `<p>原价: ¥${batchResult.currentPrice}</p>`;
            html += `<p>最优购买数量: ${batchResult.optimalQuantity}件</p>`;

            // 显示前20个数量的详细价格
            html += `<h4>📋 详细价格表（前20项）</h4>`;
            html += `<table style="width:100%; border-collapse: collapse; font-size: 12px;">`;
            html += `<tr style="background:#f0f0f0;"><th>数量</th><th>单价</th><th>总价</th><th>优惠</th><th>说明</th></tr>`;

            batchResult.priceRange.slice(0, 20).forEach(item => {
                let bgColor = '#fff';
                let statusText = '';

                if (item.isOptimal) {
                    bgColor = '#e8f5e8';
                    statusText = '🌟最优';
                }

                html += `<tr style="background:${bgColor};">`;
                html += `<td>${item.quantity}</td>`;
                html += `<td>¥${item.unitPrice.toFixed(2)}</td>`;
                html += `<td>¥${item.totalPrice.toFixed(2)}</td>`;
                html += `<td>¥${item.totalDiscount.toFixed(2)}</td>`;
                html += `<td>${statusText} ${item.appliedPromotions.length}个促销</td>`;
                html += `</tr>`;
            });

            html += `</table>`;

            if (batchResult.priceRange.length > 20) {
                html += `<p style="color:#666; font-size:12px;">显示前20项，共计算${batchResult.priceRange.length}个数量档位</p>`;
            }

            return html;
        }// 解析京东API数据结构
        function parseJDApiData(apiResponse) {
            const data = apiResponse.data || apiResponse;

            const result = {
                productInfo: extractProductInfo(data),
                priceInfo: extractPriceInfo(data),
                availablePromotions: extractAvailablePromotions(data),
                availableCoupons: extractAvailableCoupons(data),
                priceExpression: extractPriceExpression(data),
                limits: extractLimits(data),
                stockInfo: extractStockInfo(data)
            };

            return result;
        }

        // 提取商品基础信息
        function extractProductInfo(data) {
            const wareInfo = data.wareInfoReadMap || {};
            return {
                skuId: wareInfo.product_id || '',
                name: wareInfo.product_name || wareInfo.sku_name || '',
                brand: wareInfo.cn_brand || '',
                categoryId: wareInfo.category_id || '',
                weight: wareInfo.weight || '',
                shopId: wareInfo.shop_id || '',
                shopName: wareInfo.shop_name || ''
            };
        }

        // 提取价格信息
        function extractPriceInfo(data) {
            const price = data.price || {};
            const finalPrice = price.finalPrice || {};

            return {
                originalPrice: parseFloat(price.op) || 0,
                currentPrice: parseFloat(price.p) || 0,
                marketPrice: parseFloat(price.m) || 0,
                finalPrice: parseFloat(finalPrice.price) || 0,
                priceContent: finalPrice.priceContent || '',
                skuId: price.id || ''
            };
        }

        // 提取可用促销活动（不包括价格表达式中已应用的）
        function extractAvailablePromotions(data) {
            const promotions = [];
            const preferenceInfo = data.preferenceInfo || {};

            if (preferenceInfo.promotions) {
                preferenceInfo.promotions.forEach(promo => {
                    const promotion = {
                        id: promo.promoId,
                        type: getPromotionTypeByTag(promo.tag),
                        title: promo.text || promo.shortText,
                        shortText: promo.shortText,
                        description: promo.value,
                        tag: promo.tag,
                        sortNum: promo.proSortNum,
                        link: promo.link || '',
                        formal: promo.formal,
                        crossStoreFullCut: promo.crossStoreFullCut
                    };

                    // 解析具体的优惠内容
                    if (promo.value) {
                        promotion.parsedValue = parsePromotionValue(promo.value, promo.tag);
                    }

                    promotions.push(promotion);
                });
            }

            return promotions;
        }

        // 提取可用优惠券
        function extractAvailableCoupons(data) {
            const coupons = [];
            const preferenceInfo = data.preferenceInfo || {};

            if (preferenceInfo.coupons) {
                preferenceInfo.coupons.forEach(coupon => {
                    coupons.push({
                        batchId: coupon.batchId,
                        desc: coupon.desc,
                        discount: parseFloat(coupon.discount) || 0,
                        quota: parseFloat(coupon.quota) || 0,
                        startTime: coupon.startTime,
                        endTime: coupon.endTime,
                        joinFlag: coupon.joinFlag,
                        url: coupon.toUrl,
                        couponType: coupon.couponType,
                        businessLabel: coupon.businessLabel
                    });
                });
            }

            return coupons;
        }

        // 提取价格表达式（显示当前最终价格的计算过程）
        function extractPriceExpression(data) {
            const preferenceInfo = data.preferenceInfo || {};
            const expression = preferenceInfo.expression || {};

            const result = {
                basePrice: parseFloat(expression.basePrice) || 0,
                finalDiscount: parseFloat(expression.discountAmount) || 0,
                discountDesc: expression.discountDesc || '',
                appliedPromotions: []
            };

            if (expression.subtrahends) {
                expression.subtrahends.forEach(sub => {
                    result.appliedPromotions.push({
                        description: sub.preferenceDesc,
                        amount: parseFloat(sub.preferenceAmount) || 0,
                        type: sub.preferenceType,
                        category: sub.topDesc
                    });
                });
            }

            return result;
        }
        // 提取限购信息
        function extractLimits(data) {
            const limitInfo = data.commonLimitInfo || {};
            const resultExt = limitInfo.resultExtMap || {};

            return {
                minNum: parseInt(limitInfo.limitMinNum) || 1,
                maxNum: parseInt(limitInfo.mergeMaxBuyNum) || 999,
                canBuy: resultExt.canBuy === '1',
                limitNum: resultExt.limitNum || '0',
                isPlusLimit: resultExt.isPlusLimit === '1'
            };
        }

        // 提取库存信息
        function extractStockInfo(data) {
            const stockInfo = data.stockInfo || {};

            return {
                isStock: stockInfo.isStock,
                stockState: stockInfo.stockState,
                stockDesc: stockInfo.stockDesc,
                availableNum: stockInfo.stockInfo?.availableNum || 0,
                promiseInfo: stockInfo.promiseInfoText || stockInfo.promiseResult
            };
        }

        // 根据tag判断促销类型
        function getPromotionTypeByTag(tag) {
            const typeMap = {
                19: 'FullCount',      // 多买优惠
                40: 'Member',         // PLUS会员
                97: 'Discount',       // 单品折扣
                3: 'Expression'       // 价格表达式
            };
            return typeMap[tag] || 'Unknown';
        }

        // 解析促销规则的具体数值
        function parsePromotionValue(value, tag) {
            const result = {};

            if (tag === 19) {
                // 多买优惠：满2件，总价打8折；满3件，总价打7折
                const matches = value.match(/满(\d+)件.*?(\d+\.?\d*)折/g);
                if (matches) {
                    result.tiers = [];
                    matches.forEach(match => {
                        const tierMatch = match.match(/满(\d+)件.*?(\d+\.?\d*)折/);
                        if (tierMatch) {
                            result.tiers.push({
                                quantity: parseInt(tierMatch[1]),
                                discount: parseFloat(tierMatch[2]) / 10
                            });
                        }
                    });
                }
            } else if (tag === 97) {
                // 单品折扣：满1件享6.10折
                const match = value.match(/满(\d+)件.*?(\d+\.?\d*)折/);
                if (match) {
                    result.quantity = parseInt(match[1]);
                    result.discount = parseFloat(match[2]) / 10;
                }
            } else if (tag === 40) {
                // PLUS会员优惠
                result.memberOnly = true;
                result.description = value;
            }

            return result;
        }

        // 格式化解析结果显示
        function formatParsingResult(result) {
            let html = '<div class="success">✅ API数据解析完成</div>';

            // 价格信息
            html += `<h4>💰 价格信息</h4>`;
            html += `<p>原价(p字段): ¥${result.priceInfo.currentPrice}</p>`;
            html += `<p><strong>最终价: ¥${result.priceInfo.finalPrice} (${result.priceInfo.priceContent})</strong></p>`;

            // 当前价格计算过程
            if (result.priceExpression.appliedPromotions.length > 0) {
                html += `<h4>🧮 价格计算过程</h4>`;
                html += `<p>基础价格: ¥${result.priceExpression.basePrice}</p>`;
                result.priceExpression.appliedPromotions.forEach(promo => {
                    html += `<p>- ${promo.description} (${promo.category})</p>`;
                });
                html += `<p><strong>最终优惠: ¥${result.priceExpression.finalDiscount}</strong></p>`;
            }

            // 可用促销
            if (result.availablePromotions.length > 0) {
                html += `<h4>🎉 可用促销活动</h4>`;
                result.availablePromotions.forEach(promo => {
                    html += `<div class="promotion-item">`;
                    html += `<strong>[Tag ${promo.tag}]</strong> ${promo.title}<br>`;
                    html += `${promo.description}<br>`;
                    html += `</div>`;
                });
            }

            // 可用优惠券
            if (result.availableCoupons.length > 0) {
                html += `<h4>🎫 可用优惠券</h4>`;
                result.availableCoupons.forEach(coupon => {
                    html += `<div class="promotion-item">`;
                    html += `${coupon.desc}<br>`;
                    html += `有效期: ${coupon.startTime} - ${coupon.endTime}<br>`;
                    html += `状态: ${coupon.joinFlag ? '已领取' : '未领取'}`;
                    html += `</div>`;
                });
            }

            return html;
        }

        // 运行自动化测试
        function runAutomatedTests() {
            const output = document.getElementById('test-output');

            if (!promotionCalculator) {
                output.innerHTML = '<div class="error">❌ 优惠算法模块未加载</div>';
                return;
            }

            output.innerHTML = '<div class="success">🚀 正在运行自动化测试...</div>';

            // 基础功能测试
            setTimeout(() => {
                try {
                    // 测试1：基础折扣
                    const testResult1 = promotionCalculator.calculatePromotions({
                        price: { op: "100", p: "100" },
                        promotions: [
                            { tag: 97, text: "8折优惠", shortText: "满1件享8折", value: "满1件享8.0折" }
                        ]
                    }, 1);

                    // 测试2：满减百分比
                    const testResult2 = promotionCalculator.calculatePromotions({
                        price: { p: "150" },
                        promotions: [
                            {
                                tag: 15,
                                text: "满减",
                                shortText: "满299元，可减8%",
                                value: "满299元，可减8%"
                            }
                        ]
                    }, 2); // 2件，总价300元，应该满足299元门槛

                    let html = '<div class="success">✅ 所有测试通过</div>';
                    html += `<h4>📊 测试结果1：基础折扣</h4>`;
                    html += `<p>原价: ¥${testResult1.originalPrice}</p>`;
                    html += `<p>最终价: ¥${testResult1.finalPrice}</p>`;
                    html += `<p>优惠金额: ¥${testResult1.totalDiscount}</p>`;

                    html += `<h4>📊 测试结果2：满减百分比</h4>`;
                    html += `<p>原价: ¥${testResult2.originalPrice} × 2件 = ¥${testResult2.totalPrice}</p>`;
                    html += `<p>最终价: ¥${testResult2.finalPrice}</p>`;
                    html += `<p>优惠金额: ¥${testResult2.totalDiscount} (期望: ¥24，即300×8%)</p>`;
                    html += `<p>应用促销数: ${testResult2.appliedPromotions.length}</p>`;

                    // 验证满减百分比计算
                    if (testResult2.totalDiscount === 24 && testResult2.finalPrice === 276) {
                        html += '<div class="success">✅ 满减百分比测试通过</div>';
                    } else {
                        html += '<div class="error">❌ 满减百分比测试失败</div>';
                    }

                    if (testResult1.error || testResult2.error) {
                        html += `<div class="error">测试发现错误: ${testResult1.error || testResult2.error}</div>`;
                    }

                    output.innerHTML = html;
                } catch (error) {
                    output.innerHTML = `<div class="error">❌ 测试失败: ${error.message}</div>`;
                    console.error('自动化测试错误:', error);
                }
            }, 500);
        }

        // 清空所有输入
        function clearAll() {
            document.getElementById('api-data').value = '';
            document.getElementById('manual-promotions').value = '';
            document.getElementById('original-price').value = '99.9';
            document.getElementById('max-quantity').value = '1';
            document.getElementById('batch-max').value = '50';
            document.getElementById('calculation-output').innerHTML = '请先输入数据并点击"计算最优价格"';
            document.getElementById('parsing-output').innerHTML = '请先输入API数据并点击"解析API数据"';
            document.getElementById('batch-output').innerHTML = '点击"批量价格分析"查看1-N件的价格曲线';
            document.getElementById('test-output').innerHTML = '点击"自动化测试"按钮运行完整的测试套件';
            console.log('🗑️ 所有输入已清空');
        }
    </script>
</body>

</html>
