<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SKU和URL简化提取测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            color: #333;
            margin-bottom: 15px;
            font-weight: bold;
        }
        .url-input {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .test-button {
            background: #007bff;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .success {
            border-left-color: #28a745;
        }
        .error {
            border-left-color: #dc3545;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>SKU和URL简化提取测试</h1>
    
    <div class="test-container">
        <h2>测试说明</h2>
        <p>本测试用于验证简化后的SKU获取和URL处理方式：</p>
        <ul>
            <li><strong>SKU获取</strong>：直接从URL提取，不再依赖API数据</li>
            <li><strong>URL处理</strong>：使用当前页面URL，去掉.html后面的所有内容</li>
        </ul>
    </div>

    <div class="test-container">
        <div class="test-section">
            <div class="test-title">1. URL模拟测试</div>
            <p>输入不同的京东商品URL，测试SKU提取和URL清理：</p>
            
            <input type="text" class="url-input" id="testUrl" placeholder="输入京东商品URL进行测试" 
                   value="https://item.jd.com/12345.html?ext=abc&from=search">
            
            <button class="test-button" onclick="testUrlParsing()">测试URL解析</button>
            <button class="test-button" onclick="loadPresetUrls()">加载预设URL</button>
            
            <div id="urlTestResult"></div>
            
            <div style="margin-top: 15px;">
                <strong>预设测试URL：</strong>
                <div>
                    <button class="test-button" onclick="setTestUrl('https://item.jd.com/12345.html')">中国站标准URL</button>
                    <button class="test-button" onclick="setTestUrl('https://item.jd.com/67890.html?ext=abc&from=search')">带参数URL</button>
                    <button class="test-button" onclick="setTestUrl('https://item.jd.hk/12345.html#tab')">国际站URL</button>
                    <button class="test-button" onclick="setTestUrl('https://npcitem.jd.hk/67890.html?a=1')">国际站NPC URL</button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">2. 数据库交互模块功能测试</div>
            <p>测试修改后的数据库交互模块的SKU获取和URL处理功能：</p>
            
            <button class="test-button" onclick="testDatabaseManager()">测试DatabaseManager</button>
            <button class="test-button" onclick="testSkuExtraction()">测试SKU提取</button>
            <button class="test-button" onclick="testUrlCleaning()">测试URL清理</button>
            
            <div id="databaseTestResult"></div>
        </div>

        <div class="test-section">
            <div class="test-title">3. 模拟页面环境测试</div>
            <p>模拟不同的页面环境，测试功能的兼容性：</p>
            
            <button class="test-button" onclick="simulateJdPage()">模拟京东页面</button>
            <button class="test-button" onclick="simulateJdHkPage()">模拟国际站页面</button>
            <button class="test-button" onclick="clearSimulation()">清除模拟</button>
            
            <div id="simulationResult"></div>
        </div>
    </div>

    <div class="test-container">
        <h2>控制台日志</h2>
        <p>详细的测试日志将在浏览器控制台中显示。按F12打开开发者工具查看。</p>
        <pre id="consoleOutput"></pre>
    </div>

    <script>
        // URL解析测试函数
        function extractSkuFromUrl(url) {
            console.log('🔍 测试URL提取SKU:', url);
            const match = url.match(/\/(\d+)\.html/);
            if (match && match[1]) {
                console.log('✅ SKU提取成功:', match[1]);
                return match[1];
            }
            console.error('⚠️ 无法从URL提取SKU');
            return null;
        }

        function cleanUrl(url) {
            console.log('🔗 测试URL清理:', url);
            const cleanedUrl = url.split('.html')[0] + '.html';
            console.log('🔗 清理后URL:', cleanedUrl);
            return cleanedUrl;
        }

        function testUrlParsing() {
            const url = document.getElementById('testUrl').value;
            const resultDiv = document.getElementById('urlTestResult');
            
            console.log('=== URL解析测试开始 ===');
            
            try {
                const sku = extractSkuFromUrl(url);
                const cleanedUrl = cleanUrl(url);
                
                const result = {
                    原始URL: url,
                    提取的SKU: sku,
                    清理后URL: cleanedUrl,
                    测试结果: sku ? '成功' : '失败'
                };
                
                console.log('📊 URL解析测试结果:', result);
                
                resultDiv.innerHTML = `
                    <div class="result ${sku ? 'success' : 'error'}">
                        <strong>URL解析测试结果：</strong><br>
                        <strong>原始URL：</strong> ${url}<br>
                        <strong>提取的SKU：</strong> ${sku || '提取失败'}<br>
                        <strong>清理后URL：</strong> ${cleanedUrl}<br>
                        <strong>测试状态：</strong> ${sku ? '✅ 成功' : '❌ 失败'}
                    </div>
                `;
                
            } catch (error) {
                console.error('💥 URL解析测试失败:', error);
                resultDiv.innerHTML = `
                    <div class="result error">
                        <strong>测试失败：</strong> ${error.message}
                    </div>
                `;
            }
            
            console.log('=== URL解析测试结束 ===');
        }

        function setTestUrl(url) {
            document.getElementById('testUrl').value = url;
            testUrlParsing();
        }

        function loadPresetUrls() {
            const presetUrls = [
                'https://item.jd.com/12345.html',
                'https://item.jd.com/67890.html?ext=abc&from=search',
                'https://item.jd.hk/12345.html#tab',
                'https://npcitem.jd.hk/67890.html?a=1&b=2'
            ];
            
            console.log('🔄 测试预设URL列表...');
            
            presetUrls.forEach((url, index) => {
                console.log(`\n--- 预设URL ${index + 1} ---`);
                const sku = extractSkuFromUrl(url);
                const cleanedUrl = cleanUrl(url);
                
                console.log('测试结果:', {
                    URL: url,
                    SKU: sku,
                    清理后URL: cleanedUrl
                });
            });
        }

        // 数据库管理器测试
        function testDatabaseManager() {
            const resultDiv = document.getElementById('databaseTestResult');
            
            console.log('=== 数据库管理器测试开始 ===');
            
            try {
                // 检查是否存在数据库管理器
                if (typeof window.DatabaseManager === 'undefined') {
                    throw new Error('DatabaseManager 类未找到');
                }
                
                // 创建测试实例
                const testManager = new window.DatabaseManager();
                
                // 测试getCurrentSKU方法
                const originalHref = window.location.href;
                
                // 模拟不同的URL
                const testUrls = [
                    'https://item.jd.com/12345.html',
                    'https://item.jd.com/67890.html?ext=abc',
                    'https://item.jd.hk/54321.html'
                ];
                
                const results = [];
                
                testUrls.forEach(testUrl => {
                    // 临时修改location.href进行测试（这在实际页面中不会生效，但可以测试逻辑）
                    Object.defineProperty(window.location, 'href', {
                        value: testUrl,
                        writable: true,
                        configurable: true
                    });
                    
                    const sku = testManager.getCurrentSKU();
                    results.push({
                        url: testUrl,
                        sku: sku,
                        success: sku !== null
                    });
                });
                
                // 恢复原始URL
                Object.defineProperty(window.location, 'href', {
                    value: originalHref,
                    writable: true,
                    configurable: true
                });
                
                console.log('📊 DatabaseManager测试结果:', results);
                
                resultDiv.innerHTML = `
                    <div class="result success">
                        <strong>DatabaseManager测试完成</strong><br>
                        ${results.map(r => 
                            `URL: ${r.url} → SKU: ${r.sku || '提取失败'} ${r.success ? '✅' : '❌'}`
                        ).join('<br>')}
                    </div>
                `;
                
            } catch (error) {
                console.error('💥 DatabaseManager测试失败:', error);
                resultDiv.innerHTML = `
                    <div class="result error">
                        <strong>测试失败：</strong> ${error.message}<br>
                        请确保数据库交互模块已正确加载
                    </div>
                `;
            }
            
            console.log('=== 数据库管理器测试结束 ===');
        }

        function testSkuExtraction() {
            console.log('=== SKU提取专项测试 ===');
            
            const testCases = [
                { url: 'https://item.jd.com/12345.html', expected: '12345' },
                { url: 'https://item.jd.com/67890.html?a=1&b=2', expected: '67890' },
                { url: 'https://item.jd.hk/54321.html#section', expected: '54321' },
                { url: 'https://npcitem.jd.hk/98765.html', expected: '98765' },
                { url: 'https://item.jd.com/invalid-url', expected: null },
                { url: 'https://item.jd.com/.html', expected: null }
            ];
            
            testCases.forEach((testCase, index) => {
                const result = extractSkuFromUrl(testCase.url);
                const success = result === testCase.expected;
                
                console.log(`测试 ${index + 1}:`, {
                    URL: testCase.url,
                    预期SKU: testCase.expected,
                    实际SKU: result,
                    结果: success ? '✅ 通过' : '❌ 失败'
                });
            });
        }

        function testUrlCleaning() {
            console.log('=== URL清理专项测试 ===');
            
            const testCases = [
                { 
                    url: 'https://item.jd.com/12345.html',
                    expected: 'https://item.jd.com/12345.html'
                },
                { 
                    url: 'https://item.jd.com/12345.html?a=1&b=2',
                    expected: 'https://item.jd.com/12345.html'
                },
                { 
                    url: 'https://item.jd.com/12345.html#section',
                    expected: 'https://item.jd.com/12345.html'
                },
                { 
                    url: 'https://item.jd.com/12345.html?ext=abc&from=search#tab',
                    expected: 'https://item.jd.com/12345.html'
                }
            ];
            
            testCases.forEach((testCase, index) => {
                const result = cleanUrl(testCase.url);
                const success = result === testCase.expected;
                
                console.log(`URL清理测试 ${index + 1}:`, {
                    原始URL: testCase.url,
                    预期结果: testCase.expected,
                    实际结果: result,
                    测试结果: success ? '✅ 通过' : '❌ 失败'
                });
            });
        }

        // 模拟页面环境
        function simulateJdPage() {
            console.log('🎭 模拟京东中国站页面环境...');
            
            // 模拟修改当前页面URL
            const mockUrl = 'https://item.jd.com/12345.html?ext=test';
            
            Object.defineProperty(window.location, 'href', {
                value: mockUrl,
                writable: true,
                configurable: true
            });
            
            document.getElementById('simulationResult').innerHTML = `
                <div class="result">
                    <strong>已模拟京东中国站环境</strong><br>
                    模拟URL: ${mockUrl}
                </div>
            `;
            
            console.log('✅ 京东中国站环境模拟完成');
        }

        function simulateJdHkPage() {
            console.log('🎭 模拟京东国际站页面环境...');
            
            const mockUrl = 'https://item.jd.hk/67890.html?region=hk';
            
            Object.defineProperty(window.location, 'href', {
                value: mockUrl,
                writable: true,
                configurable: true
            });
            
            document.getElementById('simulationResult').innerHTML = `
                <div class="result">
                    <strong>已模拟京东国际站环境</strong><br>
                    模拟URL: ${mockUrl}
                </div>
            `;
            
            console.log('✅ 京东国际站环境模拟完成');
        }

        function clearSimulation() {
            console.log('🧹 清除模拟环境...');
            
            // 尝试恢复真实的location.href（在测试环境中可能无效）
            document.getElementById('simulationResult').innerHTML = `
                <div class="result">
                    <strong>模拟环境已清除</strong><br>
                    当前URL: ${window.location.href}
                </div>
            `;
            
            console.log('✅ 模拟环境清除完成');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📋 SKU和URL简化提取测试页面已加载');
            console.log('🔍 当前页面URL:', window.location.href);
            
            // 自动进行一次基础测试
            setTimeout(() => {
                console.log('\n🚀 执行自动基础测试...');
                testUrlParsing();
            }, 1000);
        });

        // 捕获控制台输出并显示在页面上
        const originalConsoleLog = console.log;
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            
            const output = document.getElementById('consoleOutput');
            if (output) {
                const message = args.map(arg => 
                    typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
                ).join(' ');
                output.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
                output.scrollTop = output.scrollHeight;
            }
        };
    </script>
</body>
</html>
