/**
 * 统一元素提取器 - API拦截优先架构
 * 整合元素提取和第三方数据提取功能，支持异步加载和优先级处理
 * 数据流架构：API拦截(价格) > DOM提取(辅助信息) > 第三方数据
 * 价格数据完全依赖API拦截，DOM仅用于品类、品牌等辅助信息
 */

console.log('统一元素提取器开始加载...');

// 避免重复声明
if (typeof window.UnifiedExtractor !== 'undefined' && window.unifiedExtractor) {
    console.log('UnifiedExtractor 已存在，跳过重复声明');
} else {

    class UnifiedExtractor {
        constructor() {
            // 完整商品对象（按提取顺序构建）
            this.productData = {
                // 基础信息
                basic: {
                    skuId: null,
                    title: null,
                    pcUrl: null,
                    mobileUrl: null,
                    imageUrl: null
                },
                // 分类信息（按顺序：类目→品牌→型号）
                category: {
                    level1: '',
                    level2: '',
                    level3: '',
                    brand: '',
                    model: ''
                },
                // 价格信息（仅使用API数据中的p字段）
                price: {
                    originalPrice: null,     // 原价（仅从API的p字段获取）
                    bestPrice: null,         // 最优价格（等同于原价）
                    bestPriceType: '',       // 最优价格类型
                    priceDescription: ''     // 价格说明
                },
                // 新增：API拦截数据
                api: null
            };

            // 提取状态管理
            this.extractionStatus = {
                categoryExtracted: false,
                basicInfoExtracted: false,
                priceExtracted: false,
                allDataReady: false
            };

            // 异步处理配置
            this.asyncConfig = {
                priceRetryCount: 0,
                maxPriceRetries: 3,
                priceRetryDelay: 2000
            };

            // MutationObserver
            this.observer = null;
            this.initTime = Date.now();

            // console.log('🚀 统一元素提取器初始化完成');

            // --- 新增：在构造函数末尾发送就绪信号（延迟执行确保content_script.js已就绪） ---
            console.log('🚀 [Extractor] Instantiated. Will fire UnifiedExtractorReady event after delay.');
            setTimeout(() => {
                console.log('🚀 [Extractor] Firing UnifiedExtractorReady event now.');
                window.dispatchEvent(new CustomEvent('UnifiedExtractorReady'));
            }, 100);
        }

        /**
         * 新增：从localStorage读取API数据缓冲区
         * 这是一个兜底机制，确保即使事件丢失也能获取到API数据
         */
        checkLocalStorageApiBuffer() {
            try {
                // 首先尝试获取当前商品ID
                const currentSkuId = this.extractSkuId() || window.location.href.match(/\/(\d{6,})\.html/)?.[1];

                // 优先检查统一数据缓存
                let unifiedCacheKey = 'jd_unified_data_default';
                if (currentSkuId) {
                    unifiedCacheKey = `jd_unified_data_${currentSkuId}`;
                }

                let unifiedDataStr = localStorage.getItem(unifiedCacheKey);
                if (!unifiedDataStr && currentSkuId) {
                    unifiedDataStr = localStorage.getItem('jd_unified_data_default');
                }

                if (unifiedDataStr) {
                    const unifiedDataBuffer = JSON.parse(unifiedDataStr);
                    // 检查数据是否新鲜（5秒内）
                    if (Date.now() - unifiedDataBuffer.timestamp < 5000) {
                        console.log('📦 [Extractor] Found fresh unified data in localStorage:', unifiedDataBuffer);
                        this.processUnifiedData(unifiedDataBuffer);
                        localStorage.removeItem(unifiedCacheKey);
                        return true;
                    } else {
                        console.log('⏰ [Extractor] Unified data in localStorage is stale, clearing...');
                        localStorage.removeItem(unifiedCacheKey);
                        localStorage.removeItem('jd_unified_data_default');
                    }
                }

                // 兼容性：检查旧格式API数据
                let cacheKey = 'jd_api_data_buffer_default';
                if (currentSkuId) {
                    cacheKey = `jd_api_data_buffer_${currentSkuId}`;
                }

                let apiDataStr = localStorage.getItem(cacheKey);
                if (!apiDataStr && currentSkuId) {
                    apiDataStr = localStorage.getItem('jd_api_data_buffer_default');
                }

                if (apiDataStr) {
                    const apiDataBuffer = JSON.parse(apiDataStr);
                    // 检查数据是否新鲜（5秒内）
                    if (Date.now() - apiDataBuffer.timestamp < 5000) {
                        console.log('📦 [Extractor] Found fresh legacy API data, converting to unified format:', apiDataBuffer);
                        // 转换为统一格式并处理
                        const unifiedBuffer = {
                            source: 'legacy_cache',
                            type: 'product',
                            data: apiDataBuffer.data,
                            timestamp: apiDataBuffer.timestamp,
                            processed: true
                        };
                        this.processUnifiedData(unifiedBuffer);
                        localStorage.removeItem(cacheKey);
                        return true;
                    } else {
                        console.log('⏰ [Extractor] Legacy API data in localStorage is stale, clearing...');
                        localStorage.removeItem(cacheKey);
                        localStorage.removeItem('jd_api_data_buffer_default');
                    }
                }
            } catch (err) {
                console.error('❌ [Extractor] Error reading localStorage buffer:', err);
            }
            return false;
        }

        /**
         * 统一数据处理方法 - 不区分数据来源，统一处理所有数据
         * @param {object} unifiedDataBuffer - 统一数据格式的buffer对象
         */
        processUnifiedData(unifiedDataBuffer) {
            console.log('🎯 [Extractor] Received unified data:', unifiedDataBuffer);

            if (!unifiedDataBuffer || !unifiedDataBuffer.data) {
                console.warn('[Extractor] Unified data buffer is invalid. Skipping processing.');
                return;
            }

            const { source, type, data, timestamp } = unifiedDataBuffer;

            console.log(`[Extractor] Processing data from source: ${source}, type: ${type}`);

            // 存储原始数据
            this.productData.api = data;

            // 解析数据并更新内部数据模型（统一使用parseApiData方法）
            this.parseApiData(data);

            // 触发统一数据就绪事件
            document.dispatchEvent(new CustomEvent('unifiedDataReady', {
                detail: {
                    source,
                    type,
                    data: this.productData.api,
                    productData: this.productData
                }
            }));

            console.log(`✅ [Extractor] Unified data processed successfully from ${source}`);
        }

        /**
         * 新增：处理从 content_script 传入的API数据（保持兼容性）
         * @param {object} apiData - 从API拦截到的JSON数据
         */
        processInjectedApiData(apiData) {
            console.log('✅ [Extractor] Received API data via processInjectedApiData (legacy):', apiData);

            // 转换为统一数据格式并调用统一处理方法
            const unifiedDataBuffer = {
                source: 'legacy_api',
                type: 'product',
                data: apiData,
                timestamp: Date.now(),
                processed: true
            };

            this.processUnifiedData(unifiedDataBuffer);
        }

        /**
         * 解析API数据并更新productData
         * @param {object} apiData - API响应的JSON数据
         */
        parseApiData(apiData) {
            console.log('🔄 [Extractor] Parsing API data...');
            if (!apiData) {
                console.warn('[Extractor] API data is null or undefined. Skipping parsing.');
                return;
            }

            let updated = false;

            // 处理价格信息（仅使用p字段作为原价）
            if (apiData.price && typeof apiData.price === 'object') {
                // 处理原价 p 字段
                if (apiData.price.p && !isNaN(parseFloat(apiData.price.p))) {
                    const newPrice = parseFloat(apiData.price.p);
                    if (this.productData.price.originalPrice !== newPrice) {
                        this.productData.price.originalPrice = newPrice;
                        this.productData.price.bestPrice = newPrice;
                        this.productData.price.bestPriceType = '原价';
                        console.log(`[Extractor] API-UPDATE: originalPrice updated to ${newPrice}`);
                        updated = true;
                    }
                }

                // 处理商品ID
                if (apiData.price.id && this.productData.basic.skuId !== String(apiData.price.id)) {
                    this.productData.basic.skuId = String(apiData.price.id);
                    console.log(`[Extractor] API-UPDATE: skuId updated to ${apiData.price.id}`);
                    updated = true;
                }
            }

            // 从wareInfoReadMap提取基础信息
            if (apiData.wareInfoReadMap && typeof apiData.wareInfoReadMap === 'object') {
                const wareInfo = apiData.wareInfoReadMap;

                // 提取商品名称
                if (wareInfo.sku_name && this.productData.basic.title !== wareInfo.sku_name) {
                    this.productData.basic.title = wareInfo.sku_name;
                    console.log(`[Extractor] API-UPDATE: title updated from wareInfoReadMap`);
                    updated = true;
                }

                // 提取SKU ID
                if (wareInfo.product_id && this.productData.basic.skuId !== String(wareInfo.product_id)) {
                    this.productData.basic.skuId = String(wareInfo.product_id);
                    console.log(`[Extractor] API-UPDATE: skuId updated to ${wareInfo.product_id}`);
                    updated = true;
                }

                // 提取图片URL
                if (wareInfo.img_dfs_url && !this.productData.basic.imageUrl) {
                    this.productData.basic.imageUrl = `https://img12.360buyimg.com/n1/s720x720_${wareInfo.img_dfs_url}.avif`;
                    console.log(`[Extractor] API-UPDATE: imageUrl updated`);
                    updated = true;
                }
            }

            // 尝试从其他常见的API结构提取信息
            const itemInfo = apiData.item || apiData.ware || apiData.product;
            if (itemInfo && typeof itemInfo === 'object') {
                if (itemInfo.name && this.productData.basic.title !== itemInfo.name) {
                    this.productData.basic.title = itemInfo.name;
                    console.log(`[Extractor] API-UPDATE: title updated from item info`);
                    updated = true;
                }
                if (itemInfo.skuId && this.productData.basic.skuId !== String(itemInfo.skuId)) {
                    this.productData.basic.skuId = String(itemInfo.skuId);
                    console.log(`[Extractor] API-UPDATE: skuId updated to ${itemInfo.skuId}`);
                    updated = true;
                }
            }

            if (updated) {
                console.log('📦 [Extractor] Product data updated from API. Re-evaluating complete product object.');

                // 标记相关状态为已提取
                if (this.productData.basic.skuId && this.productData.basic.title) {
                    this.extractionStatus.basicInfoExtracted = true;
                    console.log('✅ [Extractor] Basic info extracted from API');
                }

                if (this.productData.price.originalPrice !== null) {
                    this.extractionStatus.priceExtracted = true;
                    console.log('✅ [Extractor] Price info extracted from API');
                }

                // 重新构建完整对象
                this.checkAndBuildCompleteProduct();

                // 发送基础信息就绪事件
                if (this.extractionStatus.basicInfoExtracted) {
                    document.dispatchEvent(new CustomEvent('basicInfoReady', {
                        detail: this.productData.basic
                    }));
                }
            } else {
                console.log('🤔 [Extractor] No new data parsed from this API response.');
            }
        }

        /**
         * 简化版初始化 - 按严格顺序提取
         */
        async initialize() {
            try {
                const isOnItemPage = location.href.includes('item.jd.com');
                const isOnOrderPage = location.href.includes('trade.jd.com/shopping/order') || location.href.includes('trade.m.jd.com/order/confirm');

                if (isOnItemPage) {
                    // 商品页完整初始化
                    // 首先检查是否有localStorage中的API数据缓冲区（兜底机制）
                    this.checkLocalStorageApiBuffer();

                    this.extractCategoryInfo();
                    await this.extractBasicInfo();
                    this.extractJdPrices();
                    this.checkAndBuildCompleteProduct();
                    this.checkForAutoOrderRetry(); // 检查重试请求
                } else if (isOnOrderPage) {
                    // 订单页，只需要确保能获取上一次购买数量和商品ID即可
                    // SKU ID可能需要从页面元素或sessionStorage获取，如果自动下单模块传递了
                    // console.log('统一元素提取器: 订单页初始化，尝试获取基础信息供自动下单模块使用。');
                    await this.extractBasicInfoFromOrderPage(); // 一个精简的提取，主要为了SKU
                    // 不需要完整的价格提取和第三方监控
                } else {
                    // 其他页面，可能只需要部分功能或不初始化
                    // console.log('统一元素提取器: 非商品页或订单页，按需初始化或不执行完整流程。');
                    // 可以根据需要决定是否执行部分提取
                }

            } catch (error) {
                console.error('❌ 统一元素提取器初始化失败:', error);
            }
        }

        async extractBasicInfoFromOrderPage() {
            // console.log('统一元素提取器: 尝试从订单页提取基础信息 (主要是SKU ID)');
            // 尝试从sessionStorage获取自动下单模块存储的商品ID
            const autoOrderProductId = sessionStorage.getItem('autoOrderProductId');
            if (autoOrderProductId) {
                this.productData.basic.skuId = autoOrderProductId;
                // console.log('统一元素提取器 (订单页): 从 sessionStorage 获取到 SKU ID:', autoOrderProductId);
                this.extractionStatus.basicInfoExtracted = true;
                return;
            }

            // 如果sessionStorage没有，尝试从订单页的元素中提取第一个商品的ID
            // 这需要根据订单页的实际HTML结构来确定选择器
            // 例如，PC端订单页的商品链接: .goods-item .p-name a
            // <a href="//item.jd.com/100107465921.html" target="_blank">...</a>
            const productLink = document.querySelector('.goods-item .p-name a[href*="item.jd.com"]');
            if (productLink && productLink.href) {
                const match = productLink.href.match(/item\.jd\.com\/(\d+)\.html/);
                if (match && match[1]) {
                    this.productData.basic.skuId = match[1];
                    this.extractionStatus.basicInfoExtracted = true;
                    // console.log('统一元素提取器 (订单页): 从页面元素提取到 SKU ID:', match[1]);
                    return;
                }
            }
            console.warn('统一元素提取器 (订单页): 未能提取到 SKU ID。');
        }

        /**
         * 检查并构建完整商品对象
         */
        checkAndBuildCompleteProduct() {
            // 检查基础数据是否完整
            const hasBasicData = this.productData.basic.skuId &&
                this.productData.basic.title;

            const hasPriceData = (this.productData.price.originalPrice !== null && this.productData.price.originalPrice >= 0);

            if (hasBasicData && hasPriceData) {
                // console.log('📦 基础数据完整，构建完整商品对象...');
                this.buildCompleteProductObject();
            } else {
                // console.log('⏳ 基础数据不完整，等待数据补全...', {
                //     hasBasicData,
                //     hasPriceData,
                //     skuId: this.productData.basic.skuId,
                //     title: !!this.productData.basic.title
                // });
            }
        }

        /**
         * 提取基础信息（SKU、URLs、标题）
         */
        async extractBasicInfo() {
            try {
                // 提取SKU（最重要）
                this.productData.basic.skuId = this.extractSkuId();

                // 提取URL（清理hash后缀）
                this.productData.basic.pcUrl = this.cleanUrl(window.location.href);
                this.productData.basic.mobileUrl = this.generateMobileUrl(this.productData.basic.skuId);

                // 提取标题
                this.productData.basic.title = this.extractTitle();

                // 提取主图URL
                this.productData.basic.imageUrl = this.extractImageUrl();

                // 标记基础信息提取完成
                this.extractionStatus.basicInfoExtracted = true;

                // 立即发送基础信息就绪事件（供快速复制等功能使用）
                document.dispatchEvent(new CustomEvent('basicInfoReady', {
                    detail: this.productData.basic
                }));

                return this.productData.basic;

            } catch (error) {
                console.error('❌ 基础信息提取失败:', error);
                return null;
            }
        }

        /**
         * 清理URL，移除hash和查询参数
         */
        cleanUrl(url) {
            try {
                const urlObj = new URL(url);
                // 移除hash和查询参数，只保留基础URL
                return `${urlObj.protocol}//${urlObj.host}${urlObj.pathname}`;
            } catch (error) {
                console.warn('URL清理失败，使用原URL:', error);
                // 备用方案：手动移除hash
                return url.split('#')[0].split('?')[0];
            }
        }

        /**
         * 生成正确的移动端URL
         */
        generateMobileUrl(skuId) {
            if (!skuId) return '';

            // 根据页面meta标签的格式生成正确的移动端URL
            // <meta http-equiv="mobile-agent" content="format=xhtml; url=//item.m.jd.com/product/10110700525298.html">
            return `https://item.m.jd.com/product/${skuId}.html`;
        }

        /**
         * 提取SKU ID
         */
        extractSkuId() {
            // 方法1：从URL提取 (主要用于商品详情页)
            if (location.href.includes('item.jd.com')) {
                const urlPatterns = [
                    /\/(\d{6,})\.html/,
                    /[\?&]sku=(\d{6,})/,
                    /item\.jd\.com\/(\d{6,})/
                ];
                const url = window.location.href;
                for (const pattern of urlPatterns) {
                    const match = url.match(pattern);
                    if (match && match[1]) {
                        // console.log('从URL提取到SKU:', match[1]);
                        return match[1];
                    }
                }
            }

            // 方法2：从页面元素提取 (主要用于商品详情页)
            if (location.href.includes('item.jd.com')) {
                const selectors = [
                    '[data-sku]',
                    '[data-id="skuID"]',
                    '#InitCartUrl',
                    '.product-intro'
                ];
                for (const selector of selectors) {
                    const element = document.querySelector(selector);
                    if (element) {
                        const skuMatch = element.textContent.match(/\d{6,}/) ||
                            element.getAttribute('data-sku')?.match(/\d{6,}/) ||
                            element.getAttribute('href')?.match(/\d{6,}/);
                        if (skuMatch && skuMatch[0]) {
                            // console.log('从页面元素提取到SKU:', skuMatch[0]);
                            return skuMatch[0];
                        }
                    }
                }
            }

            // 方法3: 从全局变量 (如 pageConfig, 主要用于商品详情页)
            if (location.href.includes('item.jd.com') && window.pageConfig && window.pageConfig.product && window.pageConfig.product.skuid) {
                // console.log('从 pageConfig 提取到SKU:', window.pageConfig.product.skuid);
                return String(window.pageConfig.product.skuid);
            }

            // 订单页的SKU提取已在 extractBasicInfoFromOrderPage 中处理
            if (this.productData.basic.skuId) { // 如果已通过其他方式（如订单页提取）获得
                return this.productData.basic.skuId;
            }

            console.warn('⚠️ 无法提取SKU ID');
            return null;
        }

        /**
         * 提取商品标题
         */
        extractTitle() {
            const selectors = [
                '.sku-name',
                '.product-intro h1',
                'h1',
                '.p-name a',
                '.itemInfo-wrap .sku-name'
            ];

            for (const selector of selectors) {
                const element = document.querySelector(selector);
                if (element && element.textContent.trim()) {
                    let title = element.textContent.trim();

                    // 清理标题：移除多余空格、换行符和"收藏"等后缀
                    title = title
                        .replace(/\s+/g, ' ')           // 多个空格替换为单个空格
                        .replace(/\n+/g, ' ')           // 换行符替换为空格
                        .replace(/\s*收藏\s*$/g, '')     // 移除末尾的"收藏"
                        .replace(/【.*?】/g, '')         // 移除方括号内容
                        .trim();

                    // console.log('提取到商品标题:', title.substring(0, 50) + '...');
                    return title;
                }
            }

            console.warn('⚠️ 无法提取商品标题');
            return '';
        }

        /**
         * 提取商品主图URL
         */
        extractImageUrl() {
            const selectors = [
                '#spec-img',
                '.jqzoom img',
                '.preview img',
                '.main-img img',
                '.product-intro img'
            ];

            for (const selector of selectors) {
                const element = document.querySelector(selector);
                if (element) {
                    let imageUrl = element.getAttribute('data-origin') ||
                        element.getAttribute('src') ||
                        element.getAttribute('data-src');

                    if (imageUrl) {
                        // 处理相对路径URL
                        if (imageUrl.startsWith('//')) {
                            imageUrl = 'https:' + imageUrl;
                        } else if (imageUrl.startsWith('/')) {
                            imageUrl = 'https://item.jd.com' + imageUrl;
                        }

                        // console.log('提取到商品主图URL:', imageUrl);
                        return imageUrl;
                    }
                }
            }

            console.warn('⚠️ 无法提取商品主图URL');
            return '';
        }

        /**
         * 提取分类信息（类目、品牌、型号）
         */
        extractCategoryInfo() {
            try {
                // 提取面包屑导航（类目）
                this.extractBreadcrumb();

                // 提取品牌信息
                this.extractBrandInfo();

                // 提取型号信息
                this.extractModelInfo();

                // 标记分类信息提取完成
                this.extractionStatus.categoryExtracted = true;

            } catch (error) {
                console.error('❌ 分类信息提取失败:', error);
            }
        }

        /**
         * 检查价格数据状态（仅依赖API数据中的p字段）
         */
        extractJdPrices() {
            console.log('💰 检查价格数据状态（仅依赖API数据中的p字段）...');

            // 检查是否已有API价格数据
            const hasApiPrice = (this.productData.price.originalPrice !== null && this.productData.price.originalPrice >= 0);

            if (hasApiPrice) {
                console.log('💰 已有API价格数据:', {
                    originalPrice: this.productData.price.originalPrice
                });
                this.extractionStatus.priceExtracted = true;
                this.checkCompleteProductReady();
            } else {
                console.warn('💰 尚未获取到API价格数据，等待API拦截...');
                // 设置一个短暂的等待，允许API数据到达
                setTimeout(() => {
                    if (!this.extractionStatus.priceExtracted) {
                        console.warn('💰 API价格数据等待超时，标记价格提取完成（无数据）');
                        this.extractionStatus.priceExtracted = true;
                        this.checkCompleteProductReady();
                    }
                }, 2000); // 2秒等待时间
            }
        }

        /**
         * 提取面包屑导航
         */
        extractBreadcrumb() {
            const selectors = [
                '.crumb.clearfix',
                '.crumb-wrap .crumb',
                '.breadcrumb',
                '.crumb',
                '.nav-breadcrumb'
            ];

            for (const selector of selectors) {
                const crumbContainer = document.querySelector(selector);
                if (crumbContainer) {
                    const links = crumbContainer.querySelectorAll('a');
                    const items = crumbContainer.querySelectorAll('.item');

                    // console.log('🏷️ 找到面包屑容器，链接数量:', links.length, '项目数量:', items.length);

                    if (links.length >= 3) {
                        // 根据实际HTML结构提取
                        this.productData.category.level1 = links[0]?.textContent?.trim() || '';  // 数码
                        this.productData.category.level2 = links[1]?.textContent?.trim() || '';  // 影音娱乐
                        this.productData.category.level3 = links[2]?.textContent?.trim() || '';  // 蓝牙/无线耳机

                        // 提取品牌（第4个链接）
                        if (links[3]) {
                            this.productData.category.brand = links[3].textContent?.trim() || '';
                        }

                        // 提取型号（最后一个item，通常没有链接）
                        const lastItem = items[items.length - 1];
                        if (lastItem && !lastItem.querySelector('a')) {
                            const modelText = lastItem.textContent?.trim() || lastItem.getAttribute('title')?.trim();
                            if (modelText) {
                                this.productData.category.model = modelText;
                            }
                        }

                        // console.log('🏷️ 从面包屑提取信息:', {
                        //     level1: this.productData.category.level1,
                        //     level2: this.productData.category.level2,
                        //     level3: this.productData.category.level3,
                        //     brand: this.productData.category.brand,
                        //     model: this.productData.category.model
                        // });
                        return;
                    }
                }
            }

            console.warn('⚠️ 无法从面包屑提取分类信息');
        }

        /**
         * 提取品牌信息
         */
        extractBrandInfo() {
            // 如果面包屑已经提取到品牌，就不再重复提取
            if (this.productData.category.brand) {
                // console.log('🏷️ 品牌已从面包屑提取:', this.productData.category.brand);
                return;
            }

            const selectors = [
                '[data-brand]',
                '.brand-name',
                '.p-brand',
                '.brand-info'
            ];

            // 方法1：从属性提取
            for (const selector of selectors) {
                const element = document.querySelector(selector);
                if (element) {
                    const brand = element.getAttribute('data-brand') ||
                        element.textContent?.trim();
                    if (brand) {
                        this.productData.category.brand = brand;
                        // console.log('🏷️ 从属性提取品牌:', brand);
                        return;
                    }
                }
            }

            // 方法2：从标题提取品牌
            const title = this.productData.basic.title;
            if (title) {
                // 常见品牌模式
                const brandPatterns = [
                    /^([A-Za-z]+)[\s\/]/,  // 英文品牌开头
                    /^([\u4e00-\u9fa5]+)[\s\/]/, // 中文品牌开头
                    /([A-Za-z]+\/[A-Za-z]+)/, // Apple/苹果 格式
                ];

                for (const pattern of brandPatterns) {
                    const match = title.match(pattern);
                    if (match) {
                        this.productData.category.brand = match[1];
                        // console.log('🏷️ 从标题提取品牌:', match[1]);
                        return;
                    }
                }
            }

            console.warn('⚠️ 无法提取品牌信息');
        }

        /**
         * 提取型号信息
         */
        extractModelInfo() {
            // 如果面包屑已经提取到型号，就不再重复提取
            if (this.productData.category.model) {
                // console.log('🏷️ 型号已从面包屑提取:', this.productData.category.model);
                return;
            }

            const title = this.productData.basic.title;
            if (!title) {
                console.warn('⚠️ 无标题信息，无法提取型号');
                return;
            }

            // 型号提取模式
            const modelPatterns = [
                /([A-Z0-9]{3,})\s/, // 大写字母数字组合
                /型号[：:]\s*([^\s]+)/, // 明确标注型号
                /款式[：:]\s*([^\s]+)/, // 款式信息
                /([A-Z]+[0-9]+[A-Z]*\/[A-Z]+)/, // MXP63CH/A 格式
                /([A-Z]+[0-9]+[A-Z]*)/, // MXP63CH 格式
            ];

            for (const pattern of modelPatterns) {
                const match = title.match(pattern);
                if (match) {
                    this.productData.category.model = match[1];
                    // console.log('🏷️ 从标题提取型号:', match[1]);
                    return;
                }
            }

            console.warn('⚠️ 无法从标题提取型号信息');
        }





        /**
         * 检查完整商品对象是否准备就绪
         */
        checkCompleteProductReady() {
            const { categoryExtracted, basicInfoExtracted, priceExtracted, allDataReady } = this.extractionStatus;

            // console.log('🔄 checkCompleteProductReady: Status:', { categoryExtracted, basicInfoExtracted, priceExtracted, allDataReady });

            if (categoryExtracted && basicInfoExtracted && priceExtracted && !allDataReady) {
                // console.log('📦 所有主要数据源已处理完毕，构建/更新完整商品对象...');
                this.buildCompleteProductObject();
            } else {
                let waitingFor = [];
                if (!categoryExtracted) waitingFor.push('category');
                if (!basicInfoExtracted) waitingFor.push('basic info');
                if (!priceExtracted) waitingFor.push('price');
                if (allDataReady) {
                    // console.log('📦 商品对象已构建过。若有后续更新，相关流程应确保再次评估最优价格。');
                } else if (waitingFor.length > 0) {
                    // console.log(`⏳ 等待以下数据提取完成: ${waitingFor.join(', ')}`);
                } else {
                    // console.log('⏳ 状态未知或所有条件未满足，不构建对象。');
                }
            }
        }

        /**
         * 构建完整商品对象
         */
        buildCompleteProductObject() {
            // console.log('📦 构建完整商品对象...');

            // 标记所有数据准备就绪
            this.extractionStatus.allDataReady = true;

            // console.log('✅ 完整商品对象构建完成:', this.productData);

            // 发送完整数据就绪事件
            document.dispatchEvent(new CustomEvent('completeProductDataReady', {
                detail: this.productData
            }));
        }

        /**
         * 异步提取价格信息（保留兼容性）
         */
        async extractPriceInfoAsync() {
            // console.log('💰 兼容性方法：重定向到新的价格提取流程...');
            this.extractJdPrices();
        }

        // ==================== 事件通知方法（新架构） ====================

        /**
         * 通知基础信息已准备就绪（兼容性方法）
         */
        notifyBasicInfoReady() {
            console.log('📢 基础信息已准备就绪（兼容性方法）');
            // 新架构中基础信息在extractBasicInfo中直接发送事件
        }

        /**
         * 通知价格信息已准备就绪（兼容性方法）
         */
        notifyPriceInfoReady() {
            console.log('📢 价格信息已准备就绪（兼容性方法）');
            // 新架构中不需要单独通知价格信息
        }

        // 兼容性方法已删除 - 请使用新的事件驱动架构

        // ==================== 公共API接口 ====================

        /**
         * 获取SKU ID
         */
        getSkuId() {
            return this.productData.basic.skuId;
        }

        /**
         * 获取商品标题
         */
        getTitle() {
            return this.productData.basic.title;
        }

        /**
         * 获取商品URL
         */
        getUrl() {
            return {
                pc: this.productData.basic.pcUrl,
                mobile: this.productData.basic.mobileUrl
            };
        }

        /**
         * 获取商品主图URL
         */
        getImageUrl() {
            return this.productData.basic.imageUrl;
        }

        /**
         * 获取基础信息
         */
        getBasicInfo() {
            return { ...this.productData.basic };
        }

        /**
         * 获取价格信息
         */
        getPriceInfo() {
            return { ...this.productData.price };
        }

        /**
         * 获取最优价格
         */
        getBestPrice() {
            return {
                price: this.productData.price.bestPrice,
                type: this.productData.price.bestPriceType,
                description: this.productData.price.priceDescription
            };
        }

        /**
         * 获取当前所有价格信息（价格管理器兼容性接口）
         */
        getCurrentPrices() {
            return {
                currentPrice: this.productData.price.originalPrice,
                bestPrice: this.productData.price.bestPrice,
                bestPriceType: this.productData.price.bestPriceType
            };
        }

        /**
         * 检查是否有有效价格数据（价格管理器兼容性接口）
         */
        hasValidPrice() {
            return (this.productData.price.bestPrice !== null && this.productData.price.bestPrice >= 0) ||
                (this.productData.price.originalPrice !== null && this.productData.price.originalPrice >= 0);
        }

        /**
         * 获取所有价格
         */
        getAllPrices() {
            return {
                currentPrice: this.productData.price.originalPrice,
                bestPrice: this.productData.price.bestPrice,
                bestPriceType: this.productData.price.bestPriceType
            };
        }

        /**
         * 获取分类信息
         */
        getCategoryInfo() {
            return { ...this.productData.category };
        }

        /**
         * 获取完整商品信息
         */
        getProductInfo() {
            return {
                basic: this.getBasicInfo(),
                category: this.getCategoryInfo(),
                price: this.getPriceInfo(),
                status: this.extractionStatus
            };
        }

        /**
         * 获取所有数据
         */
        getAllData() {
            return this.getProductInfo();
        }

        /**
         * 获取完整商品数据（供外部调用）
         */
        getCompleteProductData() {
            console.log('📦 统一元素提取器: 返回完整商品数据', JSON.parse(JSON.stringify(this.productData)));
            return this.productData;
        }

        /**
         * 强制刷新价格信息
         */
        forceRefreshPriceInfo() {
            console.log('🔄 强制刷新价格信息...');
            this.priceRetryCount = 0;
            this.isPriceInfoReady = false;
            this.extractPriceInfoAsync();
        }

        // ==================== 兼容性事件监听接口 ====================

        /**
         * 监听基础信息准备就绪事件（兼容性方法）
         */
        onBasicInfoReady(callback) {
            if (typeof callback === 'function') {
                // 如果基础信息已经准备就绪，立即调用
                if (this.extractionStatus.basicInfoExtracted) {
                    callback(this.getBasicInfo());
                } else {
                    // 否则监听基础信息就绪事件
                    document.addEventListener('basicInfoReady', (event) => {
                        callback(event.detail);
                    }, { once: true });
                }
            }
        }

        /**
         * 监听完整数据准备就绪事件（兼容性方法）
         */
        onAllDataReady(callback) {
            if (typeof callback === 'function') {
                // 如果完整数据已经准备就绪，立即调用
                if (this.extractionStatus.allDataReady) {
                    callback(this.getCompleteProductData());
                } else {
                    // 否则监听完整数据就绪事件
                    document.addEventListener('completeProductDataReady', (event) => {
                        callback(event.detail);
                    }, { once: true });
                }
            }
        }

        /**
         * 获取状态信息
         */
        getStatus() {
            return {
                extractionStatus: this.extractionStatus,
                asyncConfig: this.asyncConfig,
                productData: this.productData
            };
        }

        /**
         * 检查是否有来自自动下单模块的重试请求
         */
        checkForAutoOrderRetry() {
            const retryActionStr = sessionStorage.getItem('oneClickBuyNextAction');
            if (retryActionStr) {
                try {
                    const retryAction = JSON.parse(retryActionStr);
                    if (retryAction.source === 'autoOrderRetry' && retryAction.type === 'buyNow' && window.OneClickBuy && typeof window.OneClickBuy.openBuyNow === 'function') {
                        console.log('统一元素提取器: 检测到自动下单模块的重试请求。');

                        // 确保 basicInfoExtracted 为 true，并且 skuId 匹配
                        if (this.extractionStatus.basicInfoExtracted && this.productData.basic.skuId === retryAction.productId) {
                            console.log(`统一元素提取器: 执行单件购买，商品ID: ${retryAction.productId}, 数量: ${retryAction.quantity || 1}`);
                            window.OneClickBuy.openBuyNow(retryAction.quantity || 1);
                            sessionStorage.removeItem('oneClickBuyNextAction');
                            sessionStorage.removeItem('autoOrderProductId'); // 清理商品ID
                        } else {
                            console.warn('统一元素提取器: 收到重试请求，但基础数据未就绪或商品ID不匹配，稍后重试检查。',
                                {
                                    basicReady: this.extractionStatus.basicInfoExtracted,
                                    currentSku: this.productData.basic.skuId,
                                    expectedSku: retryAction.productId
                                });
                            setTimeout(() => this.checkForAutoOrderRetry(), 1000);
                        }
                    }
                } catch (e) {
                    console.error('统一元素提取器: 解析重试请求失败', e);
                    sessionStorage.removeItem('oneClickBuyNextAction');
                }
            }
        }

        getProductId() { // 新增方法供自动下单模块调用
            return this.productData.basic.skuId;
        }

        /**
         * 获取活动购买数量 (供自动下单模块使用)
         * 由于已移除第三方数据，返回默认值1
         */
        getActivityBuyQuantity() {
            console.log('统一元素提取器: getActivityBuyQuantity 已移除第三方数据支持，返回默认值 1');
            return 1; // 默认返回1件
        }
    }

    // 创建全局实例
    window.UnifiedExtractor = UnifiedExtractor;
    const instance = new UnifiedExtractor();
    window.unifiedExtractor = instance;
    window.unifiedElementExtractor = instance; // 兼容content_script.js中的变量名

    // 兼容性接口已删除 - 请直接使用 window.unifiedExtractor

    // ThirdPartyDataExtractor 兼容性接口已删除 - 请直接使用 window.unifiedExtractor.getThirdPartyData()

    // PriceManager 兼容性接口已删除 - 请直接使用 window.unifiedExtractor.getBestPrice() 等方法

    console.log('统一元素提取器加载完成');

    // 清理过期的API数据缓存
    (() => {
        try {
            const now = Date.now();
            const keys = Object.keys(localStorage);
            keys.forEach(key => {
                if (key.startsWith('jd_api_data_buffer_')) {
                    try {
                        const data = JSON.parse(localStorage.getItem(key));
                        if (data && data.timestamp && (now - data.timestamp > 5000)) {
                            console.log(`🧹 [Extractor] Removing stale cache: ${key}`);
                            localStorage.removeItem(key);
                        }
                    } catch (e) {
                        // 如果解析失败，直接删除
                        localStorage.removeItem(key);
                    }
                }
            });
        } catch (e) {
            console.warn('清理API缓存时出错:', e);
        }
    })();

    // 自动初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            instance.initialize();
        });
    } else {
        // 页面已加载完成，立即初始化
        setTimeout(() => {
            instance.initialize();
        }, 100);
    }

} // 结束 if 语句
