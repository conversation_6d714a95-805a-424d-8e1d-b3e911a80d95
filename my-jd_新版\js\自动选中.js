// 添加开关变量和时间检测变量 
let isAutoSelectEnabled = true;
let lastCheckTime = 0;
const CHECK_INTERVAL = 3000; // 减少到3秒检测间隔

// 优化自动选中购买数量函数
function focusAndSelectBuyNum() {
    if (!isAutoSelectEnabled) return;
    
    const now = Date.now();
    if (now - lastCheckTime < CHECK_INTERVAL) return;
    
    lastCheckTime = now;
    const buyNumInput = document.querySelector("#buy-num");
    
    if (buyNumInput && buyNumInput.offsetParent !== null) { // 检查元素是否可见
        // 使用requestAnimationFrame优化性能
        requestAnimationFrame(() => {
            buyNumInput.focus();
            buyNumInput.select();
        });
        
        if (!buyNumInput._ltby_select_listener) {
            buyNumInput.addEventListener('click', function() {
                if (isAutoSelectEnabled) {
                    this.select();
                }
            }, { passive: true }); // 使用passive监听器
            buyNumInput._ltby_select_listener = true;
        }
    }
}

// 优化定时检查函数
function startPeriodicCheck() {
    // 使用requestIdleCallback在浏览器空闲时执行
    function idleCheck() {
        if (isAutoSelectEnabled) {
            focusAndSelectBuyNum();
        }
        // 使用setTimeout而不是setInterval，避免累积延迟
        setTimeout(() => {
            if (window.requestIdleCallback) {
                requestIdleCallback(idleCheck);
            } else {
                idleCheck();
            }
        }, CHECK_INTERVAL);
    }
    
    if (window.requestIdleCallback) {
        requestIdleCallback(idleCheck);
    } else {
        setTimeout(idleCheck, CHECK_INTERVAL);
    }
}

// 添加开关切换函数
function toggleAutoSelect() {
    isAutoSelectEnabled = !isAutoSelectEnabled;
    // 显示当前状态
    const status = isAutoSelectEnabled ? '启用' : '禁用';
    const tip = document.createElement('div');
    tip.className = 'ltby-status-tip';
    tip.textContent = `动态选中已${status}`;
    tip.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 8px 16px;
        border-radius: 4px;
        z-index: 9999;
    `;
    document.body.appendChild(tip);
    setTimeout(() => tip.remove(), 2000);
}

// 监听方向键上
document.addEventListener('keyup', function(event) {
    if (event.key === 'ArrowUp') {
        toggleAutoSelect();
    }
});

// 修改初始化逻辑
function initialize() {
    focusAndSelectBuyNum();
    startPeriodicCheck();
}

// 调整 MutationObserver 的回调,保持即时响应
const observer = new MutationObserver((mutations) => {
    // 只在检测到购买数量输入框变化时才执行
    for (let mutation of mutations) {
        if (mutation.type === 'childList') {
            for (let node of mutation.addedNodes) {
                if (node.nodeType === 1 && // 元素节点
                    (node.id === 'buy-num' || node.querySelector('#buy-num'))) {
                    requestAnimationFrame(() => {
                        focusAndSelectBuyNum();
                    });
                    return;
                }
            }
        }
    }
});

// 更精确的观察配置
observer.observe(document.body, { 
    childList: true, 
    subtree: true,
    attributes: false, // 不监听属性变化
    characterData: false // 不监听文本变化
});

// 修改初始化调用
initialize();