<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>一键购买国际站支持测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            color: #333;
            margin-bottom: 15px;
            font-weight: bold;
        }
        .test-button {
            background: #007bff;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .success {
            border-left-color: #28a745;
        }
        .error {
            border-left-color: #dc3545;
        }
        
        /* 模拟中国站样式 */
        .choose-btns-wrapper {
            border: 2px solid #28a745;
            padding: 10px;
            margin: 10px 0;
            background: #f8fff8;
        }
        .choose-btns-wrapper::before {
            content: "中国站插入点：.choose-btns-wrapper";
            display: block;
            font-weight: bold;
            color: #28a745;
            margin-bottom: 10px;
        }
        
        /* 模拟国际站样式 */
        .choose-btns.clearfix {
            border: 2px solid #007bff;
            padding: 10px;
            margin: 10px 0;
            background: #f8f9ff;
        }
        .choose-btns.clearfix::before {
            content: "国际站插入点：.choose-btns.clearfix";
            display: block;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
        }
        .choose-amount {
            border: 1px solid #ccc;
            padding: 8px;
            margin: 5px 0;
            background: #fff;
        }
        .choose-amount::before {
            content: "数量选择器：.choose-amount";
            display: block;
            font-size: 12px;
            color: #666;
        }
        
        /* LTBY按钮样式 */
        .ltby-quick-buy-container {
            border: 2px solid #ff6b6b;
            background: #fff5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .ltby-quick-buy-wrapper {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .ltby-brand {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
            color: #666;
        }
        .ltby-buttons {
            display: flex;
            gap: 5px;
        }
        .ltby-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        .ltby-btn-primary {
            background: #007bff;
            color: white;
        }
        .ltby-btn-success {
            background: #28a745;
            color: white;
        }
        
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>一键购买国际站支持测试</h1>
    
    <div class="test-container">
        <h2>测试说明</h2>
        <p>本测试用于验证一键购买模块对国际站的支持：</p>
        <ul>
            <li><strong>中国站</strong>：插入到 <code>.choose-btns-wrapper</code> 内部</li>
            <li><strong>国际站</strong>：插入到 <code>.choose-btns.clearfix</code> 内部，<code>.choose-amount</code> 下方平级</li>
        </ul>
    </div>

    <div class="test-container">
        <div class="test-section">
            <div class="test-title">1. 域名环境切换</div>
            <p>切换模拟不同的域名环境：</p>
            
            <button class="test-button" onclick="simulateChina()">模拟中国站 (item.jd.com)</button>
            <button class="test-button" onclick="simulateHongKong()">模拟国际站 (npcitem.jd.hk)</button>
            <button class="test-button" onclick="clearSimulation()">清除模拟</button>
            
            <div id="domainResult"></div>
        </div>

        <div class="test-section">
            <div class="test-title">2. 中国站布局测试</div>
            <p>模拟中国站的页面结构：</p>
            
            <button class="test-button" onclick="createChinaLayout()">创建中国站布局</button>
            <button class="test-button" onclick="testChinaInsertion()">测试中国站插入</button>
            
            <div id="chinaContainer"></div>
        </div>

        <div class="test-section">
            <div class="test-title">3. 国际站布局测试</div>
            <p>模拟国际站的页面结构：</p>
            
            <button class="test-button" onclick="createHKLayout()">创建国际站布局</button>
            <button class="test-button" onclick="testHKInsertion()">测试国际站插入</button>
            
            <div id="hkContainer"></div>
        </div>

        <div class="test-section">
            <div class="test-title">4. 动态加载测试</div>
            <p>测试动态添加元素时的按钮重新插入：</p>
            
            <button class="test-button" onclick="testDynamicLoading()">测试动态加载</button>
            <button class="test-button" onclick="clearAllButtons()">清除所有按钮</button>
            
            <div id="dynamicResult"></div>
        </div>
    </div>

    <div class="test-container">
        <h2>控制台日志</h2>
        <p>详细的测试日志将在浏览器控制台中显示。按F12打开开发者工具查看。</p>
        <pre id="consoleOutput"></pre>
    </div>

    <script>
        // 模拟 Chrome 扩展 API
        window.chrome = window.chrome || {
            runtime: {
                getURL: (path) => `./${path}`
            }
        };

        // 模拟一键购买模块的核心逻辑
        const MockQuickBuy = {
            insertQuickBuyButton() {
                console.log('🔍 开始插入按钮检测...');
                
                // 检查是否已存在
                if (document.querySelector('#ltby-quick-buy-root')) {
                    console.log('LTBY一键购买: 按钮已存在，跳过插入');
                    return true;
                }

                // 根据域名判断插入位置
                const currentHost = window.location.hostname;
                let insertTarget = null;
                let insertMethod = 'append';

                if (currentHost.includes('jd.hk')) {
                    // 国际站逻辑
                    console.log('LTBY一键购买: 检测到国际站环境');
                    const chooseBtns = document.querySelector('.choose-btns.clearfix');
                    const chooseAmount = chooseBtns ? chooseBtns.querySelector('.choose-amount') : null;
                    
                    if (chooseBtns && chooseAmount) {
                        insertTarget = chooseAmount;
                        insertMethod = 'insertAfter';
                        console.log('LTBY一键购买: 找到国际站插入点 .choose-amount');
                    } else {
                        console.log('LTBY一键购买: 未找到国际站插入点，等待加载...');
                        return false;
                    }
                } else {
                    // 中国站逻辑
                    console.log('LTBY一键购买: 检测到中国站环境');
                    const chooseWrapper = document.querySelector('.choose-btns-wrapper');
                    if (chooseWrapper) {
                        insertTarget = chooseWrapper;
                        insertMethod = 'append';
                        console.log('LTBY一键购买: 找到中国站插入点 .choose-btns-wrapper');
                    } else {
                        console.log('LTBY一键购买: 未找到中国站插入点，等待加载...');
                        return false;
                    }
                }

                if (!insertTarget) {
                    console.log('LTBY一键购买: 未找到适合的插入点，等待动态加载...');
                    return false;
                }

                try {
                    // 创建按钮容器
                    const btnContainer = document.createElement('div');
                    btnContainer.id = 'ltby-quick-buy-root';
                    btnContainer.className = 'ltby-quick-buy-container';

                    btnContainer.innerHTML = `
                        <div class="ltby-quick-buy-wrapper">
                            <div class="ltby-brand">
                                <img src="./images/icon16.png" alt="LTBY助手" width="16" height="16">
                                <span>LTBY助手</span>
                            </div>
                            <div class="ltby-buttons">
                                <button type="button" class="ltby-btn ltby-btn-primary">一键购买</button>
                                <button type="button" class="ltby-btn ltby-btn-primary">触屏购买</button>
                                <button type="button" class="ltby-btn ltby-btn-success">活动价购买</button>
                            </div>
                        </div>
                    `;

                    // 根据插入方法进行插入
                    if (insertMethod === 'append') {
                        insertTarget.appendChild(btnContainer);
                    } else if (insertMethod === 'insertAfter') {
                        if (insertTarget.nextSibling) {
                            insertTarget.parentNode.insertBefore(btnContainer, insertTarget.nextSibling);
                        } else {
                            insertTarget.parentNode.appendChild(btnContainer);
                        }
                    }

                    console.log('LTBY一键购买: 按钮插入成功，插入方法:', insertMethod, '域名:', currentHost);
                    return true;

                } catch (error) {
                    console.error('LTBY一键购买: 按钮插入失败:', error);
                    return false;
                }
            },

            validateAndFixButtonPosition() {
                const existingButton = document.querySelector('#ltby-quick-buy-root');
                if (!existingButton) {
                    return false;
                }

                const currentHost = window.location.hostname;
                let targetContainer = null;

                if (currentHost.includes('jd.hk')) {
                    targetContainer = document.querySelector('.choose-btns.clearfix');
                } else {
                    targetContainer = document.querySelector('.choose-btns-wrapper');
                }

                if (!targetContainer) {
                    return false;
                }

                const buttonInCorrectContainer = targetContainer.contains(existingButton);
                
                if (!buttonInCorrectContainer) {
                    console.log('LTBY: 按钮位置不正确，尝试重新定位...', '域名:', currentHost);
                    existingButton.remove();
                    this.insertQuickBuyButton();
                    return true;
                }

                return true;
            }
        };

        // 域名模拟函数
        function simulateChina() {
            // 模拟修改 hostname
            Object.defineProperty(window.location, 'hostname', {
                value: 'item.jd.com',
                writable: true,
                configurable: true
            });
            
            document.getElementById('domainResult').innerHTML = `
                <div class="result success">
                    <strong>已模拟中国站环境</strong><br>
                    域名: ${window.location.hostname}
                </div>
            `;
            
            console.log('🎭 模拟中国站环境:', window.location.hostname);
        }

        function simulateHongKong() {
            Object.defineProperty(window.location, 'hostname', {
                value: 'npcitem.jd.hk',
                writable: true,
                configurable: true
            });
            
            document.getElementById('domainResult').innerHTML = `
                <div class="result success">
                    <strong>已模拟国际站环境</strong><br>
                    域名: ${window.location.hostname}
                </div>
            `;
            
            console.log('🎭 模拟国际站环境:', window.location.hostname);
        }

        function clearSimulation() {
            document.getElementById('domainResult').innerHTML = `
                <div class="result">
                    <strong>模拟环境已清除</strong><br>
                    当前域名: ${window.location.hostname}
                </div>
            `;
            
            console.log('🧹 清除模拟环境');
        }

        // 布局创建函数
        function createChinaLayout() {
            const container = document.getElementById('chinaContainer');
            container.innerHTML = `
                <div class="choose-btns-wrapper">
                    <p>这里是中国站的购物车按钮区域</p>
                    <button>加入购物车</button>
                    <button>立即购买</button>
                </div>
            `;
            console.log('📋 创建中国站布局');
        }

        function createHKLayout() {
            const container = document.getElementById('hkContainer');
            container.innerHTML = `
                <div class="choose-btns clearfix">
                    <div class="choose-amount">
                        <p>数量选择器区域</p>
                        <input type="number" value="1" min="1">
                    </div>
                    <button>加入购物车</button>
                    <button>立即购买</button>
                </div>
            `;
            console.log('📋 创建国际站布局');
        }

        function testChinaInsertion() {
            simulateChina();
            const success = MockQuickBuy.insertQuickBuyButton();
            console.log('🧪 中国站插入测试结果:', success ? '成功' : '失败');
        }

        function testHKInsertion() {
            simulateHongKong();
            const success = MockQuickBuy.insertQuickBuyButton();
            console.log('🧪 国际站插入测试结果:', success ? '成功' : '失败');
        }

        function testDynamicLoading() {
            console.log('🔄 测试动态加载...');
            
            // 清除现有布局
            document.getElementById('chinaContainer').innerHTML = '';
            document.getElementById('hkContainer').innerHTML = '';
            clearAllButtons();
            
            // 模拟动态加载过程
            setTimeout(() => {
                console.log('🔄 第1步：模拟中国站动态加载');
                simulateChina();
                createChinaLayout();
                testChinaInsertion();
            }, 500);
            
            setTimeout(() => {
                console.log('🔄 第2步：切换到国际站');
                simulateHongKong();
                createHKLayout();
                testHKInsertion();
            }, 1500);
            
            document.getElementById('dynamicResult').innerHTML = `
                <div class="result">
                    <strong>动态加载测试进行中...</strong><br>
                    请查看控制台日志和页面变化
                </div>
            `;
        }

        function clearAllButtons() {
            const buttons = document.querySelectorAll('#ltby-quick-buy-root');
            buttons.forEach(btn => btn.remove());
            console.log('🧹 清除所有LTBY按钮');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📋 一键购买国际站支持测试页面已加载');
            console.log('🔍 当前域名:', window.location.hostname);
            
            // 自动进行基础测试
            setTimeout(() => {
                console.log('\n🚀 执行自动基础测试...');
                createChinaLayout();
                createHKLayout();
            }, 1000);
        });

        // 捕获控制台输出
        const originalConsoleLog = console.log;
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            
            const output = document.getElementById('consoleOutput');
            if (output) {
                const message = args.map(arg => 
                    typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
                ).join(' ');
                output.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
                output.scrollTop = output.scrollHeight;
            }
        };
    </script>
</body>
</html>
