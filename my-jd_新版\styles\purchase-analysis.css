/* purchase-analysis.css */
#purchase-analysis-root-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    font-family: Arial, sans-serif;
    font-size: 12px;
}

#dashboard-container {
    width: 220px;
    border: 1px solid #007bff;
    background-color: white;
    margin-bottom: 5px;
    /* Reduced space */
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    /* Add pointer cursor to indicate it's interactive */
    transition: all 0.3s ease;
}

#dashboard-container:hover {
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.dashboard-header {
    background-color: #007bff;
    color: white;
    padding: 5px 10px;
    font-weight: bold;
    cursor: grab;
    user-select: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dashboard-header:active {
    cursor: grabbing;
}

.dashboard-content {
    padding: 5px;
}

.dashboard-item {
    display: flex;
    justify-content: space-between;
    padding: 3px 5px;
    border-bottom: 1px solid #eee;
}

.dashboard-item:last-child {
    border-bottom: none;
}

.dashboard-label {
    color: #333;
}

.dashboard-value {
    font-weight: bold;
    color: #ff4400;
}

#purchase-analysis-container {
    width: 450px;
    border: 1px solid #ccc;
    background-color: white;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    /* Removed position, z-index etc. as it's now controlled by the root container */
}

#purchase-analysis-container .header {
    background-color: #f5f5f5;
    padding: 5px 10px;
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#purchase-analysis-container .header button {
    margin-left: 5px;
}

#close-analysis-btn {
    border: none;
    background: none;
    font-size: 16px;
    cursor: pointer;
}

#purchase-analysis-container .content {
    padding: 10px;
}

#purchase-analysis-container .summary {
    display: flex;
    justify-content: space-around;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

#purchase-analysis-container .summary-item p {
    margin: 0;
}

#purchase-analysis-container .summary-item p:first-child {
    color: #888;
}

#purchase-analysis-container .sub-summary {
    padding: 5px 0;
    color: #888;
}

#purchase-analysis-container .promotions ul {
    list-style-type: none;
    padding-left: 0;
}

#purchase-analysis-container .promotions li {
    margin-bottom: 5px;
}

#purchase-analysis-container .promotions li span {
    margin-right: 10px;
}

#purchase-analysis-container .promotions li button {
    margin-left: 5px;
}

#purchase-analysis-container .details .tabs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ccc;
    padding-bottom: 5px;
}

#purchase-analysis-container .details .tab-link {
    background-color: inherit;
    float: left;
    border: none;
    outline: none;
    cursor: pointer;
    padding: 10px 15px;
    transition: 0.3s;
}

#purchase-analysis-container .details .tab-link.active {
    background-color: #ddd;
    font-weight: bold;
}

#purchase-analysis-container .details .switch-view button.active {
    background-color: #ddd;
}

#purchase-analysis-container .details .tab-content {
    display: block;
    /* Always show */
    padding: 10px 0;
}

#purchase-analysis-container .details table {
    width: 100%;
    border-collapse: collapse;
}

#purchase-analysis-container .details th,
#purchase-analysis-container .details td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

#purchase-analysis-container .details th {
    background-color: #f2f2f2;
}

#purchase-analysis-container .details .optimal {
    background-color: #ff4400;
    color: white;
    border: none;
    padding: 2px 5px;
}

#purchase-analysis-container .details .sub-optimal {
    background-color: #ff8c00;
    color: white;
    border: none;
    padding: 2px 5px;
}

#purchase-analysis-container.hidden {
    display: none;
}

#purchase-analysis-container .pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 10px;
}

#purchase-analysis-container .pagination a,
#purchase-analysis-container .pagination span {
    margin: 0 5px;
}

#purchase-analysis-container .pagination select {
    margin-left: 10px;
}
