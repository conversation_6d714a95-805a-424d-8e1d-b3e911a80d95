// promoTags分组测试脚本
// 可以在Node.js环境中运行

// 模拟JDPromotionCalculator类的分组方法
class TestCalculator {
    constructor() {
        this.debug = true;
    }
    
    log(message, ...args) {
        if (this.debug) {
            console.log(message, ...args);
        }
    }
    
    groupPromotionsByText(promotions) {
        const groups = {};
        
        promotions.forEach(promotion => {
            let groupKey;
            
            // 检查是否有promoTags字段
            if (promotion.promoTags && Array.isArray(promotion.promoTags) && promotion.promoTags.length > 0) {
                // 有promoTags时，使用promoTags的第一个值作为分组依据
                // 将promoTags数组排序后转换为字符串，确保相同标签组合的促销被分为同一组
                const sortedTags = [...promotion.promoTags].sort((a, b) => a - b);
                groupKey = `promoTags_${sortedTags.join('_')}`;
                
                this.log(`促销[${promotion.text}]有promoTags${JSON.stringify(promotion.promoTags)}，分组key: ${groupKey}`);
            } else {
                // 没有promoTags时，使用原有的text字段分组
                groupKey = `text_${promotion.text || 'unknown'}`;
                
                this.log(`促销[${promotion.text}]无promoTags，使用text分组，分组key: ${groupKey}`);
            }
            
            if (!groups[groupKey]) {
                groups[groupKey] = [];
            }
            groups[groupKey].push(promotion);
        });
        
        this.log('促销分组结果:', Object.keys(groups).map(key => ({
            groupKey: key,
            count: groups[key].length,
            promotions: groups[key].map(p => ({text: p.text, tag: p.tag, promoTags: p.promoTags}))
        })));
        
        return groups;
    }
}

// 测试数据
const testPromotions = [
    {
        "text": "PLUS95折",
        "tag": 40,
        "promoTags": [30],
        "shortText": "PLUS95折 立减2.34元"
    },
    {
        "text": "满减",
        "tag": 15,
        "promoTags": [39],
        "value": "66元选3件"
    },
    {
        "text": "多买优惠",
        "tag": 19,
        "promoTags": [39],
        "shortText": "满3件享7折",
        "value": "满2件，总价打8折；满3件，总价打7折"
    }
];

// 执行测试
console.log('🧪 开始promoTags分组测试...');
console.log('📊 测试数据:', testPromotions);

const calculator = new TestCalculator();
const groups = calculator.groupPromotionsByText(testPromotions);

console.log('\n🏷️ 最终分组结果:');
Object.entries(groups).forEach(([key, promos]) => {
    console.log(`组 "${key}":`);
    promos.forEach(p => {
        console.log(`  - ${p.text} (tag:${p.tag}, promoTags:${JSON.stringify(p.promoTags)})`);
    });
    console.log(`  共 ${promos.length} 个促销\n`);
});

console.log('✨ 验证结果:');
console.log('- PLUS95折 (promoTags:[30])：独立分组 ✅');
console.log('- 满减和多买优惠 (都是promoTags:[39])：被分为同一组 ✅');
console.log('- 同一组内的促销不可叠加，会选择最优的一个 ✅');
