/**
 * 统一WebSocket发送模块 - 中间件版
 * 专门用于连接到部署在39.105.176.239的WebSocket中间件服务器
 * 地址：wss://z777.top/ws
 */

class MiddlewareWebSocketSender {
    // 常量定义
    static DEFAULTS = {
        MIDDLEWARE_WS_URL: 'wss://zzz7.top/ws',
        RETRY_COUNT: 3,
        TIMEOUT: 15000,
        RECONNECT_DELAY: 2000,
        HEARTBEAT_INTERVAL: 30000
    };

    constructor() {
        this.isEnabled = true;
        this.ws = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.heartbeatInterval = null;
        this.messageQueue = []; // 消息队列，连接建立前缓存消息
        this.connectionPromise = null; // 避免重复连接

        // console.log('🔄 中间件WebSocket发送模块初始化完成');
        // console.log('📡 中间件地址:', MiddlewareWebSocketSender.DEFAULTS.MIDDLEWARE_WS_URL);
    }

    /**
     * 连接到WebSocket中间件
     */
    async connect() {
        // 如果正在连接，等待现有连接完成
        if (this.connectionPromise) {
            return this.connectionPromise;
        }

        // 如果已连接，直接返回
        if (this.isConnected && this.ws && this.ws.readyState === WebSocket.OPEN) {
            return Promise.resolve();
        }

        this.connectionPromise = this.attemptConnection();

        try {
            await this.connectionPromise;
        } finally {
            this.connectionPromise = null;
        }
    }

    /**
     * 尝试连接到WebSocket中间件服务器
     */
    async attemptConnection() {
        const maxAttempts = MiddlewareWebSocketSender.DEFAULTS.RETRY_COUNT;
        let attempts = 0;

        while (attempts < maxAttempts && !this.isConnected) {
            const wsUrl = MiddlewareWebSocketSender.DEFAULTS.MIDDLEWARE_WS_URL;
            // console.log(`🔗 尝试连接WebSocket中间件 (${attempts + 1}/${maxAttempts}): ${wsUrl}`);

            try {
                await this.connectToUrl(wsUrl);
                // console.log(`✅ WebSocket中间件连接成功: ${wsUrl}`);
                break;
            } catch (error) {
                console.warn(`❌ 连接失败: ${wsUrl} - ${error.message}`);

                attempts++;

                // 如果不是最后一次尝试，等待一段时间再重试
                if (attempts < maxAttempts) {
                    await this.sleep(MiddlewareWebSocketSender.DEFAULTS.RECONNECT_DELAY);
                }
            }
        }

        if (!this.isConnected) {
            throw new Error('WebSocket中间件连接失败');
        }

        // 处理队列中的消息
        this.processMessageQueue();
    }

    /**
     * 连接到指定URL的WebSocket
     */
    connectToUrl(url) {
        return new Promise((resolve, reject) => {
            try {
                this.ws = new WebSocket(url);

                const timeout = setTimeout(() => {
                    this.ws.close();
                    reject(new Error('连接超时'));
                }, MiddlewareWebSocketSender.DEFAULTS.TIMEOUT);

                this.ws.onopen = () => {
                    clearTimeout(timeout);
                    this.isConnected = true;
                    this.reconnectAttempts = 0;
                    this.startHeartbeat();
                    console.log('🎉 WebSocket中间件连接建立成功');
                    resolve();
                };

                this.ws.onmessage = (event) => {
                    this.handleMessage(event.data);
                };

                this.ws.onclose = (event) => {
                    clearTimeout(timeout);
                    this.isConnected = false;
                    this.stopHeartbeat();
                    console.log(`🔌 WebSocket中间件连接关闭: ${event.code} ${event.reason}`);

                    // 如果不是主动关闭，尝试重连
                    if (event.code !== 1000) {
                        this.scheduleReconnect();
                    }
                };

                this.ws.onerror = (error) => {
                    clearTimeout(timeout);
                    this.isConnected = false;
                    reject(error);
                };

            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * 发送格式化内容到中间件
     */
    async sendFormattedContent(content, imageUrl = null) {
        try {
            const messageData = {
                type: 'formatted_content',
                content: content,
                messageId: this.generateContentBasedMessageId(content, imageUrl),
                timestamp: Date.now(),
                source: 'jd_plugin_middleware'
            };

            if (imageUrl) {
                messageData.imageUrl = imageUrl;
            }

            return await this.sendMessage(messageData);
        } catch (error) {
            console.error('❌ 发送格式化内容到中间件失败:', error);
            throw error;
        }
    }

    /**
     * 发送消息（通用方法）
     */
    async sendMessage(messageData) {
        try {
            // 确保连接已建立
            if (!this.isConnected) {
                console.log('🔄 WebSocket中间件未连接，正在建立连接...');
                await this.connect();
            }

            // 再次检查连接状态
            if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
                // 将消息加入队列
                this.messageQueue.push(messageData);
                console.log('📝 消息已加入队列，等待中间件连接建立');
                return { success: true, message: '消息已排队，等待发送' };
            }

            // 发送消息
            const messageStr = JSON.stringify(messageData);
            this.ws.send(messageStr);

            console.log('📤 消息发送成功到中间件:', messageData.messageId);
            console.log('📄 消息内容:', messageData.content?.substring(0, 100) + '...');

            return { success: true, messageId: messageData.messageId };

        } catch (error) {
            console.error('❌ 发送消息到中间件失败:', error);

            // 如果发送失败，将消息加入队列并尝试重连
            this.messageQueue.push(messageData);
            this.scheduleReconnect();

            return { success: false, error: error.message };
        }
    }

    /**
     * 处理消息队列
     */
    async processMessageQueue() {
        if (this.messageQueue.length === 0) return;

        console.log(`📦 处理消息队列，共 ${this.messageQueue.length} 条消息`);

        const messages = [...this.messageQueue];
        this.messageQueue = [];

        for (const message of messages) {
            try {
                await this.sendMessage(message);
                // 消息间隔，避免发送过快
                await this.sleep(100);
            } catch (error) {
                console.error('❌ 队列消息发送失败:', error);
                // 失败的消息重新加入队列
                this.messageQueue.push(message);
            }
        }
    }

    /**
     * 处理收到的消息
     */
    handleMessage(data) {
        try {
            const message = JSON.parse(data);

            switch (message.type) {
                case 'connection_confirmed':
                    console.log('✅ 中间件确认连接:', message.message);
                    break;
                case 'received':
                    console.log('✅ 中间件确认收到消息:', message.messageId);
                    break;
                case 'processed':
                    console.log('✅ 中间件处理完成:', message.messageId, '耗时:', message.duration + 'ms');
                    break;
                case 'error':
                    console.error('❌ 中间件错误:', message.message);
                    break;
                case 'pong':
                    console.log('💓 中间件心跳响应正常');
                    break;
                default:
                    console.log('📨 收到中间件消息:', message);
            }
        } catch (error) {
            console.error('❌ 中间件消息解析失败:', error);
        }
    }

    /**
     * 开始心跳
     */
    startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            if (this.isConnected && this.ws && this.ws.readyState === WebSocket.OPEN) {
                this.ws.send(JSON.stringify({
                    type: 'ping',
                    timestamp: Date.now()
                }));
            }
        }, MiddlewareWebSocketSender.DEFAULTS.HEARTBEAT_INTERVAL);
    }

    /**
     * 停止心跳
     */
    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }

    /**
     * 计划重连
     */
    scheduleReconnect() {
        if (this.reconnectAttempts >= MiddlewareWebSocketSender.DEFAULTS.RETRY_COUNT) {
            console.error('❌ 已达到最大重连次数，停止重连');
            return;
        }

        this.reconnectAttempts++;
        const delay = MiddlewareWebSocketSender.DEFAULTS.RECONNECT_DELAY * this.reconnectAttempts;

        console.log(`🔄 计划重连中间件 (${this.reconnectAttempts}/${MiddlewareWebSocketSender.DEFAULTS.RETRY_COUNT})，${delay / 1000}秒后执行`);

        setTimeout(async () => {
            try {
                await this.connect();
            } catch (error) {
                console.error('❌ 重连中间件失败:', error);
            }
        }, delay);
    }

    /**
     * 生成消息ID（随机）
     */
    generateMessageId() {
        return 'middleware_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
    }

    /**
     * 基于内容生成稳定的消息ID（用于去重）
     */
    generateContentBasedMessageId(content, _imageUrl = null) {
        // 提取商品链接作为唯一标识
        const linkMatch = content.match(/https:\/\/u\.jd\.com\/[A-Za-z0-9]+/);
        if (linkMatch) {
            const link = linkMatch[0];
            // 使用链接的最后部分作为ID的一部分
            const linkId = link.split('/').pop();
            return 'content_' + linkId;
        }

        // 如果没有找到链接，尝试提取商品标题
        const lines = content.split('\n');
        const titleLine = lines[0] || '';

        // 移除特殊字符，只保留中文、英文、数字
        const cleanTitle = titleLine.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '');

        if (cleanTitle.length > 10) {
            // 使用标题的简单哈希
            let hash = 0;
            for (let i = 0; i < cleanTitle.length; i++) {
                const char = cleanTitle.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // 转换为32位整数
            }
            return 'content_title_' + Math.abs(hash).toString(36);
        }

        // 如果都没有，回退到随机ID
        console.warn('无法生成基于内容的消息ID，使用随机ID');
        return this.generateMessageId();
    }

    /**
     * 睡眠函数
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 获取连接状态
     */
    getStatus() {
        return {
            isConnected: this.isConnected,
            middlewareUrl: MiddlewareWebSocketSender.DEFAULTS.MIDDLEWARE_WS_URL,
            queueLength: this.messageQueue.length,
            reconnectAttempts: this.reconnectAttempts,
            readyState: this.ws ? this.ws.readyState : -1
        };
    }

    /**
     * 关闭连接
     */
    close() {
        console.log('🔌 关闭中间件WebSocket连接');
        this.stopHeartbeat();
        if (this.ws) {
            this.ws.close(1000, '主动关闭');
        }
        this.isConnected = false;
    }
}

// 导出全局实例
window.middlewareWebSocketSender = new MiddlewareWebSocketSender();

console.log('🔄 中间件WebSocket发送模块加载完成');
