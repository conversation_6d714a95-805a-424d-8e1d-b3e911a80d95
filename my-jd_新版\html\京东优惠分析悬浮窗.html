<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <title>京东优惠分析</title>
    <link rel="stylesheet" href="../styles/purchase-analysis.css">
</head>

<body>
    <div id="purchase-analysis-root-container">
        <!-- 简化的仪表板 - 点击展开详细分析 -->
        <div id="dashboard-container">
            <div class="dashboard-header">
                <span>优惠分析</span>
                <button id="toggle-dashboard" style="float: right; background: none; border: none; color: white; cursor: pointer;">▼</button>
            </div>
            <div class="dashboard-content">
                <div class="dashboard-item">
                    <span class="dashboard-label">最优单价</span>
                    <span class="dashboard-value" id="dashboard-best-price">计算中...</span>
                </div>
                <div class="dashboard-item">
                    <span class="dashboard-label">最优购买数</span>
                    <span class="dashboard-value" id="dashboard-best-quantity">计算中...</span>
                </div>
                <div class="dashboard-item">
                    <span class="dashboard-label">最优总价</span>
                    <span class="dashboard-value" id="dashboard-total-price">计算中...</span>
                </div>
                <div class="dashboard-item">
                    <span class="dashboard-label">节省金额</span>
                    <span class="dashboard-value" id="dashboard-savings">计算中...</span>
                </div>
            </div>
        </div>

        <!-- 详细分析面板 - 默认隐藏 -->
        <div id="purchase-analysis-container" class="hidden">
            <div class="header">
                <span>购买分析报告</span>
                <div>
                    <button id="copy-text-btn">复制文案</button>
                    <button id="add-to-cart-btn">加车购买</button>
                </div>
                <button id="close-analysis-btn">×</button>
            </div>
            <div class="content">
                <div class="summary">
                    <div class="summary-item">
                        <p>最优单价/原价</p>
                        <p><span id="best-price">¥0.00</span> / <span id="original-price">¥0.00</span></p>
                    </div>
                    <div class="summary-item">
                        <p>最优购买数</p>
                        <p><span id="best-quantity">×1</span> / <a href="#" id="quick-add-cart">快速加车</a></p>
                    </div>
                    <div class="summary-item">
                        <p>最优总价/原价</p>
                        <p><span id="total-best-price">¥0.00</span> / <span id="total-original-price">¥0.00</span></p>
                    </div>
                </div>
                
                <div class="sub-summary">
                    <span id="unit-price-info">单位价格信息计算中...</span>
                </div>

                <div class="promotions">
                    <p>参与促销&优惠券:</p>
                    <ul id="promotion-list">
                        <li>正在分析优惠信息...</li>
                    </ul>
                </div>

                <div class="details">
                    <div class="tabs">
                        <button class="tab-link active">详细分析表</button>
                        <div class="switch-view">
                            数量范围:
                            <select id="quantity-range">
                                <option value="1-10">1-10件</option>
                                <option value="1-20">1-20件</option>
                                <option value="1-50">1-50件</option>
                            </select>
                        </div>
                    </div>
                    <div id="details-table" class="tab-content">
                        <table>
                            <thead>
                                <tr>
                                    <th>数量</th>
                                    <th>到手单价</th>
                                    <th>到手总价</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="analysis-table-body">
                                <tr>
                                    <td colspan="4" style="text-align: center;">正在计算优惠信息...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入优惠算法模块 -->
    <script src="../js/优惠算法模块.js"></script>
    
    <script>
        // 京东优惠分析悬浮窗主逻辑
        class JDPromotionFloatingPanel {
            constructor() {
                this.calculator = new JDPromotionCalculator();
                this.currentProductData = null;
                this.analysisResults = {};
                this.isDetailPanelVisible = false;
                
                this.init();
            }

            init() {
                this.bindEvents();
                this.makeContainerDraggable();
                this.startProductDataMonitoring();
            }

            bindEvents() {
                // 仪表板点击展开/收起
                document.getElementById('dashboard-container').addEventListener('click', () => {
                    this.toggleDetailPanel();
                });

                // 关闭按钮
                document.getElementById('close-analysis-btn').addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.hideDetailPanel();
                });

                // 复制文案按钮
                document.getElementById('copy-text-btn').addEventListener('click', () => {
                    this.copyPromotionText();
                });

                // 加车购买按钮
                document.getElementById('add-to-cart-btn').addEventListener('click', () => {
                    this.addToCart();
                });

                // 数量范围选择
                document.getElementById('quantity-range').addEventListener('change', (e) => {
                    this.updateAnalysisTable(e.target.value);
                });
            }

            makeContainerDraggable() {
                const container = document.getElementById('purchase-analysis-root-container');
                const header = document.querySelector('.dashboard-header');
                
                let isDragging = false;
                let currentX;
                let currentY;
                let initialX;
                let initialY;
                let xOffset = 0;
                let yOffset = 0;

                header.addEventListener('mousedown', (e) => {
                    if (e.target.tagName === 'BUTTON') return;
                    
                    initialX = e.clientX - xOffset;
                    initialY = e.clientY - yOffset;
                    isDragging = true;
                    header.style.cursor = 'grabbing';
                });

                document.addEventListener('mousemove', (e) => {
                    if (isDragging) {
                        e.preventDefault();
                        currentX = e.clientX - initialX;
                        currentY = e.clientY - initialY;
                        xOffset = currentX;
                        yOffset = currentY;
                        
                        container.style.transform = `translate(${currentX}px, ${currentY}px)`;
                    }
                });

                document.addEventListener('mouseup', () => {
                    isDragging = false;
                    header.style.cursor = 'grab';
                });
            }

            startProductDataMonitoring() {
                // 监控页面变化，获取商品数据
                this.detectProductData();
                
                // 定期检查页面变化
                setInterval(() => {
                    this.detectProductData();
                }, 2000);
            }

            detectProductData() {
                try {
                    // 尝试从页面获取商品数据
                    const productData = this.extractProductDataFromPage();
                    
                    if (productData && JSON.stringify(productData) !== JSON.stringify(this.currentProductData)) {
                        this.currentProductData = productData;
                        this.calculatePromotions();
                    }
                } catch (error) {
                    console.log('获取商品数据失败:', error);
                }
            }

            extractProductDataFromPage() {
                // 从京东页面提取商品数据的逻辑
                // 这里需要根据京东页面的实际结构来实现
                
                // 尝试获取商品价格
                const priceElement = document.querySelector('.p-price .price, .summary-price .p-price .price');
                const price = priceElement ? parseFloat(priceElement.textContent.replace(/[^\d.]/g, '')) : null;
                
                if (!price) return null;

                // 构造基本的商品数据结构
                return {
                    price: price,
                    // 这里需要添加更多的数据提取逻辑
                    // 包括优惠券、促销信息等
                    preferenceInfo: {
                        coupons: [],
                        promotions: []
                    }
                };
            }

            calculatePromotions() {
                if (!this.currentProductData) return;

                try {
                    // 计算1-20件的优惠情况
                    this.analysisResults = {};
                    let bestResult = null;
                    let bestQuantity = 1;

                    for (let quantity = 1; quantity <= 20; quantity++) {
                        const result = this.calculator.calculateOptimalDiscount(this.currentProductData, quantity);
                        this.analysisResults[quantity] = result;

                        // 找到最优购买数量
                        if (!bestResult || result.finalUnitPrice < bestResult.finalUnitPrice) {
                            bestResult = result;
                            bestQuantity = quantity;
                        }
                    }

                    this.updateDashboard(bestResult, bestQuantity);
                    this.updateDetailPanel(bestResult, bestQuantity);
                    
                } catch (error) {
                    console.error('计算优惠失败:', error);
                    this.showError('计算优惠信息失败');
                }
            }

            updateDashboard(bestResult, bestQuantity) {
                if (!bestResult) return;

                document.getElementById('dashboard-best-price').textContent = `¥${bestResult.finalUnitPrice.toFixed(2)}`;
                document.getElementById('dashboard-best-quantity').textContent = `×${bestQuantity}`;
                document.getElementById('dashboard-total-price').textContent = `¥${bestResult.finalPrice.toFixed(2)}`;
                
                const savings = (bestResult.originalPrice * bestQuantity) - bestResult.finalPrice;
                document.getElementById('dashboard-savings').textContent = `¥${savings.toFixed(2)}`;
            }

            updateDetailPanel(bestResult, bestQuantity) {
                if (!bestResult) return;

                // 更新摘要信息
                document.getElementById('best-price').textContent = `¥${bestResult.finalUnitPrice.toFixed(2)}`;
                document.getElementById('original-price').textContent = `¥${bestResult.originalPrice.toFixed(2)}`;
                document.getElementById('best-quantity').textContent = `×${bestQuantity}`;
                document.getElementById('total-best-price').textContent = `¥${bestResult.finalPrice.toFixed(2)}`;
                document.getElementById('total-original-price').textContent = `¥${(bestResult.originalPrice * bestQuantity).toFixed(2)}`;

                // 更新促销信息
                this.updatePromotionList(bestResult);
                
                // 更新分析表格
                this.updateAnalysisTable('1-10');
            }

            updatePromotionList(result) {
                const promotionList = document.getElementById('promotion-list');
                promotionList.innerHTML = '';

                // 显示应用的优惠券
                if (result.appliedCoupons && result.appliedCoupons.length > 0) {
                    result.appliedCoupons.forEach(coupon => {
                        const li = document.createElement('li');
                        li.innerHTML = `
                            <span style="color: #ff4400;">-¥${coupon.discountAmount.toFixed(2)}</span>
                            <span>${coupon.description}</span>
                        `;
                        promotionList.appendChild(li);
                    });
                }

                // 显示应用的促销
                if (result.appliedPromotions && result.appliedPromotions.length > 0) {
                    result.appliedPromotions.forEach(promotion => {
                        const li = document.createElement('li');
                        li.innerHTML = `
                            <span style="color: #ff4400;">-¥${promotion.discountAmount.toFixed(2)}</span>
                            <span>${promotion.text || promotion.method}</span>
                        `;
                        promotionList.appendChild(li);
                    });
                }

                if (promotionList.children.length === 0) {
                    promotionList.innerHTML = '<li>暂无可用优惠</li>';
                }
            }

            updateAnalysisTable(range) {
                const [start, end] = range.split('-').map(Number);
                const tbody = document.getElementById('analysis-table-body');
                tbody.innerHTML = '';

                for (let quantity = start; quantity <= end; quantity++) {
                    const result = this.analysisResults[quantity];
                    if (!result) continue;

                    const row = document.createElement('tr');
                    const isOptimal = this.isOptimalQuantity(quantity);
                    
                    row.innerHTML = `
                        <td>${quantity}</td>
                        <td>¥${result.finalUnitPrice.toFixed(2)}</td>
                        <td>¥${result.finalPrice.toFixed(2)}</td>
                        <td>
                            ${isOptimal ? '<button class="optimal">最优</button>' : '<button class="sub-optimal">普通</button>'}
                            <a href="#" onclick="jdPromotionPanel.addToCartWithQuantity(${quantity})">加车</a>
                        </td>
                    `;
                    
                    tbody.appendChild(row);
                }
            }

            isOptimalQuantity(quantity) {
                const result = this.analysisResults[quantity];
                if (!result) return false;

                // 检查是否是最优单价
                const allResults = Object.values(this.analysisResults);
                const minUnitPrice = Math.min(...allResults.map(r => r.finalUnitPrice));
                
                return Math.abs(result.finalUnitPrice - minUnitPrice) < 0.01;
            }

            toggleDetailPanel() {
                const panel = document.getElementById('purchase-analysis-container');
                const toggleBtn = document.getElementById('toggle-dashboard');
                
                if (this.isDetailPanelVisible) {
                    this.hideDetailPanel();
                } else {
                    this.showDetailPanel();
                }
            }

            showDetailPanel() {
                const panel = document.getElementById('purchase-analysis-container');
                const toggleBtn = document.getElementById('toggle-dashboard');
                
                panel.classList.remove('hidden');
                toggleBtn.textContent = '▲';
                this.isDetailPanelVisible = true;
            }

            hideDetailPanel() {
                const panel = document.getElementById('purchase-analysis-container');
                const toggleBtn = document.getElementById('toggle-dashboard');
                
                panel.classList.add('hidden');
                toggleBtn.textContent = '▼';
                this.isDetailPanelVisible = false;
            }

            copyPromotionText() {
                // 生成并复制优惠文案
                const bestQuantity = this.getBestQuantity();
                const result = this.analysisResults[bestQuantity];
                
                if (!result) return;

                const text = this.generatePromotionText(result, bestQuantity);
                
                navigator.clipboard.writeText(text).then(() => {
                    alert('优惠文案已复制到剪贴板');
                }).catch(() => {
                    // 降级方案
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    alert('优惠文案已复制到剪贴板');
                });
            }

            generatePromotionText(result, quantity) {
                const savings = (result.originalPrice * quantity) - result.finalPrice;
                
                return `🎉 京东优惠分析
📦 最优购买：${quantity}件
💰 到手单价：¥${result.finalUnitPrice.toFixed(2)}
💵 到手总价：¥${result.finalPrice.toFixed(2)}
🎁 节省金额：¥${savings.toFixed(2)}
⭐ 优惠详情：${this.getPromotionSummary(result)}`;
            }

            getPromotionSummary(result) {
                const promotions = [];
                
                if (result.appliedCoupons) {
                    result.appliedCoupons.forEach(coupon => {
                        promotions.push(coupon.description);
                    });
                }
                
                if (result.appliedPromotions) {
                    result.appliedPromotions.forEach(promotion => {
                        promotions.push(promotion.text || promotion.method);
                    });
                }
                
                return promotions.length > 0 ? promotions.join('、') : '暂无优惠';
            }

            getBestQuantity() {
                let bestQuantity = 1;
                let bestUnitPrice = Infinity;
                
                Object.entries(this.analysisResults).forEach(([quantity, result]) => {
                    if (result.finalUnitPrice < bestUnitPrice) {
                        bestUnitPrice = result.finalUnitPrice;
                        bestQuantity = parseInt(quantity);
                    }
                });
                
                return bestQuantity;
            }

            addToCart() {
                const bestQuantity = this.getBestQuantity();
                this.addToCartWithQuantity(bestQuantity);
            }

            addToCartWithQuantity(quantity) {
                // 这里实现加车逻辑
                // 可以调用现有的一键购买模块
                console.log(`准备加车 ${quantity} 件商品`);
                alert(`准备加车 ${quantity} 件商品（功能开发中）`);
            }

            showError(message) {
                document.getElementById('dashboard-best-price').textContent = '错误';
                document.getElementById('dashboard-best-quantity').textContent = '错误';
                document.getElementById('dashboard-total-price').textContent = '错误';
                document.getElementById('dashboard-savings').textContent = '错误';
                
                console.error('京东优惠分析错误:', message);
            }
        }

        // 初始化悬浮面板
        let jdPromotionPanel;
        
        // 等待页面加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                jdPromotionPanel = new JDPromotionFloatingPanel();
            });
        } else {
            jdPromotionPanel = new JDPromotionFloatingPanel();
        }
    </script>
</body>

</html>
