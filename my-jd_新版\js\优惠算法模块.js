/**
 * 京东插件 - 优惠算法模块（完善版）
 * 职责：动态解析和计算API返回的各种优惠信息
 * 核心逻辑：原价*数量=总价，应用符合条件的优惠，计算到手单价
 *
 * 优惠分类：
 * 1. 优惠券：满*享*折、满*减*
 * 2. 促销：首购礼金、限购、单品满件折、多买优惠、满减、PLUS专享立减
 *
 * 不可叠加规则：同一类型的优惠券/促销不可叠加，选择最优的一个
 * 特殊规则：多买优惠和满件折按text分组，同一text的不同数量优惠不可叠加
 */

class JDPromotionCalculator {
    constructor() {
      this.debug = true;

      // 优惠券类型正则表达式（动态匹配）
      this.couponPatterns = {
        // 满*享*折
        fullDiscount: /满(\d+(?:\.\d+)?)享(\d+(?:\.\d+)?)折/,
        // 满*减*
        fullReduction: /满(\d+(?:\.\d+)?)减(\d+(?:\.\d+)?)/
      };

      // 促销类型正则表达式（动态匹配）
      this.promotionPatterns = {
        // 首购礼金（下单立减*元）
        firstBuy: /下单立减(\d+(?:\.\d+)?)元/,
        // 限购（限*件特惠）
        limitPurchase: /限(\d+)件特惠/,
        // 单品满件折（满*件享*折）
        itemFullDiscount: /满(\d+)件享(\d+(?:\.\d+)?)折/,
        // 多买优惠（满*件，总价打*折）
        multiBuy: /满(\d+)件[，,]?总价打(\d+(?:\.\d+)?)折/,
        // 满减（满*元减*元 或 满*减*）
        fullReduction: /满(\d+(?:\.\d+)?)(?:元)?减(\d+(?:\.\d+)?)(?:元)?/,
        // 满减百分比（满*元，可减*% 或 满*元立减*%）
        fullReductionPercent: /满(\d+(?:\.\d+)?)元[，,]?(?:可减|立减)(\d+(?:\.\d+)?)%/,
        // 固定总价选件（*元选*件）
        fixedPriceSelect: /(\d+(?:\.\d+)?)元选(\d+)件/,
        // PLUS专享立减（PLUS专享立减*元 或 PLUS*折 立减*元）
        plusDiscount: /(?:PLUS专享立减(\d+(?:\.\d+)?)元|PLUS(\d+(?:\.\d+)?)折.*?立减(\d+(?:\.\d+)?)元)/,
        // 官方立减（官方立减*% 或 官方立减*元）
        officialReduction: /官方立减(\d+(?:\.\d+)?)(%|元)?/
      };
    }

    /**
     * 主入口：计算商品的所有优惠信息
     * @param {Object} productData - API返回的商品数据
     * @param {number} quantity - 购买数量，默认1
     * @returns {Object} 标准化的优惠计算结果
     */
    calculatePromotions(productData, quantity = 1) {
      try {
        if (!productData) {
          throw new Error('商品数据为空');
        }

        // 提取基础价格信息
        const originalPrice = this.extractPrice(productData);
        if (originalPrice <= 0) {
          throw new Error('无法解析商品价格');
        }

        // 检查限购信息（优先级最高）
        const limitInfo = this.parseLimitPurchaseInfo(productData);
        let calculableQuantity = quantity;
        let limitExceeded = false;

        if (limitInfo && quantity > limitInfo.limit) {
          calculableQuantity = limitInfo.limit;
          limitExceeded = true;
          this.log(`限购检查: 请求数量${quantity}超出限购数量${limitInfo.limit}，只计算${calculableQuantity}件的优惠`);
        }

        // 初始化结果对象
        const result = {
          originalPrice: originalPrice,
          quantity: limitExceeded ? calculableQuantity : quantity,  // 超出限购时只返回限购数量
          totalPrice: originalPrice * (limitExceeded ? calculableQuantity : quantity),
          totalDiscount: 0,
          finalPrice: 0,
          finalUnitPrice: 0,
          appliedCoupons: [],
          appliedPromotions: [],
          allCoupons: [],
          allPromotions: [],
          limitInfo: limitInfo,
          limitExceeded: limitExceeded,
          calculableQuantity: calculableQuantity,
          requestedQuantity: quantity  // 保存用户请求的数量
        };

        // 解析优惠券
        const coupons = this.parseCoupons(productData);
        result.allCoupons = coupons;

        // 解析促销
        const promotions = this.parsePromotions(productData);
        result.allPromotions = promotions;

        // 应用最优优惠组合（基于可计算数量）
        this.applyOptimalDiscounts(result, coupons, promotions, calculableQuantity);

        // 如果超出限购，直接停止计算，不累加超出部分的价格
        if (limitExceeded) {
          // 只返回限购数量内的优惠计算结果，不累加超出部分
          result.finalUnitPrice = result.finalPrice / calculableQuantity;
          this.log(`限购计算完成: 仅计算限购内${calculableQuantity}件，优惠后${result.finalPrice}元，单价${result.finalUnitPrice}元`);
        } else {
          // 计算最终单价
          result.finalUnitPrice = result.finalPrice / quantity;
        }

        this.log('优惠计算完成:', result);
        return result;

      } catch (error) {
        console.error('优惠计算出错:', error);
        return {
          originalPrice: 0,
          quantity: quantity,
          totalPrice: 0,
          totalDiscount: 0,
          finalPrice: 0,
          finalUnitPrice: 0,
          appliedCoupons: [],
          appliedPromotions: [],
          allCoupons: [],
          allPromotions: [],
          error: error.message
        };
      }
    }

    /**
     * 提取商品价格
     * @param {Object} productData
     * @returns {number} 商品价格
     */
    extractPrice(productData) {
      // 优先使用 price.p 字段作为原价
      if (productData.price && productData.price.p) {
        return parseFloat(productData.price.p);
      }
      // 备选：price.op
      if (productData.price && productData.price.op) {
        return parseFloat(productData.price.op);
      }
      // 备选：直接的 price 字段
      if (productData.price && typeof productData.price === 'string') {
        return parseFloat(productData.price);
      }
      // 最后备选：m 字段
      if (productData.m) {
        return parseFloat(productData.m);
      }
      return 0;
    }

    /**
     * 解析优惠券
     * @param {Object} productData
     * @returns {Array} 解析后的优惠券数组
     */
    parseCoupons(productData) {
      const coupons = [];

      // 从 preferenceInfo.coupons 字段解析（京东标准结构）
      if (productData.preferenceInfo && productData.preferenceInfo.coupons && Array.isArray(productData.preferenceInfo.coupons)) {
        productData.preferenceInfo.coupons.forEach(coupon => {
          const parsed = this.parseSingleCoupon(coupon);
          if (parsed) coupons.push(parsed);
        });
      }

      // 从 couponList 字段解析（备选）
      if (productData.couponList && Array.isArray(productData.couponList)) {
        productData.couponList.forEach(coupon => {
          const parsed = this.parseSingleCoupon(coupon);
          if (parsed) coupons.push(parsed);
        });
      }

      return coupons;
    }

    /**
     * 解析单个优惠券
     * @param {Object} coupon
     * @returns {Object|null} 解析后的优惠券对象
     */
    parseSingleCoupon(coupon) {
      if (!coupon || !coupon.desc) return null;

      const description = coupon.desc;

      // 满*享*折
      const fullDiscountMatch = description.match(this.couponPatterns.fullDiscount);
      if (fullDiscountMatch) {
        return {
          type: 'coupon',
          subType: 'fullDiscount',
          description: description,
          threshold: parseFloat(fullDiscountMatch[1]),
          discount: parseFloat(fullDiscountMatch[2]) / 10, // 转为小数
          value: coupon.value || 0,
          raw: coupon
        };
      }

      // 满*减*
      const fullReductionMatch = description.match(this.couponPatterns.fullReduction);
      if (fullReductionMatch) {
        return {
          type: 'coupon',
          subType: 'fullReduction',
          description: description,
          threshold: parseFloat(fullReductionMatch[1]),
          reduction: parseFloat(fullReductionMatch[2]),
          value: coupon.value || 0,
          raw: coupon
        };
      }

      return null;
    }

    /**
     * 解析促销
     * @param {Object} productData
     * @returns {Array} 解析后的促销数组
     */
    parsePromotions(productData) {
      const promotions = [];

      // 从 preferenceInfo.promotions 字段解析（京东标准结构）
      if (productData.preferenceInfo && productData.preferenceInfo.promotions && Array.isArray(productData.preferenceInfo.promotions)) {
        productData.preferenceInfo.promotions.forEach(promo => {
          const parsed = this.parseSinglePromotion(promo);
          if (parsed) promotions.push(parsed);
        });
      }

      // 从 promotions 字段解析（备选）
      if (productData.promotions && Array.isArray(productData.promotions)) {
        productData.promotions.forEach(promo => {
          const parsed = this.parseSinglePromotion(promo);
          if (parsed) promotions.push(parsed);
        });
      }

      // 从其他可能的促销字段解析（备选）
      if (productData.promotion && Array.isArray(productData.promotion)) {
        productData.promotion.forEach(promo => {
          const parsed = this.parseSinglePromotion(promo);
          if (parsed) promotions.push(parsed);
        });
      }

      return promotions;
    }
    /**
     * 解析单个促销
     * @param {Object} promotion
     * @returns {Object|null} 解析后的促销对象
     */
    parseSinglePromotion(promotion) {
      if (!promotion) return null;

      // 使用 text 字段作为促销名称
      const promotionName = promotion.text || '';
      if (!promotionName) return null;

      // 智能选择促销方法信息源
      const promotionMethod = this.selectBestPromotionMethod(promotion);
      if (!promotionMethod) return null;

      // 优先检查官方立减促销（支持百分比形式）
      if (this.isOfficialReductionPromotion(promotionName, promotionMethod)) {
        const officialReductionInfo = this.parseOfficialReduction(promotionMethod, promotion.value);
        if (officialReductionInfo) {
          return {
            type: 'promotion',
            subType: 'officialReduction',
            text: promotionName,
            method: promotionMethod,
            reductionPercent: officialReductionInfo.reductionPercent,
            reduction: officialReductionInfo.reduction,
            value: promotion.value || 0,
            raw: promotion
          };
        }
      }

      // 智能识别"立减/减"促销：优先检查所有包含"立减"或"减"的促销
      const reductionAmount = this.extractReductionAmount(promotionMethod, promotion.value);
      if (reductionAmount > 0) {
        // 根据促销类型进一步分类
        if (this.isPlusPromotion(promotionName, promotionMethod)) {
          return {
            type: 'promotion',
            subType: 'plusDiscount',
            text: promotionName,
            method: promotionMethod,
            reduction: reductionAmount,
            value: promotion.value || 0,
            raw: promotion
          };
        } else if (this.isFirstBuyPromotion(promotionName, promotionMethod)) {
          return {
            type: 'promotion',
            subType: 'firstBuy',
            text: promotionName,
            method: promotionMethod,
            reduction: reductionAmount,
            value: promotion.value || 0,
            raw: promotion
          };
        } else if (this.isFullReductionPromotion(promotionName, promotionMethod)) {
          // 满减需要同时解析门槛和减免金额
          const fullReductionInfo = this.parseFullReduction(promotionMethod, promotion.value);
          if (fullReductionInfo) {
            const subType = fullReductionInfo.isEveryFull ? 'everyFullReduction' :
              (fullReductionInfo.isPercentReduction ? 'fullReductionPercent' : 'fullReduction');
            const result = {
              type: 'promotion',
              subType: subType,
              text: promotionName,
              method: promotionMethod,
              threshold: fullReductionInfo.threshold,
              value: promotion.value || 0,
              raw: promotion
            };

            if (fullReductionInfo.isPercentReduction) {
              result.reductionPercent = fullReductionInfo.reductionPercent;
            } else {
              result.reduction = fullReductionInfo.reduction;
              result.maxReduction = fullReductionInfo.maxReduction;
              result.isEveryFull = fullReductionInfo.isEveryFull;
            }

            return result;
          }
        } else {
          // 其他类型的立减促销，按通用立减处理
          return {
            type: 'promotion',
            subType: 'generalReduction',
            text: promotionName,
            method: promotionMethod,
            reduction: reductionAmount,
            value: promotion.value || 0,
            raw: promotion
          };
        }
      }

      // 限购促销（tag=3 或匹配限购模式）
      const limitMatch = promotionMethod.match(this.promotionPatterns.limitPurchase);
      const isLimitPromotion = promotion.tag === 3 || promotionName.includes('限购') || limitMatch;

      if (isLimitPromotion) {
        let limit = 0;
        let specialPrice = null;

        // 优先从已有的正则匹配提取
        if (limitMatch) {
          limit = parseInt(limitMatch[1]);
        }

        // 如果tag=3，使用更全面的解析
        if (promotion.tag === 3) {
          const limitInfo = this.parseSingleLimitPurchase(promotion);
          if (limitInfo) {
            limit = limitInfo.limit;
            specialPrice = limitInfo.specialPrice;
          }
        }

        // 如果仍然没有解析到限购数量，尝试其他模式
        if (limit === 0) {
          const patterns = [/限(\d+)件/, /购买1-(\d+)件/, /限购(\d+)件/];
          for (const pattern of patterns) {
            const match = (promotionMethod + ' ' + (promotion.value || '')).match(pattern);
            if (match) {
              limit = parseInt(match[1]);
              break;
            }
          }
        }

        if (limit > 0) {
          const result = {
            type: 'promotion',
            subType: 'limitPurchase',
            text: promotionName,
            method: promotionMethod,
            limit: limit,
            value: promotion.value || 0,
            raw: promotion
          };

          if (specialPrice !== null) {
            result.specialPrice = specialPrice;
          }

          return result;
        }
      }

      // 固定总价选件（*元选*件）：优先检查value字段
      const fixedPriceSelectMatch = promotionMethod.match(this.promotionPatterns.fixedPriceSelect) ||
        (promotion.value && promotion.value.match(this.promotionPatterns.fixedPriceSelect));

      if (fixedPriceSelectMatch) {
        const fixedPrice = parseFloat(fixedPriceSelectMatch[1]);
        const requiredQuantity = parseInt(fixedPriceSelectMatch[2]);

        return {
          type: 'promotion',
          subType: 'fixedPriceSelect',
          text: promotionName,
          method: promotionMethod,
          fixedPrice: fixedPrice,
          requiredQuantity: requiredQuantity,
          value: promotion.value || 0,
          raw: promotion
        };
      }

      // 多买优惠/单品满件折/单品满件减：增强匹配模式
      // 匹配各种折扣表达方式：满*件享*折、满*件打*折、单品立享*折、立享*折等
      const discountMatch = promotionMethod.match(/满(\d+)件[打享](\d+(?:\.\d+)?)折/) ||
        promotionMethod.match(/单品立享(\d+(?:\.\d+)?)折/) ||
        promotionMethod.match(/立享(\d+(?:\.\d+)?)折/) ||
        promotionMethod.match(/享(\d+(?:\.\d+)?)折/);

      // 匹配各种减免表达方式：满*件减*元
      const reductionMatch = promotionMethod.match(/满(\d+)件减(\d+(?:\.\d+)?)元?/);

      // 同时检查value字段中的折扣信息
      const valueDiscountMatch = promotion.value ?
        promotion.value.match(/满(\d+)件.*?(\d+(?:\.\d+)?)折/) : null;

      // 同时检查value字段中的减免信息
      const valueReductionMatch = promotion.value ?
        promotion.value.match(/满(\d+)件减(\d+(?:\.\d+)?)元?/) : null;

      if (discountMatch || valueDiscountMatch || reductionMatch || valueReductionMatch ||
        (promotion.tag === 19 && promotion.value && promotion.value.includes('折')) ||
        (promotion.tag === 97 && (promotionMethod.includes('折') || promotionMethod.includes('减') ||
          (promotion.value && (promotion.value.includes('折') || promotion.value.includes('减')))))) {

        let threshold = 1; // 默认门槛为1件
        let discount = 1;  // 默认不打折
        let reduction = 0; // 默认不减免
        let subType = 'multiBuy'; // 默认为多买优惠

        // 优先处理shortText中的折扣匹配
        if (discountMatch) {
          if (promotionMethod.includes('单品立享') || promotionMethod.includes('立享')) {
            // 单品立享*折的格式，默认满1件
            threshold = 1;
            discount = parseFloat(discountMatch[1]) / 10;
            subType = 'itemFullDiscount';
          } else {
            // 满*件享*折的格式
            threshold = parseInt(discountMatch[1]);
            discount = parseFloat(discountMatch[2]) / 10;
            subType = promotionName.includes('多买') ? 'multiBuy' : 'itemFullDiscount';
          }
        }
        // 处理shortText中的减免匹配
        else if (reductionMatch) {
          threshold = parseInt(reductionMatch[1]);
          reduction = parseFloat(reductionMatch[2]);
          subType = 'itemFullReduction'; // 新类型：单品满件减
        }
        // 其次处理value字段中的折扣匹配
        else if (valueDiscountMatch) {
          threshold = parseInt(valueDiscountMatch[1]);
          discount = parseFloat(valueDiscountMatch[2]) / 10;
          subType = promotion.value.includes('总价') ? 'multiBuy' : 'itemFullDiscount';
        }
        // 处理value字段中的减免匹配
        else if (valueReductionMatch) {
          threshold = parseInt(valueReductionMatch[1]);
          reduction = parseFloat(valueReductionMatch[2]);
          subType = 'itemFullReduction'; // 新类型：单品满件减
        }
        // 特殊处理tag=97的单品满件折/满件减
        else if (promotion.tag === 97) {
          // 判断是折扣还是减免
          if (promotion.value && promotion.value.includes('减')) {
            subType = 'itemFullReduction';
            // 解析第一个档位作为显示用途，实际计算时会动态选择最优档位
            const firstTierMatch = promotion.value.match(/满(\d+)件减(\d+(?:\.\d+)?)元?/);
            if (firstTierMatch) {
              threshold = parseInt(firstTierMatch[1]);
              reduction = parseFloat(firstTierMatch[2]);
            }
          } else {
            subType = 'itemFullDiscount';
            // 解析第一个档位作为显示用途，实际计算时会动态选择最优档位
            const firstTierMatch = promotion.value.match(/满(\d+)件.*?(\d+(?:\.\d+)?)折/);
            if (firstTierMatch) {
              threshold = parseInt(firstTierMatch[1]);
              discount = parseFloat(firstTierMatch[2]) / 10;
            }
          }
        }
        // 特殊处理tag=19的多买优惠
        else if (promotion.tag === 19) {
          subType = 'multiBuy';
          // 保存原始value，在计算时动态选择档位
          threshold = 1; // 临时设置，计算时会重新确定
          discount = 1;  // 临时设置，计算时会重新确定
        }

        const result = {
          type: 'promotion',
          subType: subType,
          text: promotionName,
          method: promotionMethod,
          threshold: threshold,
          value: promotion.value || 0,
          raw: promotion
        };

        // 根据类型添加对应的字段
        if (subType === 'itemFullReduction') {
          result.reduction = reduction;
        } else {
          result.discount = discount;
        }

        return result;
      }

      return null;
    }

    /**
     * 从value字段解析促销（当没有shortText时使用）
     * @param {Object} promotion
     * @param {string} promotionName
     * @param {string} valueText
     * @returns {Object|null}
     */
    parsePromotionFromValue(promotion, promotionName, valueText) {
      // 智能识别"立减/减"促销：优先检查所有包含"立减"或"减"的促销
      const reductionAmount = this.extractReductionAmount(valueText, '');
      if (reductionAmount > 0) {
        // 根据促销类型进一步分类
        if (this.isPlusPromotion(promotionName, valueText)) {
          return {
            type: 'promotion',
            subType: 'plusDiscount',
            text: promotionName,
            method: valueText,
            reduction: reductionAmount,
            value: promotion.value || 0,
            raw: promotion
          };
        } else if (this.isFirstBuyPromotion(promotionName, valueText)) {
          return {
            type: 'promotion',
            subType: 'firstBuy',
            text: promotionName,
            method: valueText,
            reduction: reductionAmount,
            value: promotion.value || 0,
            raw: promotion
          };
        } else if (this.isFullReductionPromotion(promotionName, valueText)) {
          // 满减需要同时解析门槛和减免金额
          const fullReductionInfo = this.parseFullReduction(valueText, '');
          if (fullReductionInfo) {
            const subType = fullReductionInfo.isEveryFull ? 'everyFullReduction' : 'fullReduction';
            return {
              type: 'promotion',
              subType: subType,
              text: promotionName,
              method: valueText,
              threshold: fullReductionInfo.threshold,
              reduction: fullReductionInfo.reduction,
              maxReduction: fullReductionInfo.maxReduction,
              isEveryFull: fullReductionInfo.isEveryFull,
              value: promotion.value || 0,
              raw: promotion
            };
          }
        } else {
          // 其他类型的立减促销，按通用立减处理
          return {
            type: 'promotion',
            subType: 'generalReduction',
            text: promotionName,
            method: valueText,
            reduction: reductionAmount,
            value: promotion.value || 0,
            raw: promotion
          };
        }
      }

      // 如果无法解析为减免类型，返回null
      return null;
    }

    /**
     * 智能提取"立减/减"后的数字金额
     * @param {string} text - 要解析的文本
     * @param {string} value - value字段（备选）
     * @returns {number} 提取的金额，没有则返回0
     */
    extractReductionAmount(text, value = '') {
      const combinedText = `${text} ${value}`.toLowerCase();

      // 匹配各种"立减"和"减"的表达方式
      const patterns = [
        /立减(\d+(?:\.\d+)?)元?/g,
        /减(\d+(?:\.\d+)?)元?/g,
        /优惠(\d+(?:\.\d+)?)元?/g,
        /省(\d+(?:\.\d+)?)元?/g
      ];

      let maxAmount = 0;
      for (const pattern of patterns) {
        let match;
        while ((match = pattern.exec(combinedText)) !== null) {
          const amount = parseFloat(match[1]);
          if (amount > maxAmount) {
            maxAmount = amount;
          }
        }
        // 重置正则的lastIndex
        pattern.lastIndex = 0;
      }

      return maxAmount;
    }

    /**
     * 判断是否为PLUS促销
     */
    isPlusPromotion(promotionName, promotionMethod) {
      const combinedText = `${promotionName} ${promotionMethod}`.toLowerCase();
      return combinedText.includes('plus') || combinedText.includes('会员');
    }

    /**
     * 判断是否为首购促销
     */
    isFirstBuyPromotion(promotionName, promotionMethod) {
      const combinedText = `${promotionName} ${promotionMethod}`.toLowerCase();
      return combinedText.includes('首购') || combinedText.includes('新用户') || combinedText.includes('首单');
    }
    /**
     * 判断是否为满减促销（包括每满减）
     */
    isFullReductionPromotion(promotionName, promotionMethod) {
      const combinedText = `${promotionName} ${promotionMethod}`.toLowerCase();
      return /满\d+.*?减/.test(combinedText) ||
        /满.*?元.*?减/.test(combinedText) ||
        /每满.*?减/.test(combinedText) ||
        /每满.*?可减/.test(combinedText) ||
        /满.*?可减/.test(combinedText) ||
        /满.*?元.*?可减.*?%/.test(combinedText) ||
        /满.*?元.*?立减.*?%/.test(combinedText);
    }

    /**
     * 判断是否为官方立减促销
     */
    isOfficialReductionPromotion(promotionName, promotionMethod) {
      const combinedText = `${promotionName} ${promotionMethod}`.toLowerCase();
      return combinedText.includes('官方立减') || 
             (combinedText.includes('官方') && combinedText.includes('立减'));
    }

    /**
     * 解析官方立减促销信息
     * 支持百分比和固定金额两种形式
     */
    parseOfficialReduction(promotionMethod, value = '') {
      const combinedText = `${promotionMethod} ${value}`;
      
      // 匹配官方立减百分比：官方立减15%
      const percentMatch = combinedText.match(/官方立减(\d+(?:\.\d+)?)%/);
      if (percentMatch) {
        return {
          reductionPercent: parseFloat(percentMatch[1]),
          reduction: 0
        };
      }
      
      // 匹配官方立减固定金额：官方立减24元
      const amountMatch = combinedText.match(/官方立减(\d+(?:\.\d+)?)元?/);
      if (amountMatch) {
        return {
          reductionPercent: 0,
          reduction: parseFloat(amountMatch[1])
        };
      }
      
      // 如果没有直接匹配到，尝试从数字中提取百分比
      const numberMatch = combinedText.match(/(\d+(?:\.\d+)?)%/);
      if (numberMatch && combinedText.includes('官方')) {
        return {
          reductionPercent: parseFloat(numberMatch[1]),
          reduction: 0
        };
      }
      
      return null;
    }

    /**
     * 解析满减促销的门槛和减免金额（支持每满减）
     */
    parseFullReduction(promotionMethod, value = '') {
      const combinedText = `${promotionMethod} ${value}`;

      // 匹配每满减格式1：每满X元减Y元
      const everyFullMatch1 = combinedText.match(/每满(\d+(?:\.\d+)?)元?.*?减(\d+(?:\.\d+)?)元?/);
      if (everyFullMatch1) {
        // 提取最大减免金额（如果有）
        const maxReductionMatch = combinedText.match(/最多.*?减(\d+(?:\.\d+)?)元?/);
        const maxReduction = maxReductionMatch ? parseFloat(maxReductionMatch[1]) : null;

        return {
          threshold: parseFloat(everyFullMatch1[1]),
          reduction: parseFloat(everyFullMatch1[2]),
          maxReduction: maxReduction,
          isEveryFull: true
        };
      }

      // 匹配每满减格式2：每满X元，可减Y元现金，最多可减Z元
      const everyFullMatch2 = combinedText.match(/每满(\d+(?:\.\d+)?)元?.*?可减(\d+(?:\.\d+)?)元?/);
      if (everyFullMatch2) {
        // 提取最大减免金额
        const maxReductionMatch = combinedText.match(/最多.*?减(\d+(?:\.\d+)?)元?/);
        const maxReduction = maxReductionMatch ? parseFloat(maxReductionMatch[1]) : null;

        return {
          threshold: parseFloat(everyFullMatch2[1]),
          reduction: parseFloat(everyFullMatch2[2]),
          maxReduction: maxReduction,
          isEveryFull: true
        };
      }

      // 匹配满减百分比格式：满X元，可减Y% 或 满X元立减Y%
      const fullReductionPercentMatch = combinedText.match(/满(\d+(?:\.\d+)?)元[，,]?(?:可减|立减)(\d+(?:\.\d+)?)%/);
      if (fullReductionPercentMatch) {
        return {
          threshold: parseFloat(fullReductionPercentMatch[1]),
          reductionPercent: parseFloat(fullReductionPercentMatch[2]),
          isEveryFull: false,
          isPercentReduction: true
        };
      }

      // 匹配普通满减格式：满X减Y、满X元减Y元等
      const patterns = [
        /满(\d+(?:\.\d+)?)元?减(\d+(?:\.\d+)?)元?/,
        /满(\d+(?:\.\d+)?)减(\d+(?:\.\d+)?)/,
        /满(\d+(?:\.\d+)?)元?.*?可减(\d+(?:\.\d+)?)元?/
      ];

      for (const pattern of patterns) {
        const match = combinedText.match(pattern);
        if (match) {
          // 提取最大减免金额（如果有）
          const maxReductionMatch = combinedText.match(/最多.*?减(\d+(?:\.\d+)?)元?/);
          const maxReduction = maxReductionMatch ? parseFloat(maxReductionMatch[1]) : null;

          return {
            threshold: parseFloat(match[1]),
            reduction: parseFloat(match[2]),
            maxReduction: maxReduction,
            isEveryFull: false  // 标记为普通满减
          };
        }
      }

      return null;
    }

    /**
     * 应用最优优惠组合（按照用户算法：基于原价分别计算，最后统一减去总折扣）
     * @param {Object} result
     * @param {Array} coupons
     * @param {Array} promotions
     * @param {number} quantity
     */
    applyOptimalDiscounts(result, coupons, promotions, quantity) {
      const originalTotalPrice = result.totalPrice; // 保存原始总价
      result.totalDiscount = 0;
      result.appliedCoupons = [];
      result.appliedPromotions = [];

      this.log('开始优惠计算（用户算法），原始总价:', originalTotalPrice, '数量:', quantity);

      // 按text分组促销，同text的不可叠加
      const promotionGroups = this.groupPromotionsByText(promotions);
      this.log('促销分组结果:', promotionGroups);

      // 选择最优促销组合
      const bestPromotions = this.selectBestPromotions(promotionGroups, originalTotalPrice, quantity);
      this.log('选择的最优促销:', bestPromotions);

      // 选择最优优惠券（只能选一张）
      const bestCoupon = this.selectBestCoupon(coupons, originalTotalPrice);
      if (bestCoupon) {
        this.log('选择的最优优惠券:', bestCoupon);
      }

      // 用户算法：基于原价分别计算所有折扣，最后统一减去
      let totalDiscountAmount = 0;

      // 应用促销折扣（基于原价）
      for (const promotion of bestPromotions) {
        const promotionDiscount = this.calculatePromotionDiscount(promotion, originalTotalPrice, quantity);
        if (promotionDiscount > 0) {
          totalDiscountAmount += promotionDiscount;

          // 构建应用的促销对象，使用增强后的信息
          const appliedPromotion = {
            ...promotion,
            discountAmount: promotionDiscount
          };

          // 如果有当前方法信息，使用它来显示
          if (promotion.currentMethod) {
            appliedPromotion.method = promotion.currentMethod;
          }

          result.appliedPromotions.push(appliedPromotion);
          this.log(`应用促销[${promotion.text}]: -${promotionDiscount}元`);
        }
      }

      // 应用优惠券折扣（基于原价）
      if (bestCoupon) {
        const couponDiscount = this.calculateCouponDiscount(bestCoupon, originalTotalPrice);
        if (couponDiscount > 0) {
          totalDiscountAmount += couponDiscount;
          result.appliedCoupons.push({
            ...bestCoupon,
            discountAmount: couponDiscount
          });
          this.log(`应用优惠券[${bestCoupon.description}]: -${couponDiscount}元`);
        }
      }

      // 计算最终价格
      result.totalDiscount = totalDiscountAmount;
      result.finalPrice = Math.max(0, originalTotalPrice - totalDiscountAmount);

      this.log(`总优惠: -${totalDiscountAmount}元, 最终价格: ${result.finalPrice}元`);
    }

    /**
     * 按text和promoTags分组促销
     * 分组规则：
     * 1. 没有promoTags时，按text字段分组
     * 2. 有promoTags时，promoTags数组内数值相同的视为同一组（即使text和tag不同）
     * 3. 同一组内的促销不可叠加，选择最优的一个
     * @param {Array} promotions
     * @returns {Object} 分组后的促销对象
     */
    groupPromotionsByText(promotions) {
      const groups = {};
      
      promotions.forEach(promotion => {
        let groupKey;
        
        // 检查是否有promoTags字段
        if (promotion.promoTags && Array.isArray(promotion.promoTags) && promotion.promoTags.length > 0) {
          // 有promoTags时，使用promoTags的第一个值作为分组依据
          // 将promoTags数组排序后转换为字符串，确保相同标签组合的促销被分为同一组
          const sortedTags = [...promotion.promoTags].sort((a, b) => a - b);
          groupKey = `promoTags_${sortedTags.join('_')}`;
          
          this.log(`促销[${promotion.text}]有promoTags${JSON.stringify(promotion.promoTags)}，分组key: ${groupKey}`);
        } else {
          // 没有promoTags时，使用原有的text字段分组
          groupKey = `text_${promotion.text || 'unknown'}`;
          
          this.log(`促销[${promotion.text}]无promoTags，使用text分组，分组key: ${groupKey}`);
        }
        
        if (!groups[groupKey]) {
          groups[groupKey] = [];
        }
        groups[groupKey].push(promotion);
      });
      
      this.log('促销分组结果:', Object.keys(groups).map(key => ({
        groupKey: key,
        count: groups[key].length,
        promotions: groups[key].map(p => ({text: p.text, tag: p.tag, promoTags: p.promoTags}))
      })));
      
      return groups;
    }

    /**
     * 选择最优促销组合
     * @param {Object} promotionGroups
     * @param {number} totalPrice
     * @param {number} quantity
     * @returns {Array} 最优促销数组
     */
    selectBestPromotions(promotionGroups, totalPrice, quantity) {
      const bestPromotions = [];

      // 每个分组选择最优的一个
      Object.keys(promotionGroups).forEach(text => {
        const groupPromotions = promotionGroups[text];
        let bestPromotion = null;
        let maxDiscount = 0;

        groupPromotions.forEach(promotion => {
          const discount = this.calculatePromotionDiscount(promotion, totalPrice, quantity);
          if (discount > maxDiscount) {
            maxDiscount = discount;
            bestPromotion = this.enhancePromotionWithCurrentInfo(promotion, totalPrice, quantity);
          }
        });

        if (bestPromotion && maxDiscount > 0) {
          bestPromotions.push(bestPromotion);
        }
      });

      return bestPromotions;
    }

    /**
     * 选择最优优惠券
     * @param {Array} coupons
     * @param {number} totalPrice
     * @returns {Object|null} 最优优惠券
     */
    selectBestCoupon(coupons, totalPrice) {
      let bestCoupon = null;
      let maxDiscount = 0;

      coupons.forEach(coupon => {
        const discount = this.calculateCouponDiscount(coupon, totalPrice);
        if (discount > maxDiscount) {
          maxDiscount = discount;
          bestCoupon = coupon;
        }
      });

      return bestCoupon;
    }

    /**
     * 计算促销优惠金额
     * @param {Object} promotion
     * @param {number} totalPrice
     * @param {number} quantity
     * @returns {number} 优惠金额
     */
    calculatePromotionDiscount(promotion, totalPrice, quantity) {
      if (!promotion) return 0;

      switch (promotion.subType) {
        case 'firstBuy':
        case 'generalReduction':
          return promotion.reduction || 0;

        case 'officialReduction':
          // 官方立减：支持百分比和固定金额两种形式
          if (promotion.reductionPercent > 0) {
            // 百分比立减：原价 * 百分比
            const originalPrice = totalPrice / quantity; // 单价
            const reductionAmount = originalPrice * (promotion.reductionPercent / 100) * quantity;
            this.log(`官方立减百分比计算: 单价${originalPrice.toFixed(2)}, 数量${quantity}, 减免比例${promotion.reductionPercent}%, 减免金额${reductionAmount.toFixed(2)}`);
            return reductionAmount;
          } else if (promotion.reduction > 0) {
            // 固定金额立减：直接按数量倍增
            const reductionAmount = promotion.reduction * quantity;
            this.log(`官方立减固定金额计算: 单件减免${promotion.reduction}, 数量${quantity}, 总减免${reductionAmount}`);
            return reductionAmount;
          }
          return 0;

        case 'plusDiscount':
          // PLUS立减按数量倍增
          return (promotion.reduction || 0) * quantity;

        case 'fullReduction':
          // 满减：检查是否满足门槛，并支持最大减免金额限制
          if (totalPrice >= (promotion.threshold || 0)) {
            const reduction = promotion.reduction || 0;
            const maxReduction = promotion.maxReduction || null;

            // 如果有最大减免金额限制，进行限制
            if (maxReduction !== null && reduction > maxReduction) {
              return maxReduction;
            }
            return reduction;
          }
          return 0;

        case 'everyFullReduction':
          // 每满减：计算可以享受多少次优惠，并支持最大减免金额限制
          const threshold = promotion.threshold || 0;
          const reduction = promotion.reduction || 0;
          const maxReduction = promotion.maxReduction || null;
          if (threshold > 0 && totalPrice >= threshold) {
            const times = Math.floor(totalPrice / threshold);
            let totalReduction = times * reduction;

            // 如果有最大减免金额限制，进行限制
            if (maxReduction !== null && totalReduction > maxReduction) {
              totalReduction = maxReduction;
              this.log(`每满减计算（有上限）: 总价${totalPrice}, 门槛${threshold}, 次数${times}, 计算减免${times * reduction}, 最大限制${maxReduction}, 实际减免${totalReduction}`);
            } else {
              this.log(`每满减计算: 总价${totalPrice}, 门槛${threshold}, 次数${times}, 总减免${totalReduction}`);
            }
            return totalReduction;
          }
          return 0;

        case 'fullReductionPercent':
          // 满减百分比：检查是否满足门槛，按百分比计算减免
          if (totalPrice >= (promotion.threshold || 0)) {
            const reductionPercent = promotion.reductionPercent || 0;
            const reductionAmount = totalPrice * (reductionPercent / 100);
            this.log(`满减百分比计算: 总价${totalPrice}, 门槛${promotion.threshold}, 减免比例${reductionPercent}%, 减免金额${reductionAmount.toFixed(2)}`);
            return reductionAmount;
          }
          return 0;

        case 'itemFullDiscount':
          // 单品满件折：支持多档位折扣
          return this.calculateItemFullDiscount(promotion, totalPrice, quantity);

        case 'itemFullReduction':
          // 单品满件减：支持多档位减免
          return this.calculateItemFullReduction(promotion, totalPrice, quantity);

        case 'multiBuy':
          // 多买优惠：动态选择最优档位
          return this.calculateMultiBuyDiscount(promotion, totalPrice, quantity);

        case 'fixedPriceSelect':
          // 固定总价选件：当数量匹配时使用固定总价
          return this.calculateFixedPriceSelectDiscount(promotion, totalPrice, quantity);

        case 'limitPurchase':
          // 限购促销通常不直接提供折扣
          return 0;

        default:
          this.log('未知促销类型:', promotion.subType);
          return 0;
      }
    }

    /**
     * 计算多买优惠
     * @param {Object} promotion
     * @param {number} totalPrice
     * @param {number} quantity
     * @returns {number} 优惠金额
     */
    calculateMultiBuyDiscount(promotion, totalPrice, quantity) {
      if (!promotion.value) return 0;

      // 解析value字段中的多档优惠
      const tiers = this.parseMultiBuyTiers(promotion.value);
      if (!tiers.length) return 0;

      // 选择当前数量下满足条件的最优档位（最高折扣）
      let bestTier = null;
      for (const tier of tiers) {
        if (quantity >= tier.threshold) {
          if (!bestTier || tier.discount < bestTier.discount) { // 折扣越小越优惠
            bestTier = tier;
          }
        }
      }

      if (bestTier) {
        const discountAmount = totalPrice * (1 - bestTier.discount);
        this.log(`多买优惠计算: 数量${quantity}, 总价${totalPrice}, 选择档位满${bestTier.threshold}件${bestTier.discount * 10}折, 优惠${discountAmount}元`);
        return discountAmount;
      }

      return 0;
    }

    /**
     * 计算单品满件折优惠
     * @param {Object} promotion
     * @param {number} totalPrice
     * @param {number} quantity
     * @returns {number} 优惠金额
     */
    calculateItemFullDiscount(promotion, totalPrice, quantity) {
      // 如果有多档位的value，解析它
      if (promotion.value && promotion.value.includes('满') && promotion.value.includes('折')) {
        const tiers = this.parseMultiBuyTiers(promotion.value);
        if (tiers.length > 0) {
          // 选择当前数量下满足条件的最优档位（最高折扣）
          let bestTier = null;
          for (const tier of tiers) {
            if (quantity >= tier.threshold) {
              if (!bestTier || tier.discount < bestTier.discount) { // 折扣越小越优惠
                bestTier = tier;
              }
            }
          }

          if (bestTier) {
            const discountAmount = totalPrice * (1 - bestTier.discount);
            this.log(`单品满件折计算: 数量${quantity}, 总价${totalPrice}, 选择档位满${bestTier.threshold}件${bestTier.discount * 10}折, 优惠${discountAmount}元`);
            return discountAmount;
          }
          return 0;
        }
      }

      // 单一档位的处理
      if (quantity >= (promotion.threshold || 1)) {
        const discountRate = 1 - (promotion.discount || 1);
        const discountAmount = totalPrice * discountRate;
        this.log(`单品满件折计算: 数量${quantity}, 总价${totalPrice}, 门槛${promotion.threshold}件, 折扣率${promotion.discount}, 优惠${discountAmount}元`);
        return discountAmount;
      }
      return 0;
    }

    /**
     * 计算单品满件减优惠
     * @param {Object} promotion
     * @param {number} totalPrice
     * @param {number} quantity
     * @returns {number} 优惠金额
     */
    calculateItemFullReduction(promotion, totalPrice, quantity) {
      // 如果有多档位的value，解析它
      if (promotion.value && promotion.value.includes('满') && promotion.value.includes('减')) {
        const tiers = this.parseItemReductionTiers(promotion.value);
        if (tiers.length > 0) {
          // 选择当前数量下满足条件的最优档位（最高减免）
          let bestTier = null;
          for (const tier of tiers) {
            if (quantity >= tier.threshold) {
              if (!bestTier || tier.reduction > bestTier.reduction) { // 减免越多越优惠
                bestTier = tier;
              }
            }
          }

          if (bestTier) {
            this.log(`单品满件减计算: 数量${quantity}, 选择档位满${bestTier.threshold}件减${bestTier.reduction}元, 优惠${bestTier.reduction}元`);
            return bestTier.reduction;
          }
          return 0;
        }
      }

      // 单一档位的处理
      if (quantity >= (promotion.threshold || 1)) {
        const reductionAmount = promotion.reduction || 0;
        this.log(`单品满件减计算: 数量${quantity}, 门槛${promotion.threshold}件, 减免${reductionAmount}元`);
        return reductionAmount;
      }
      return 0;
    }

    /**
     * 解析多买优惠档位
     * @param {string} value
     * @returns {Array} 档位数组
     */
    parseMultiBuyTiers(value) {
      const tiers = [];
      const matches = value.matchAll(/满(\d+)件.*?(\d+(?:\.\d+)?)折/g);

      for (const match of matches) {
        tiers.push({
          threshold: parseInt(match[1]),
          discount: parseFloat(match[2]) / 10
        });
      }

      // 按门槛升序排序
      return tiers.sort((a, b) => a.threshold - b.threshold);
    }

    /**
     * 解析单品满件减档位
     * @param {string} value
     * @returns {Array} 档位数组
     */
    parseItemReductionTiers(value) {
      const tiers = [];
      const matches = value.matchAll(/满(\d+)件减(\d+(?:\.\d+)?)元?/g);

      for (const match of matches) {
        tiers.push({
          threshold: parseInt(match[1]),
          reduction: parseFloat(match[2])
        });
      }

      // 按门槛升序排序
      return tiers.sort((a, b) => a.threshold - b.threshold);
    }

    /**
     * 计算固定总价选件优惠（按京东实际逻辑）
     * @param {Object} promotion
     * @param {number} totalPrice
     * @param {number} quantity
     * @returns {number} 优惠金额
     */
    calculateFixedPriceSelectDiscount(promotion, totalPrice, quantity) {
      const requiredQuantity = promotion.requiredQuantity || 0;
      const fixedPrice = promotion.fixedPrice || 0;

      // 京东实际逻辑：只要购买数量 >= 要求数量，就应用固定优惠金额
      if (quantity >= requiredQuantity && fixedPrice > 0 && requiredQuantity > 0) {
        // 计算固定优惠金额：按要求数量的原价 - 固定总价
        const originalPrice = totalPrice / quantity; // 单价
        const requiredQuantityTotalPrice = originalPrice * requiredQuantity; // 要求数量的原价总计
        const fixedDiscountAmount = Math.max(0, requiredQuantityTotalPrice - fixedPrice);

        this.log(`固定总价选件计算: 购买${quantity}件，满足${requiredQuantity}件要求`);
        this.log(`单价${originalPrice}元，${requiredQuantity}件原价${requiredQuantityTotalPrice}元，固定价${fixedPrice}元`);
        this.log(`固定优惠金额: ${fixedDiscountAmount}元（不随数量变化）`);

        // 如果固定价格高于对应数量的原价，则此促销不划算，不应用
        if (fixedDiscountAmount <= 0) {
          this.log(`固定总价选件: 固定价${fixedPrice}元不低于${requiredQuantity}件原价${requiredQuantityTotalPrice}元，不应用此促销`);
          return 0;
        }

        return fixedDiscountAmount;
      }

      // 购买数量不足要求数量时，不应用此优惠
      return 0;
    }

    /**
     * 计算优惠券优惠金额
     * @param {Object} coupon
     * @param {number} totalPrice
     * @returns {number} 优惠金额
     */
    calculateCouponDiscount(coupon, totalPrice) {
      if (!coupon || totalPrice < (coupon.threshold || 0)) return 0;

      switch (coupon.subType) {
        case 'fullDiscount':
          return totalPrice * (1 - (coupon.discount || 1));
        case 'fullReduction':
          return coupon.reduction || 0;
        default:
          return 0;
      }
    }

    /**
     * 日志输出
     * @param {...any} args
     */
    log(...args) {
      if (this.debug) {
        console.log('[JD促销计算]', ...args);
      }
    }

    /**
     * 增强促销对象，添加当前数量下的实际应用信息
     * @param {Object} promotion
     * @param {number} totalPrice
     * @param {number} quantity
     * @returns {Object} 增强后的促销对象
     */
    enhancePromotionWithCurrentInfo(promotion, totalPrice, quantity) {
      const enhanced = { ...promotion };

      // 对于单品满件折，添加当前实际应用的档位信息
      if (promotion.subType === 'itemFullDiscount' && promotion.value && promotion.value.includes('满') && promotion.value.includes('折')) {
        const tiers = this.parseMultiBuyTiers(promotion.value);
        if (tiers.length > 0) {
          // 找到当前数量下实际应用的档位
          let appliedTier = null;
          for (const tier of tiers) {
            if (quantity >= tier.threshold) {
              if (!appliedTier || tier.discount < appliedTier.discount) {
                appliedTier = tier;
              }
            }
          }

          if (appliedTier) {
            enhanced.appliedTier = appliedTier;
            enhanced.currentMethod = `满${appliedTier.threshold}件${appliedTier.discount * 10}折`;
            enhanced.currentThreshold = appliedTier.threshold;
            enhanced.currentDiscount = appliedTier.discount;
          }
        }
      }

      // 对于固定总价选件，添加当前应用状态信息
      if (promotion.subType === 'fixedPriceSelect') {
        const requiredQuantity = promotion.requiredQuantity || 0;
        const fixedPrice = promotion.fixedPrice || 0;

        enhanced.isActive = quantity >= requiredQuantity;
        enhanced.currentQuantity = quantity;
        enhanced.requiredQuantity = requiredQuantity;
        enhanced.fixedPrice = fixedPrice;

        if (enhanced.isActive) {
          // 计算固定优惠金额（京东逻辑）
          const originalPrice = totalPrice / quantity;
          const requiredQuantityTotalPrice = originalPrice * requiredQuantity;
          const fixedDiscountAmount = Math.max(0, requiredQuantityTotalPrice - fixedPrice);

          enhanced.currentMethod = `${fixedPrice}元选${requiredQuantity}件 (已生效，固定优惠${fixedDiscountAmount.toFixed(2)}元)`;
          enhanced.fixedDiscountAmount = fixedDiscountAmount;
          enhanced.unitPriceAfterFixed = (requiredQuantityTotalPrice - fixedDiscountAmount) / requiredQuantity;
        } else {
          enhanced.currentMethod = `${fixedPrice}元选${requiredQuantity}件 (未生效，需≥${requiredQuantity}件)`;
        }
      }

      // 对于满减百分比，添加当前应用状态信息
      if (promotion.subType === 'fullReductionPercent') {
        const threshold = promotion.threshold || 0;
        const reductionPercent = promotion.reductionPercent || 0;
        const isActive = totalPrice >= threshold;

        enhanced.isActive = isActive;
        enhanced.currentTotalPrice = totalPrice;
        enhanced.threshold = threshold;
        enhanced.reductionPercent = reductionPercent;

        if (isActive) {
          const reductionAmount = totalPrice * (reductionPercent / 100);
          enhanced.currentMethod = `满${threshold}元可减${reductionPercent}% (已生效，减免${reductionAmount.toFixed(2)}元)`;
          enhanced.currentReductionAmount = reductionAmount;
        } else {
          const needAmount = threshold - totalPrice;
          enhanced.currentMethod = `满${threshold}元可减${reductionPercent}% (未生效，还需${needAmount.toFixed(2)}元)`;
        }
      }

      return enhanced;
    }

    /**
     * 智能选择最佳的促销方法信息源
     * @param {Object} promotion
     * @returns {string} 最佳的促销方法描述
     */
    selectBestPromotionMethod(promotion) {
      const shortText = promotion.shortText || '';
      const value = promotion.value || '';
      const desc = promotion.desc || '';

      // 情况1：如果没有任何描述，返回空
      if (!shortText && !value && !desc) {
        return '';
      }

      // 情况2：只有一个字段有内容，直接使用
      const availableFields = [shortText, value, desc].filter(field => field.length > 0);
      if (availableFields.length === 1) {
        return availableFields[0];
      }

      // 情况3：多个字段都有内容，需要智能选择

      // 针对单品满件减/满件折（tag=97）：优先使用包含多档位信息的字段
      if (promotion.tag === 97) {
        // 检查value是否包含完整的多档位信息
        const valueTiers = this.countPromotionTiers(value);
        const shortTextTiers = this.countPromotionTiers(shortText);

        // 如果value包含更多档位信息，且包含具体的折扣/减免方法，优先使用value
        if (valueTiers > shortTextTiers &&
          value.includes('满') && value.includes('件') &&
          (value.includes('折') || value.includes('减'))) {
          this.log(`tag=97促销选择value字段: ${valueTiers}档位 vs ${shortTextTiers}档位`);
          return value;
        }

        // 否则使用shortText
        if (shortText.includes('满') && shortText.includes('件') &&
          (shortText.includes('折') || shortText.includes('减'))) {
          this.log(`tag=97促销选择shortText字段: 单一档位或更明确的描述`);
          return shortText;
        }
      }

      // 针对PLUS促销（tag=40）：优先使用包含具体折扣和立减金额的字段
      if (promotion.tag === 40) {
        // 检查shortText是否包含具体的PLUS折扣方法（如"PLUS95折 立减32.5元"）
        const shortTextHasMethod = shortText.includes('PLUS') &&
          (shortText.includes('折') || shortText.includes('立减')) &&
          /\d+(?:\.\d+)?/.test(shortText);

        // 检查value是否只是叙述性说明（如"可与PLUS价、满减、券等优惠叠加使用"）
        const valueIsDescription = value.includes('可与') || value.includes('叠加') ||
          value.includes('优惠') && !(/\d+(?:\.\d+)?[折元]/.test(value));

        // 如果shortText包含具体方法，而value只是说明，优先使用shortText
        if (shortTextHasMethod && valueIsDescription) {
          this.log(`tag=40促销选择shortText字段: 包含具体折扣方法，value为叙述说明`);
          return shortText;
        }

        // 如果shortText包含具体方法，优先使用shortText
        if (shortTextHasMethod) {
          this.log(`tag=40促销选择shortText字段: 包含具体折扣方法`);
          return shortText;
        }

        // 检查value是否包含具体的折扣方法（备选）
        if (value.includes('PLUS') &&
          (value.includes('折') || value.includes('立减')) &&
          /\d+(?:\.\d+)?/.test(value)) {
          this.log(`tag=40促销选择value字段: 包含具体折扣方法`);
          return value;
        }
      }

      // 针对每满减促销：优先使用包含完整信息的字段
      if (value.includes('每满') && value.includes('可减') && value.includes('最多')) {
        this.log(`每满减促销选择value字段: 包含完整限制信息`);
        return value;
      }

      // 针对首购礼金（tag=84）：优先使用shortText
      if (promotion.tag === 84 && shortText.includes('首购')) {
        this.log(`tag=84促销选择shortText字段: 首购礼金`);
        return shortText;
      }

      // 通用规则：检查哪个字段包含更多有用的数字信息
      const shortTextNumbers = (shortText.match(/\d+(?:\.\d+)?/g) || []).length;
      const valueNumbers = (value.match(/\d+(?:\.\d+)?/g) || []).length;

      // 如果value包含明显更多的数字信息（可能是多档位），优先使用value
      if (valueNumbers > shortTextNumbers && valueNumbers >= 2 &&
        (value.includes('满') || value.includes('减') || value.includes('折'))) {
        this.log(`通用选择value字段: 包含更多数字信息 (${valueNumbers} vs ${shortTextNumbers})`);
        return value;
      }

      // 默认情况：优先使用shortText，其次value，最后desc
      this.log(`默认选择shortText字段`);
      return shortText || value || desc;
    }

    /**
     * 计算促销描述中的档位数量
     * @param {string} text
     * @returns {number} 档位数量
     */
    countPromotionTiers(text) {
      if (!text) return 0;

      // 计算"满X件"的出现次数
      const matches = text.match(/满\d+件/g);
      return matches ? matches.length : 0;
    }

    /**
     * 解析限购信息
     * @param {Object} productData
     * @returns {Object|null} 限购信息对象
     */
    parseLimitPurchaseInfo(productData) {
      // 从 preferenceInfo.promotions 中查找限购促销（tag=3）
      if (productData.preferenceInfo && productData.preferenceInfo.promotions && Array.isArray(productData.preferenceInfo.promotions)) {
        for (const promotion of productData.preferenceInfo.promotions) {
          if (promotion.tag === 3 && promotion.text && promotion.text.includes('限购')) {
            return this.parseSingleLimitPurchase(promotion);
          }
        }
      }

      // 从其他可能的促销字段查找
      if (productData.promotions && Array.isArray(productData.promotions)) {
        for (const promotion of productData.promotions) {
          if (promotion.tag === 3 && promotion.text && promotion.text.includes('限购')) {
            return this.parseSingleLimitPurchase(promotion);
          }
        }
      }

      return null;
    }

    /**
     * 解析单个限购促销
     * @param {Object} promotion
     * @returns {Object|null} 解析后的限购信息
     */
    parseSingleLimitPurchase(promotion) {
      if (!promotion) return null;

      const shortText = promotion.shortText || '';
      const value = promotion.value || '';
      const text = promotion.text || '';

      // 从不同字段中提取限购数量
      let limit = 0;
      let specialPrice = null;

      // 匹配模式：限X件特惠、限X件、限购X件等，以及单件价模式
      const patterns = [
        /限(\d+)件特惠/,
        /限(\d+)件/,
        /限购(\d+)件/,
        /购买1-(\d+)件/
      ];

      // 优先从shortText解析
      for (const pattern of patterns) {
        const match = shortText.match(pattern);
        if (match) {
          limit = parseInt(match[1]);
          break;
        }
      }

      // 如果shortText没找到，从value解析
      if (limit === 0) {
        for (const pattern of patterns) {
          const match = value.match(pattern);
          if (match) {
            limit = parseInt(match[1]);
            break;
          }
        }
      }

      // 特殊处理：检测真正的限购 vs "至少X件"的特价
      if (limit === 0 && (value.includes('单件价') || value.includes('特价品'))) {
        // 检查是否为"至少X件"模式（实际上不限购）
        const atLeastMatch = value.match(/至少(\d+)件/);
        if (atLeastMatch) {
          // "至少X件"表示不限购，从该数量开始享受特价，返回null表示不是真正的限购
          this.log(`检测到"至少${atLeastMatch[1]}件"特价模式，不是真正限购`);
          return null;
        }

        // 检查是否明确说明了限购数量
        const explicitLimitMatch = value.match(/限购(\d+)件/) || value.match(/限(\d+)件/);
        if (explicitLimitMatch) {
          limit = parseInt(explicitLimitMatch[1]);
          this.log(`检测到明确限购${limit}件`);
        } else {
          // 如果只是"单件价"或"特价品"但没有明确限购，可能是真正的限购1件
          // 但需要更仔细地判断
          if (value.includes('单件价') && !value.includes('至少')) {
            limit = 1;
            this.log('检测到单件价模式，设置限购为1件');
          } else {
            // 其他"特价品"情况，可能不是真正限购
            this.log('检测到特价品但无明确限购信息，不视为限购');
            return null;
          }
        }
      }

      // 从customTag中提取特价（如果有）
      if (promotion.customTag && promotion.customTag.p) {
        specialPrice = parseFloat(promotion.customTag.p);
      }

      // 从value中提取特价（备选）
      if (!specialPrice && value.includes('￥')) {
        const priceMatch = value.match(/￥(\d+(?:\.\d+)?)/);
        if (priceMatch) {
          specialPrice = parseFloat(priceMatch[1]);
        }
      }

      if (limit > 0) {
        const result = {
          type: 'limitPurchase',
          limit: limit,
          text: text,
          shortText: shortText,
          value: value,
          tag: promotion.tag,
          raw: promotion
        };

        if (specialPrice !== null) {
          result.specialPrice = specialPrice;
        }

        this.log(`解析限购信息: 限购${limit}件${specialPrice ? `，特价￥${specialPrice}` : ''}`);
        return result;
      }

      return null;
    }

    /**
     * 根据批量计算结果，计算最优买数据
     * @param {Array} batchResults - 批量计算结果数组
     * @returns {Object} 最优数据对象
     */
    calculateOptimalData(batchResults) {
      if (!batchResults || batchResults.length === 0) {
        return {
          optimalUnitPrice: 0,
          optimalQuantity: 1,
          optimalTotalPrice: 0,
          minQuantity: 1,    // 小优：单价最低时的第一个数量
          maxQuantity: 1,    // 大优：单价最低时的最大数量
          minQuantityData: null,
          maxQuantityData: null,
          allOptimalQuantities: [], // 所有单价最低的数量
          savings: 0,        // 相对原价的节省金额
          savingsPercent: 0  // 节省百分比
        };
      }

      // 过滤掉超出限购的记录
      const validResults = batchResults.filter(result => !result.isLimitExceeded);

      if (validResults.length === 0) {
        return this.calculateOptimalData([]); // 返回默认值
      }

      // 找到最低单价
      const minUnitPrice = Math.min(...validResults.map(r => r.finalUnitPrice));

      // 找到所有单价等于最低单价的数量
      const optimalResults = validResults.filter(r =>
        Math.abs(r.finalUnitPrice - minUnitPrice) < 0.01 // 允许0.01的浮点数误差
      );

      // 按数量排序
      optimalResults.sort((a, b) => a.quantity - b.quantity);

      // 小优：单价最低时的第一个数量（最少买几件能享受最低单价）
      const minQuantity = optimalResults[0].quantity;
      const minQuantityData = optimalResults[0];

      // 大优：单价最低时的最大数量（最多买几件还能享受最低单价）
      const maxQuantity = optimalResults[optimalResults.length - 1].quantity;
      const maxQuantityData = optimalResults[optimalResults.length - 1];

      // 计算节省信息（基于小优数量）
      const originalTotalPrice = minQuantityData.originalPrice * minQuantityData.quantity;
      const finalTotalPrice = minQuantityData.finalPrice;
      const savings = originalTotalPrice - finalTotalPrice;
      const savingsPercent = originalTotalPrice > 0 ? (savings / originalTotalPrice * 100) : 0;

      const result = {
        optimalUnitPrice: minUnitPrice,
        optimalQuantity: minQuantity,  // 默认推荐小优数量
        optimalTotalPrice: minQuantityData.finalPrice,
        minQuantity: minQuantity,
        maxQuantity: maxQuantity,
        minQuantityData: minQuantityData,
        maxQuantityData: maxQuantityData,
        allOptimalQuantities: optimalResults.map(r => r.quantity),
        savings: savings,
        savingsPercent: savingsPercent,
        // 添加详细信息便于其他模块使用
        unitPriceComparison: {
          original: minQuantityData.originalPrice,
          optimal: minUnitPrice,
          discount: minQuantityData.originalPrice - minUnitPrice
        }
      };

      return result;
    }

    /**
     * 批量计算不同数量下的优惠信息（支持限购）
     * @param {Object} productData - API返回的商品数据
     * @param {number} maxQuantity - 最大计算数量，默认200
     * @returns {Object} 包含批量结果和最优数据的对象 { batchResults, optimal }
     */
    calculateBatchPromotions(productData, maxQuantity = 200) {
      const results = [];

      // 先解析限购信息
      const limitInfo = this.parseLimitPurchaseInfo(productData);
      let effectiveMaxQuantity = maxQuantity;

      if (limitInfo) {
        this.log(`发现限购信息: 限购${limitInfo.limit}件`);
        effectiveMaxQuantity = Math.min(maxQuantity, limitInfo.limit);
      }

      // 临时关闭debug模式以减少批量计算的日志输出
      const originalDebug = this.debug;
      this.debug = false;

      // 计算每个数量的结果
      for (let quantity = 1; quantity <= effectiveMaxQuantity; quantity++) {
        const result = this.calculatePromotions(productData, quantity);
        results.push({
          quantity: quantity,
          originalPrice: result.originalPrice,
          totalPrice: result.totalPrice,
          totalDiscount: result.totalDiscount,
          finalPrice: result.finalPrice,
          finalUnitPrice: result.finalUnitPrice,
          appliedPromotions: result.appliedPromotions,
          appliedCoupons: result.appliedCoupons,
          limitInfo: result.limitInfo,
          limitExceeded: result.limitExceeded
        });
      }

      // 恢复debug模式
      this.debug = originalDebug;

      // 计算最优数据
      const optimalData = this.calculateOptimalData(results);

      // 如果有限购且用户想计算超出限购的数量，添加提示信息
      if (limitInfo && maxQuantity > limitInfo.limit) {
        results.push({
          quantity: maxQuantity,
          isLimitExceeded: true,
          limitInfo: limitInfo,
          message: `购买超过${limitInfo.limit}件时超出限购范围，无法计算优惠信息`
        });
      }

      // 输出批量计算总结
      if (originalDebug && results.length > 0) {
        console.log(`[JD促销计算] 批量计算完成: 计算了1-${effectiveMaxQuantity}件的优惠信息`);
        console.log(`[JD促销计算] 最优单价: ${optimalData.optimalUnitPrice.toFixed(2)}元/件`);
        console.log(`[JD促销计算] 小优数量: ${optimalData.minQuantity}件, 大优数量: ${optimalData.maxQuantity}件`);
      }

      // 返回包含最优数据的结果
      return {
        batchResults: results,
        optimal: optimalData
      };
    }

  }

  // 兼容Node.js和浏览器环境
  if (typeof module !== 'undefined' && module.exports) {
    module.exports = JDPromotionCalculator;
  }

  // 浏览器环境：创建全局实例并实现事件监听架构
  if (typeof window !== 'undefined') {
    // 创建全局优惠算法模块实例
    window.jdPromotionCalculator = new JDPromotionCalculator();

    console.log('[JD-HELPER] 优惠算法模块已加载，等待数据事件...');

    // 监听统一数据事件
    window.addEventListener('JdUnifiedData', function (event) {
      console.log('[JD-HELPER] 📊 优惠算法模块收到统一数据事件:', event.detail);

      const { source, type, data, timestamp } = event.detail;

      // 只处理商品数据
      if (type !== 'product' || !data) {
        console.log('[JD-HELPER] ⏭️ 跳过非商品数据或空数据');
        return;
      }

      try {
        // 执行优惠计算（默认数量为1，批量计算200件）
        const singleResult = window.jdPromotionCalculator.calculatePromotions(data, 1);
        const batchResults = window.jdPromotionCalculator.calculateBatchPromotions(data, 200);

        // 构造计算结果事件
        const calculatedEvent = new CustomEvent('JdPromotionCalculated', {
          detail: {
            source: source,
            originalData: data,
            timestamp: Date.now(),
            calculationTimestamp: timestamp,
            results: {
              single: singleResult,           // 单件计算结果
              batch: batchResults.batchResults, // 批量计算结果数组（1-200件或限购数量）
              optimal: batchResults.optimal   // 最优数据（直接提供给所有模块使用）
            },
            calculator: 'JDPromotionCalculator'
          }
        });

        // 分发优惠计算完成事件
        document.dispatchEvent(calculatedEvent);
        console.log('[JD-HELPER] 🎯 优惠计算完成，已分发JdPromotionCalculated事件');

      } catch (error) {
        console.error('[JD-HELPER] ❌ 优惠计算出错:', error);

        // 分发错误事件
        const errorEvent = new CustomEvent('JdPromotionCalculated', {
          detail: {
            source: source,
            originalData: data,
            timestamp: Date.now(),
            calculationTimestamp: timestamp,
            error: error.message,
            results: null,
            calculator: 'JDPromotionCalculator'
          }
        });

        document.dispatchEvent(errorEvent);
      }
    });

    // 提供主动调用接口（兼容其他模块直接调用）
    window.jdPromotionCalculator.calculateWithEvents = function (productData, quantity = 1) {
      console.log('[JD-HELPER] 🔧 主动调用优惠计算接口');
      return this.calculatePromotions(productData, quantity);
    };

    window.jdPromotionCalculator.calculateBatchWithEvents = function (productData, maxQuantity = 200) {
      console.log('[JD-HELPER] 🔧 主动调用批量优惠计算接口');
      return this.calculateBatchPromotions(productData, maxQuantity);
    };
  }
