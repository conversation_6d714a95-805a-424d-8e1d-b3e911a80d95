/**
 * 毛利计算模块
 * 负责计算商品毛利并根据利润百分比设置显示颜色
 */

class ProfitCalculator {
    constructor() {
        this.profitDisplay = null;
        this.isInitialized = false;
        this.promotionData = null; // 存储来自优惠计算模块的数据

        // 监听优惠计算模块的结果
        document.addEventListener('JdPromotionCalculated', (event) => {
            if (event.detail && event.detail.results && event.detail.results.optimal) {
                this.promotionData = event.detail.results.optimal;
                console.log('毛利计算模块收到优惠计算数据:', this.promotionData);
                
                // 如果已初始化，立即刷新显示
                if (this.isInitialized) {
                    this.refresh();
                }
            }
        });
    }

    async initialize() {
        try {

            // 绑定毛利显示元素
            await this.bindProfitElement();

            // 计算并显示毛利
            this.calculateAndDisplayProfit();

            this.isInitialized = true;

        } catch (error) {
            // console.error('毛利计算模块初始化失败:', error);
        }
    }

    /**
     * 绑定毛利显示元素
     */
    async bindProfitElement() {
        try {

            // 等待元素出现（最多等待3秒）
            let attempts = 0;
            const maxAttempts = 30;

            while (attempts < maxAttempts) {
                this.profitDisplay = document.querySelector('[data-id="estimated-profit"]');

                if (this.profitDisplay) {
                    break;
                }
                await new Promise(resolve => setTimeout(resolve, 100));
                attempts++;
            }

            if (!this.profitDisplay) {
                // console.error('未找到毛利显示元素');
                return;
            }

        } catch (error) {
            // console.error('绑定毛利显示元素失败:', error);
        }
    }

    /**
     * 获取市场价格
     */
    getMarketPrice() {
        if (window.marketPriceManager && window.marketPriceManager.currentMarketPrice) {
            const price = parseFloat(window.marketPriceManager.currentMarketPrice);
            if (!isNaN(price) && price > 0) {
                return price;
            }
        }
        return null;
    }

    /**
     * 获取活动价格（从优惠计算模块）
     */
    getActivityPrice() {
        try {
            // 优先从优惠计算模块获取最优单价
            if (this.promotionData && this.promotionData.optimalUnitPrice > 0) {
                console.log('毛利计算：从优惠计算模块获取最优单价:', this.promotionData.optimalUnitPrice);
                return this.promotionData.optimalUnitPrice;
            }

            // 如果优惠计算模块没有数据，尝试从统一数据中获取原价作为备用
            const originalPrice = this.getOriginalPriceFromUnifiedData();
            if (originalPrice > 0) {
                console.log('毛利计算：优惠计算模块暂无数据，使用原价作为活动价');
                return originalPrice;
            }

            console.log('毛利计算：无法获取有效的活动价格');
            return null;
        } catch (error) {
            console.error('毛利计算：获取活动价失败:', error);
            return null;
        }
    }

    /**
     * 从统一数据中获取原价（备用方案）
     */
    getOriginalPriceFromUnifiedData() {
        try {
            // 获取当前商品SKU
            const currentSKU = this.getCurrentSKU();
            if (!currentSKU) {
                return 0;
            }

            // 从localStorage获取统一数据
            const unifiedDataKey = `jd_unified_data_${currentSKU}`;
            const unifiedDataString = localStorage.getItem(unifiedDataKey);
            
            if (!unifiedDataString) {
                return 0;
            }

            const unifiedData = JSON.parse(unifiedDataString);
            
            // 从统一数据中提取原价 (p字段)
            if (unifiedData.data && unifiedData.data.price && unifiedData.data.price.p) {
                const originalPrice = parseFloat(unifiedData.data.price.p);
                return isNaN(originalPrice) ? 0 : originalPrice;
            }

            return 0;
        } catch (error) {
            console.error('毛利计算：从统一数据获取原价失败:', error);
            return 0;
        }
    }

    /**
     * 获取当前商品SKU
     */
    getCurrentSKU() {
        const url = window.location.href;
        const match = url.match(/\/(\d+)\.html/);
        return match ? match[1] : null;
    }



    /**
     * 计算毛利
     */
    calculateProfit() {
        const marketPrice = this.getMarketPrice();
        const activityPrice = this.getActivityPrice();

        console.log('毛利计算数据:', {
            marketPrice: marketPrice,
            activityPrice: activityPrice,
            hasPromotionData: !!this.promotionData
        });

        if (marketPrice === null || isNaN(marketPrice) || marketPrice <= 0) {
            console.log('市场价格无效，无法计算毛利');
            return null;
        }

        if (activityPrice === null || isNaN(activityPrice) || activityPrice < 0) {
            console.log('活动价格无效，无法计算毛利');
            return null;
        }

        const profit = marketPrice - activityPrice;
        const profitPercentage = (marketPrice > 0) ? (profit / marketPrice) * 100 : 0;

        console.log('毛利计算结果:', {
            profit: profit.toFixed(2),
            profitPercentage: profitPercentage.toFixed(2) + '%',
            marketPrice: marketPrice,
            activityPrice: activityPrice
        });

        return {
            profit: profit,
            profitPercentage: profitPercentage,
            marketPrice: marketPrice,
            activityPrice: activityPrice
        };
    }

    /**
     * 根据利润百分比获取显示颜色
     */
    getProfitColor(profitPercentage) {
        if (profitPercentage < 0) {
            // 负数：绿色（亏损）
            return '#52c41a';
        } else if (profitPercentage >= 50) {
            // 大于50%：红色（利润很大）
            return '#ff4d4f';
        } else {
            // 正数但小于50%：蓝色（正常利润）
            return '#1890ff';
        }
    }

    /**
     * 更新毛利显示
     */
    updateProfitDisplay() {
        if (!this.profitDisplay) return;

        const profitData = this.calculateProfit();

        if (!profitData) {
            // 无法计算毛利
            this.profitDisplay.innerHTML = `<span style="color: #ff4d4f; font-weight: bold;">毛利:</span><span style="color: #999;">无数据</span>`;
            console.log('毛利显示已更新: 无数据');
            return;
        }

        const { profit, profitPercentage } = profitData;
        const color = this.getProfitColor(profitPercentage);
        const profitText = profit >= 0 ? `+¥${profit.toFixed(2)}` : `-¥${Math.abs(profit).toFixed(2)}`;

        // 使用HTML来设置不同颜色
        this.profitDisplay.innerHTML = `<span style="color: #ff4d4f; font-weight: bold;">毛利:</span><span style="color: ${color}; font-weight: bold;">${profitText}</span>`;

        console.log('毛利显示已更新:', {
            profit: profitText,
            percentage: profitPercentage.toFixed(2) + '%',
            color: color
        });
    }

    /**
     * 计算并显示毛利
     */
    calculateAndDisplayProfit() {
        console.log('开始计算并显示毛利...');
        this.updateProfitDisplay();
    }

    /**
     * 刷新毛利计算（当价格数据更新时调用）
     */
    refresh() {
        console.log('刷新毛利计算...');
        this.calculateAndDisplayProfit();
    }

    /**
     * 获取当前状态
     */
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            hasElement: !!this.profitDisplay,
            hasPromotionData: !!this.promotionData,
            profitData: this.calculateProfit()
        };
    }
}

// 创建全局实例
window.profitCalculator = new ProfitCalculator();

// 兼容性接口
window.ProfitCalculator = {
    init: () => {
        console.log('ProfitCalculator.init 被调用');
        return window.profitCalculator.initialize();
    },
    refresh: () => {
        console.log('ProfitCalculator.refresh 被调用');
        return window.profitCalculator.refresh();
    },
    getStatus: () => {
        return window.profitCalculator.getStatus();
    }
};

// 自动初始化
(function () {
    console.log('毛利计算模块自动初始化开始...');

    // 等待DOM和其他模块加载完成
    function initializeWhenReady() {
        // 检查是否是京东商品页面
        if (!location.href.includes('item.jd.com')) {
            console.log('不是京东商品页面，跳过毛利计算模块初始化');
            return;
        }

        // 等待更长时间确保所有元素和模块都已加载
        setTimeout(async () => {
            try {
                await window.profitCalculator.initialize();

                // 监听市场价格变化
                if (window.marketPriceManager) {
                    // 重写市场价格模块的更新方法，在价格更新后刷新毛利
                    const originalUpdateDisplay = window.marketPriceManager.updatePriceDisplay;
                    window.marketPriceManager.updatePriceDisplay = function () {
                        originalUpdateDisplay.call(this);
                        // 延迟刷新毛利，确保价格已更新
                        setTimeout(() => {
                            if (window.profitCalculator) {
                                window.profitCalculator.refresh();
                            }
                        }, 100);
                    };
                }

            } catch (error) {
                console.error('毛利计算模块自动初始化失败:', error);
                // 如果失败，再次尝试
                setTimeout(async () => {
                    try {
                        await window.profitCalculator.initialize();
                    } catch (retryError) {
                        console.error('毛利计算模块重试初始化也失败:', retryError);
                    }
                }, 2000);
            }
        }, 2000); // 等待2秒，确保其他模块都已加载
    }

    // 等待页面加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeWhenReady);
    } else {
        initializeWhenReady();
    }
})();

// 添加全局调试函数
window.debugProfit = function () {
    console.log('🔍 调试毛利计算模块...');
    if (window.profitCalculator) {
        console.log('📊 当前模块状态:', window.profitCalculator.getStatus());
        console.log('💰 重新计算毛利...');
        window.profitCalculator.refresh();
    } else {
        console.log('❌ profitCalculator 未初始化');
    }
};

console.log('毛利计算模块加载完成 - 已适配优惠计算模块');
console.log('💡 调试命令: window.debugProfit()');
