// 官方立减功能调试脚本
// 可以在浏览器控制台中运行此脚本来测试

console.log('🧪 开始官方立减功能调试...');

// 测试数据
const testPromotions = [
    {
        "shortText": "官方立减15%",
        "tag": 90,
        "text": "官方立减",
        "value": "官方立减15%，无使用门槛，7月31日 23:59结束"
    },
    {
        "shortText": "官方立减24元",
        "tag": 90,
        "text": "官方立减",
        "value": "官方立减24元，无使用门槛"
    }
];

// 假设已经加载了优惠算法模块
if (typeof JDPromotionCalculator !== 'undefined') {
    const calculator = new JDPromotionCalculator();
    
    console.log('✅ 优惠算法模块已加载');
    
    // 测试官方立减识别方法
    testPromotions.forEach((promo, index) => {
        console.log(`\n📊 测试促销 ${index + 1}:`, promo);
        
        // 测试识别方法
        const isOfficialReduction = calculator.isOfficialReductionPromotion(promo.text, promo.shortText);
        console.log(`🔍 是否识别为官方立减: ${isOfficialReduction}`);
        
        // 测试解析方法
        const parsedInfo = calculator.parseOfficialReduction(promo.shortText, promo.value);
        console.log(`📋 解析结果:`, parsedInfo);
        
        // 测试完整解析
        const fullParsed = calculator.parseSinglePromotion(promo);
        console.log(`🎯 完整解析结果:`, fullParsed);
    });
    
    // 测试完整计算
    console.log('\n🧮 测试完整计算...');
    const mockData = {
        p: 158.7,
        preferenceInfo: {
            promotions: [
                {
                    "crossStoreFullCut": false,
                    "formal": false,
                    "link": "//plus.jd.com/right/index#item-exclusive",
                    "logPromoId": "308050394650",
                    "proSortNum": 20,
                    "promoId": 308050394650,
                    "promoTags": [1128],
                    "shortText": "PLUS专享立减1.59元",
                    "tag": 40,
                    "text": "PLUS专享立减",
                    "typeNumber": "",
                    "value": "可与PLUS价、满减、券等优惠叠加使用"
                },
                {
                    "crossStoreFullCut": false,
                    "formal": false,
                    "logPromoId": "307354521213",
                    "proSortNum": 100,
                    "promoId": 307354521213,
                    "shortText": "单品立享8折",
                    "tag": 97,
                    "text": "单品满件折",
                    "typeNumber": "",
                    "value": "满1件享8.00折"
                },
                {
                    "crossStoreFullCut": false,
                    "formal": false,
                    "logPromoId": "286642058655",
                    "proSortNum": 190,
                    "promoId": 286642058655,
                    "shortText": "官方立减15%",
                    "tag": 90,
                    "text": "官方立减",
                    "typeNumber": "25",
                    "value": "官方立减15%，无使用门槛，7月31日 23:59结束"
                }
            ]
        }
    };
    
    const result = calculator.calculatePromotions(mockData, 1);
    console.log('🎉 完整计算结果:', result);
    
    console.log('\n📊 计算验证:');
    console.log(`原价: ¥${result.originalPrice}`);
    console.log(`总优惠: ¥${result.totalDiscount.toFixed(2)}`);
    console.log(`到手价: ¥${result.finalPrice.toFixed(2)}`);
    console.log(`到手单价: ¥${result.finalUnitPrice.toFixed(2)}`);
    
    console.log('\n🎯 应用的促销:');
    result.appliedPromotions.forEach(promo => {
        console.log(`- ${promo.text}: 减免¥${promo.discountAmount.toFixed(2)}`);
    });
    
    // 手动验证计算
    console.log('\n🔢 手动验证:');
    console.log(`京东价: ¥158.7`);
    console.log(`- PLUS立减: ¥1.59`);
    console.log(`- 单品8折减免: ¥${(158.7 * 0.2).toFixed(2)}`);
    console.log(`- 官方立减15%: ¥${(158.7 * 0.15).toFixed(2)}`);
    const expectedFinal = 158.7 - 1.59 - (158.7 * 0.2) - (158.7 * 0.15);
    console.log(`预期到手价: ¥${expectedFinal.toFixed(2)}`);
    
} else {
    console.error('❌ 优惠算法模块未加载');
}
