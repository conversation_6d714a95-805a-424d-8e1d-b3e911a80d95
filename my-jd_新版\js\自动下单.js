// 自动下单模块 - 配合一键购买功能使用
if (window.AutoOrderInitialized) {
    // console.warn('自动下单模块已初始化，跳过重复初始化。');
} else {
    window.AutoOrderInitialized = true;
    (function () {
        const AutoOrder = {
            // 配置参数
            config: {
                profitThreshold: 20, // 毛利率阈值（百分比）
                discountThreshold: 0.1, // 折扣阈值（1折）
                retryWithSingleItem: true, // 无货时是否尝试购买单件
                checkInterval: 500, // 检查订单页面的间隔(毫秒)
                orderPagePriceCheckTimeout: 2000, // 订单页面价格和库存检查总超时时间(毫秒)
                submitDelay: 600, // 点击提交订单前的延迟(毫秒)
                priceTolerance: 0.08, // 订单页总价允许的上浮比例 (8%)
                defaultOrderQuantity: 1, // 自动下单时的默认购买数量
                productPageDataFetchMaxAttempts: 10, // 商品页获取稳定价格数据的最大尝试次数
                productPageDataFetchIntervalMs: 500, // 商品页获取价格数据的间隔时间(毫秒)
            },            // SessionStorage keys
            expectedPriceKey: 'autoOrderExpectedUnitPrice_v3',
            expectedQuantityKey: 'autoOrderExpectedQuantity_v3',
            hasRetriedKey: 'autoOrderHasRetried_v3',
            autoOrderFlowKey: 'isAutoOrderFlow_v3',

            // Chrome本地存储键
            storageFlowStateKey: 'ao_flowState_v3', // Object: {isActive, unitPrice, quantity, totalPrice, productId, hasRetried}
            storageNextActionKey: 'ao_nextAction_v3', // Object: {type, quantity, source, productId, expectedUnitPrice}

            // 内部状态，从chrome.storage.local加载
            internalState: {
                isAutoFlowActive: false,
                expectedUnitPrice: null,
                expectedQuantity: null,
                expectedTotalPrice: null, // 添加期望总价字段
                productId: null,
                hasRetried: false,
                nextAction: null, // For retry mechanism
                isPriceMonitoringManuallyActive: false, // User-triggered monitoring state
                priceMonitorInterval: null, // Interval ID for price monitoring/data fetching
                dataCheckAttempts: 0, // Counter for data fetching attempts
                currentPromotionData: null, // 当前优惠算法模块数据
            },

            // 快捷键设置标志
            shortcutsSetup: false,

            // 从URL获取SKU
            getSkuFromUrl() {
                const url = window.location.href;
                const match = url.match(/\/(\d+)\.html/);
                return match ? match[1] : null;
            },

            // 初始化
            async init() {
                // console.log('自动下单模块 v3 (Storage) 初始化...');
                this.isItemPage = location.href.includes('item.jd.com');
                this.isMobileOrderPage = location.href.includes('trade.m.jd.com/order/confirm') || location.href.includes('trade.m.jd.com/pay?sceneval=');
                this.isPcOrderPage = location.href.includes('trade.jd.com/shopping/order');
                this.isOrderPage = this.isMobileOrderPage || this.isPcOrderPage;

                // 监听优惠算法模块事件
                if (this.isItemPage) {
                    document.addEventListener('JdPromotionCalculated', (event) => {
                        this.handlePromotionData(event.detail);
                    });
                }

                try {
                    await this.loadStateFromStorage(); // Load state first

                    // Proceed with initialization based on loaded state and page type
                    if (this.isItemPage) {
                        // Handle product page specific initialization
                        if (this.internalState.nextAction && this.internalState.nextAction.source === 'autoOrderRetry') {
                            // console.log('自动下单 (商品页-init): 检测到来自storage的重试流程。');
                            // Further logic for retry will be handled after dependency checks
                        } else if (this.internalState.isAutoFlowActive) {
                            // console.warn('自动下单 (商品页-init): 检测到来自storage的活动自动下单流程，但当前在商品页且非重试。可能需要清理。');
                            await this.clearAutoOrderStorage([this.storageFlowStateKey]); // Clear stale flow state
                            this.internalState.isAutoFlowActive = false; // Reset internal state
                        }

                        if (!(this.internalState.nextAction && this.internalState.nextAction.source === 'autoOrderRetry')) {
                            sessionStorage.removeItem(this.hasRetriedKey);
                            // console.log('自动下单 (商品页-init): 非storage重试流程, 清理sessionStorage的hasRetriedKey (如果存在)。');
                        }

                        this.waitForDependencies('itemPage')
                            .then(() => {
                                if (this.internalState.nextAction && this.internalState.nextAction.source === 'autoOrderRetry' && this.internalState.nextAction.productId) {
                                    // console.log('自动下单: (Storage) 检测到重试购买单件流程，直接调用一键购买。');
                                    const action = this.internalState.nextAction;
                                    this.internalState.nextAction = null;

                                    // 为即将到来的订单页设置 flowState，并明确标记 hasRetried: true
                                    const flowStateForRetryOrderPage = {
                                        isActive: true,
                                        unitPrice: action.expectedUnitPrice,
                                        quantity: 1, // 重试购买数量为1
                                        totalPrice: action.expectedUnitPrice * 1, // 重试时单价*1
                                        productId: action.productId,
                                        hasRetried: true // 关键：标记此流程为已重试
                                    };

                                    // console.log("自动下单 (商品页-retry): 设置storageFlowStateKey (for next order page):", flowStateForRetryOrderPage);
                                    chrome.storage.local.set({ [this.storageFlowStateKey]: flowStateForRetryOrderPage }, () => {
                                        if (chrome.runtime.lastError) {
                                            // console.error('自动下单 (商品页-retry): 保存flowState到storage失败:', chrome.runtime.lastError);
                                        } else {
                                            // console.log('自动下单 (商品页-retry): flowState已为重试订单页保存到storage。');
                                        }
                                    }); sessionStorage.setItem(this.autoOrderFlowKey, 'true');
                                    sessionStorage.setItem(this.expectedQuantityKey, '1');
                                    if (action.expectedUnitPrice !== null && action.expectedUnitPrice !== undefined) {
                                        sessionStorage.setItem(this.expectedPriceKey, action.expectedUnitPrice.toString());
                                    }
                                    if (action.productId) {
                                        sessionStorage.setItem('autoOrderProductId', action.productId);
                                    }
                                    sessionStorage.setItem(this.hasRetriedKey, 'true');                                    // console.log(`自动下单 (商品页-retry flow from Storage): 即将调用 openOptimalBuyNow(1) 重试1件.`);
                                    // 重试时使用固定1件购买
                                    if (window.OneClickBuy && typeof window.OneClickBuy.openOptimalBuyNow === 'function') {
                                        window.OneClickBuy.openOptimalBuyNow(1);
                                    } else {
                                        // console.error("自动下单: 重试失败 - 一键购买模块不可用!");
                                    }
                                    return;
                                } // console.log("自动下单 (商品页): 准备自动启动价格监控。");
                                this.internalState.isPriceMonitoringManuallyActive = true;
                                this.startPriceMonitoring();
                                // console.log("自动下单 (商品页): 价格监控已自动启动。");

                                // 添加快捷键监听
                                this.setupKeyboardShortcuts();
                                // console.log("自动下单 (商品页): 手动触发快捷键已设置。");
                            })
                            .catch(err => { } /* console.error('自动下单模块 (商品页) 初始化失败:', err) */);
                    } else if (this.isOrderPage) {
                        this.waitForDependencies('orderPage').then(() => {
                            this.handleOrderPage(this.isMobileOrderPage);
                            // console.log("自动下单 (订单页): 正常流程，已恢复 handleOrderPage 调用。");
                        })
                            .catch(err => { } /* console.error(`自动下单模块 (${this.isMobileOrderPage ? '移动' : 'PC'}订单页) 初始化失败:`, err) */);
                    } else {
                        // console.log("自动下单: 当前页面既不是商品页也不是订单页，模块不活动。");
                        await this.clearAutoOrderStorage();
                    }

                } catch (error) {
                    // console.error("自动下单: 初始化过程中加载状态失败:", error);
                }
            },

            // 处理优惠算法模块数据
            handlePromotionData(promotionData) {
                this.internalState.currentPromotionData = promotionData;
                // console.log('自动下单: 接收到优惠数据:', promotionData);
            },

            async loadStateFromStorage() {
                return new Promise((resolve, reject) => {
                    const keysToGet = [this.storageFlowStateKey, this.storageNextActionKey];
                    chrome.storage.local.get(keysToGet, (result) => {
                        if (chrome.runtime.lastError) {
                            // console.error('自动下单: 从chrome.storage.local读取失败:', chrome.runtime.lastError);
                            return reject(chrome.runtime.lastError);
                        }

                        // console.log('自动下单: 从storage加载的状态:', result);
                        const flowState = result[this.storageFlowStateKey];
                        const nextAction = result[this.storageNextActionKey];
                        let keysToClearImmediately = [];

                        if (flowState) {
                            this.internalState.isAutoFlowActive = flowState.isActive === true;
                            this.internalState.expectedUnitPrice = flowState.unitPrice !== undefined ? parseFloat(flowState.unitPrice) : null;
                            this.internalState.expectedQuantity = flowState.quantity !== undefined ? parseInt(flowState.quantity, 10) : null;
                            this.internalState.expectedTotalPrice = flowState.totalPrice !== undefined ? parseFloat(flowState.totalPrice) : null;
                            this.internalState.productId = flowState.productId || null;
                            this.internalState.hasRetried = flowState.hasRetried === true;
                            // console.log('自动下单: 已从storage加载 flowState:', this.internalState);
                            if (this.isOrderPage && this.internalState.isAutoFlowActive) {
                                keysToClearImmediately.push(this.storageFlowStateKey);
                                // console.log('自动下单 (订单页): flowState已加载到内存, 将从storage中清除。');
                            } else if (this.isItemPage && this.internalState.isAutoFlowActive && (!nextAction || nextAction.source !== 'autoOrderRetry')) {
                                // console.warn('自动下单 (商品页): 检测到活动的flowState但非重试场景, 可能为过时状态, 将清除。');
                                keysToClearImmediately.push(this.storageFlowStateKey);
                                this.internalState.isAutoFlowActive = false;
                            }
                        } else {
                            this.internalState.isAutoFlowActive = false;
                        }

                        if (nextAction) {
                            this.internalState.nextAction = nextAction;
                            // console.log('自动下单: 已从storage加载 nextAction:', this.internalState.nextAction);
                            if (this.isItemPage && this.internalState.nextAction.source === 'autoOrderRetry') {
                                keysToClearImmediately.push(this.storageNextActionKey);
                                // console.log('自动下单 (商品页): nextAction已加载到内存, 将从storage中清除。');
                            } else if (this.isOrderPage && nextAction) {
                                // console.warn('自动下单 (订单页): 检测到nextAction, 可能为过时状态, 将清除。');
                                keysToClearImmediately.push(this.storageNextActionKey);
                                this.internalState.nextAction = null;
                            }
                        } else {
                            this.internalState.nextAction = null;
                        }

                        if (this.internalState.nextAction && this.internalState.nextAction.source === 'autoOrderRetry' && !flowState?.hasRetried) {
                            this.internalState.hasRetried = true;
                            // console.log("自动下单: 从nextAction推断 hasRetried 为 true");
                        }

                        if (keysToClearImmediately.length > 0) {
                            chrome.storage.local.remove([...new Set(keysToClearImmediately)], () => {
                                if (chrome.runtime.lastError) {
                                    // console.error('自动下单: 清理storage中的特定键失败:', chrome.runtime.lastError);
                                } else {
                                    // console.log('自动下单: 已成功从storage中清除加载后的键:', keysToClearImmediately.join(', '));
                                }
                                resolve();
                            });
                        } else {
                            resolve();
                        }
                    });
                });
            },

            async clearAutoOrderStorage(keys = null) {
                const keysToClear = keys || [this.storageFlowStateKey, this.storageNextActionKey];
                return new Promise((resolve, reject) => {
                    if (!keysToClear || keysToClear.length === 0) {
                        // console.log("自动下单: clearAutoOrderStorage 调用时没有指定要清除的键。");
                        return resolve();
                    }
                    chrome.storage.local.remove(keysToClear, () => {
                        if (chrome.runtime.lastError) {
                            // console.error('自动下单: 清理chrome.storage.local失败:', chrome.runtime.lastError, 'Keys:', keysToClear);
                            return reject(chrome.runtime.lastError);
                        }
                        // console.log('自动下单: 已成功从storage中清除键:', keysToClear.join(', '));
                        resolve();
                    });
                });
            },

            async waitForDependencies(pageType) {
                // console.log(`自动下单: 等待 ${pageType} 依赖...`);
                return new Promise(resolve => setTimeout(() => {
                    // console.log(`自动下单: ${pageType} 依赖加载完成。`);
                    resolve();
                }, 100));
            },

            // 开始价格监控
            startPriceMonitoring() {
                if (!this.internalState.isPriceMonitoringManuallyActive) {
                    // console.log("自动下单: startPriceMonitoring - 监控标志未激活，不启动。");
                    if (this.internalState.priceMonitorInterval) {
                        clearInterval(this.internalState.priceMonitorInterval);
                        this.internalState.priceMonitorInterval = null;
                    }
                    return;
                }
                if (this.internalState.isAutoFlowActive) {
                    // console.warn("自动下单: startPriceMonitoring - 自动下单流程已通过storage激活，本次不启动新的商品页监控。");
                    this.internalState.isPriceMonitoringManuallyActive = false; // Ensure it doesn't run if a flow is already active
                    return;
                }

                const skuId = this.getSkuFromUrl();
                if (!skuId) {
                    // console.error("自动下单: startPriceMonitoring - 无法从URL获取SKU ID，监控无法启动。");
                    this.internalState.isPriceMonitoringManuallyActive = false; // Reset flag
                    return;
                }

                // console.log(`自动下单: startPriceMonitoring - SKU: ${skuId} - 准备开始一次性价格条件检查 (最多 ${this.config.productPageDataFetchMaxAttempts} 次尝试)。`);

                if (this.internalState.priceMonitorInterval) {
                    clearInterval(this.internalState.priceMonitorInterval);
                }
                this.internalState.dataCheckAttempts = 0; // Reset attempt counter

                this.internalState.priceMonitorInterval = setInterval(async () => {
                    if (!this.internalState.isPriceMonitoringManuallyActive) { // Check if monitoring was stopped externally
                        // console.log("自动下单: 数据获取循环 - 监控已手动停止，清除interval。");
                        clearInterval(this.internalState.priceMonitorInterval);
                        this.internalState.priceMonitorInterval = null;
                        await this.clearAutoOrderStorage([this.storageNextActionKey]);
                        return;
                    }

                    this.internalState.dataCheckAttempts++;
                    // console.log(`自动下单: 价格数据检查尝试 #${this.internalState.dataCheckAttempts} (SKU: ${skuId})`);
                    // 使用优惠算法模块数据
                    const promotionData = this.internalState.currentPromotionData;
                    let optimalPrice = null;
                    let originalPrice = null;
                    
                    if (promotionData && promotionData.results && promotionData.results.optimal) {
                        optimalPrice = promotionData.results.optimal.optimalUnitPrice;
                    }
                    
                    // 从originalData.price.p获取原价
                    if (promotionData && promotionData.originalData && promotionData.originalData.price && promotionData.originalData.price.p) {
                        originalPrice = promotionData.originalData.price.p;
                    }

                    const marketPrice = window.marketPriceManager && typeof window.marketPriceManager.currentMarketPrice !== 'undefined' ? parseFloat(window.marketPriceManager.currentMarketPrice) : null;

                    // Data Stability/Availability Check: optimalPrice must be valid (包括0元优惠价，null为无效).
                    if (optimalPrice !== null && !isNaN(optimalPrice) && optimalPrice >= 0) {
                        // console.log(`自动下单: SKU: ${skuId} - 价格数据已获取 (最优价: ${optimalPrice}, 原价: ${originalPrice}, 市场价: ${marketPrice}). 准备执行条件判断。`);
                        clearInterval(this.internalState.priceMonitorInterval);
                        this.internalState.priceMonitorInterval = null;

                        // 在条件判断前检查商品是否无货
                        if (this.checkProductOutOfStock()) {
                            // console.log(`自动下单: SKU: ${skuId} - 检测到商品无货，停止自动下单流程。`);
                            this.internalState.isPriceMonitoringManuallyActive = false;
                            await this.clearAutoOrderStorage([this.storageNextActionKey]);
                            return;
                        }

                        let conditionMet = false;
                        let reason = "";                        // 1. 检查最优价是否小于等于原价的1折 (0元最优价自动符合1折条件)
                        // console.log(`自动下单: 检查1折条件 - SKU: ${skuId}, 最优价: ${optimalPrice}, 原价: ${originalPrice}, 1折阈值: ${this.config.discountThreshold * 100}% (即 ${originalPrice !== null && originalPrice > 0 ? (originalPrice * this.config.discountThreshold).toFixed(2) : 'N/A'})`);

                        // 0元最优价直接符合1折条件
                        if (optimalPrice === 0) {
                            conditionMet = true;
                            reason = `0元最优价直接符合1折条件。`;
                            // console.log(`自动下单: 条件满足 (0元最优价) - SKU: ${skuId}, ${reason}`);
                        } else if (originalPrice !== null && originalPrice > 0 && optimalPrice <= originalPrice * this.config.discountThreshold) {
                            conditionMet = true;
                            reason = `最优价 ${optimalPrice} <= 原价 ${originalPrice} 的 ${this.config.discountThreshold * 100}% (即 ${(originalPrice * this.config.discountThreshold).toFixed(2)})。`;
                            // console.log(`自动下单: 条件满足 (1折) - SKU: ${skuId}, ${reason}`);
                        } else {
                            // console.log(`自动下单: 1折条件不满足 - SKU: ${skuId}, 最优价: ${optimalPrice} > 原价1折: ${originalPrice !== null && originalPrice > 0 ? (originalPrice * this.config.discountThreshold).toFixed(2) : 'N/A'}`);
                        }

                        // 2. 如果1折条件不满足，再检查毛利率 (确保 marketPrice > 0 避免除零错误)
                        if (!conditionMet && marketPrice !== null && marketPrice > 0) {
                            const profitData = window.profitCalculator && typeof window.profitCalculator.calculateProfit === 'function' ? window.profitCalculator.calculateProfit(optimalPrice, marketPrice) : null;
                            const profitPercentage = profitData && typeof profitData.profitPercentage === 'number' ? profitData.profitPercentage : -Infinity;

                            // console.log(`自动下单: 检查毛利率 - SKU: ${skuId}, 最优价: ${optimalPrice}, 市场价: ${marketPrice}, 计算后毛利率: ${profitPercentage.toFixed(2)}% (毛利阈值: ${this.config.profitThreshold}%)`);
                            if (profitPercentage >= this.config.profitThreshold) {
                                conditionMet = true;
                                reason = `毛利率 ${profitPercentage.toFixed(2)}% >= 阈值 ${this.config.profitThreshold}%。`;
                                // console.log(`自动下单: 条件满足 (毛利率) - SKU: ${skuId}, ${reason}`);
                            }
                        } else if (!conditionMet) {
                            // console.log(`自动下单: SKU: ${skuId}, 最优价: ${optimalPrice}. 未满足1折条件，且未进行毛利计算 (市场价: ${marketPrice} 无效或无需计算).`);
                        }                        if (conditionMet) {
                            // console.log(`自动下单: 最终条件满足! SKU: ${skuId}, 最优价: ${optimalPrice}. 原因: ${reason} 准备自动下单。`);
                            
                            // 获取最优数量
                            let optimalQuantity = this.config.defaultOrderQuantity;
                            if (promotionData && promotionData.results && promotionData.results.optimal && promotionData.results.optimal.optimalQuantity) {
                                optimalQuantity = promotionData.results.optimal.optimalQuantity;
                                // console.log(`自动下单: 从优惠算法模块获取到最优数量: ${optimalQuantity}`);
                            } else {
                                // console.log(`自动下单: 优惠算法模块未返回有效最优数量，使用默认数量: ${optimalQuantity}`);
                            }

                            // 为一键购买模块设置自动下单状态
                            const flowState = {
                                isActive: true,
                                unitPrice: optimalPrice,
                                quantity: optimalQuantity, // 使用最优数量
                                totalPrice: promotionData.results.optimal.optimalTotalPrice || (optimalPrice * optimalQuantity), // 优先使用算法提供的总价
                                productId: skuId,
                                hasRetried: false
                            };

                            // console.log("自动下单: 设置storageFlowStateKey:", flowState);
                            chrome.storage.local.set({ [this.storageFlowStateKey]: flowState }, () => {
                                if (chrome.runtime.lastError) {
                                    // console.error('自动下单: 保存flowState到storage失败:', chrome.runtime.lastError);
                                } else {
                                    // console.log('自动下单: flowState已保存到storage, 调用一键购买的最优购买流程。');

                                    // 设置必要的 sessionStorage 用于自动下单流程识别
                                    sessionStorage.setItem(this.autoOrderFlowKey, 'true');
                                    sessionStorage.setItem(this.expectedPriceKey, optimalPrice.toString());
                                    sessionStorage.setItem('autoOrderProductId', skuId);

                                    // 调用一键购买模块的最优购买方法
                                    if (window.OneClickBuy && typeof window.OneClickBuy.openOptimalBuyNow === 'function') {
                                        // console.log('自动下单: 调用 OneClickBuy.openOptimalBuyNow() 进行最优购买');
                                        window.OneClickBuy.openOptimalBuyNow();
                                    } else {
                                        // console.error("自动下单: 一键购买模块不可用!");
                                    }
                                }
                            });
                        } else {
                            // console.log(`自动下单: SKU: ${skuId} - 最终条件未满足 (最优价: ${optimalPrice}, 原价: ${originalPrice}, 市场价: ${marketPrice}). 停止监控。`);
                        }
                        this.internalState.isPriceMonitoringManuallyActive = false; // Ensure monitoring stops after one check cycle
                        await this.clearAutoOrderStorage([this.storageNextActionKey]); // Clean up potential retry state

                    } else if (this.internalState.dataCheckAttempts >= this.config.productPageDataFetchMaxAttempts) {
                        // console.log(`自动下单: SKU: ${skuId} - ${this.config.productPageDataFetchMaxAttempts}次尝试后主要价格数据(最优价)仍无效。停止监控。`);
                        clearInterval(this.internalState.priceMonitorInterval);
                        this.internalState.priceMonitorInterval = null;
                        this.internalState.isPriceMonitoringManuallyActive = false;
                        await this.clearAutoOrderStorage([this.storageNextActionKey]);
                    } else {
                        // console.log(`自动下单: SKU: ${skuId} - 主要价格数据(最优价: ${optimalPrice === null ? 'null(未获取到)' : optimalPrice})尚不可用，将在 ${this.config.productPageDataFetchIntervalMs}ms 后重试 (${this.internalState.dataCheckAttempts}/${this.config.productPageDataFetchMaxAttempts})。`);
                    }
                }, this.config.productPageDataFetchIntervalMs);
            }, async handleOrderPage(isMobile) {
                // console.log(`自动下单: handleOrderPage (isMobile: ${isMobile}) - 订单页处理已激活`);
                if (!this.internalState.isAutoFlowActive || this.internalState.expectedUnitPrice === null || this.internalState.expectedUnitPrice === undefined || typeof this.internalState.expectedQuantity === 'undefined') {
                    // console.log("自动下单 (订单页): 未检测到活动的自动下单流程或必要信息缺失。清理潜在的过时storage状态。");
                    await this.clearAutoOrderStorage([this.storageFlowStateKey, this.storageNextActionKey]);
                    return; // Early exit if no active flow
                }

                // console.log(`自动下单 (订单页): 流程激活。期望单价: ${this.internalState.expectedUnitPrice}, 期望数量: ${this.internalState.expectedQuantity}, 商品ID: ${this.internalState.productId}, 已重试: ${this.internalState.hasRetried}`);

                let checkAttempts = 0;
                const maxCheckAttempts = this.config.orderPagePriceCheckTimeout / this.config.checkInterval;
                let orderSubmitted = false; // Flag to stop further checks once an action (submit/retry/terminate) is decided

                const checkAndSubmit = async () => {
                    if (orderSubmitted) return; // If a terminal action has been taken, stop.
                    checkAttempts++;
                    // console.log(`自动下单 (订单页): 检查库存和价格，尝试 #${checkAttempts}`);

                    // 1. 检查库存
                    let hasStock = false;
                    if (!isMobile) { // PC端订单页
                        let stockQuery = null;
                        if (this.internalState.productId) {
                            stockQuery = `span.p-state[skuid="${this.internalState.productId}"]`;
                        } else {
                            // console.warn("自动下单 (订单页): internalState.productId 未定义，无法使用SKU特定选择器查找库存。");
                        }

                        const stockElement = stockQuery ? document.querySelector(stockQuery) : null;

                        if (stockElement) {
                            const stockText = stockElement.textContent.toLowerCase().trim();
                            // console.log(`自动下单 (订单页): 提取到库存文本: "${stockText}" (来自选择器: ${stockQuery})`);
                            if (stockText.includes('有货') || stockText.includes('现货') || stockText.match(/仅剩\s*\d+\s*件/) || stockText.includes('可配货')) {
                                hasStock = true;
                            } else if (stockText.includes('无货') || stockText.includes('缺货')) {
                                hasStock = false;
                            } else {
                                // console.warn(`自动下单 (订单页): 库存状态文本为 "${stockText}"，未明确识别。默认按无货处理。`);
                                hasStock = false;
                            }
                        } else {
                            // console.warn(`自动下单 (订单页): PC端库存状态元素 (${stockQuery || '未指定'}) 未找到。默认有货处理（需要完善）。`);
                            hasStock = true;
                        }
                    } else { // 移动端订单页
                        // console.warn("自动下单 (订单页): 移动端库存检查逻辑未实现，默认有货处理。");
                        hasStock = true;
                    }
                    // console.log(`自动下单 (订单页): 库存状态判断: ${hasStock ? '有货' : '无货'}`);

                    // 2. 检查价格
                    let currentTotalPrice = 0;
                    let priceMatch = false;

                    let totalPriceElement = null;
                    if (!isMobile) { // PC端
                        totalPriceElement = document.querySelector('#sumPayPriceId');
                    } else { // 移动端
                        totalPriceElement = document.querySelector('.checkout_price_total .price_val');
                    } if (totalPriceElement) {
                        currentTotalPrice = parseFloat(totalPriceElement.textContent.replace(/[^\d.]/g, ''));
                    } else {
                        // console.warn(`自动下单 (订单页): 未找到${isMobile ? '移动端' : 'PC端'}总价元素。`);
                    }                    // 简化的订单页总价判断逻辑：实际总价 <= 期望总价 * (1 + 8%容忍度)
                    if (!isNaN(currentTotalPrice) && currentTotalPrice >= 0) {
                        // 优先使用优惠算法模块提供的总价，回退到单价*数量
                        const expectedTotalPrice = this.internalState.expectedTotalPrice || 
                                                   (this.internalState.expectedUnitPrice * this.internalState.expectedQuantity);
                        const maxAllowedPrice = expectedTotalPrice * (1 + this.config.priceTolerance); // 期望总价 + 8%容忍度

                        if (currentTotalPrice <= maxAllowedPrice) {
                            priceMatch = true;
                            // console.log(`自动下单 (订单页): 总价匹配成功 - 当前总价: ${currentTotalPrice}, 期望总价: ${expectedTotalPrice.toFixed(2)}, 允许上限(含8%容忍度): ${maxAllowedPrice.toFixed(2)}`);
                        } else {
                            // console.log(`自动下单 (订单页): 总价超出允许范围 - 当前总价: ${currentTotalPrice}, 期望总价: ${expectedTotalPrice.toFixed(2)}, 允许上限(含8%容忍度): ${maxAllowedPrice.toFixed(2)}`);
                        }
                    } else {
                        // console.log("自动下单 (订单页): 未能提取到有效的当前总价。");
                    }
                    // console.log(`自动下单 (订单页): 价格匹配状态: ${priceMatch}`);

                    // 3. 条件判断与操作
                    if (hasStock && priceMatch) {
                        // console.log("自动下单 (订单页): 库存充足且价格匹配，准备提交订单...");
                        orderSubmitted = true; // Mark as action taken
                        await new Promise(resolve => setTimeout(resolve, this.config.submitDelay));

                        const pcSubmitBtn = document.querySelector('#order-submit');
                        const mobileSubmitBtn = document.querySelector('.checkout_btn_submit');
                        let submitClicked = false;
                        if (pcSubmitBtn && !isMobile) {
                            pcSubmitBtn.click();
                            submitClicked = true;
                        } else if (mobileSubmitBtn && isMobile) {
                            mobileSubmitBtn.click();
                            submitClicked = true;
                        }

                        if (submitClicked) {
                            // console.log("自动下单 (订单页): 模拟点击提交订单成功。");
                        } else {
                            // console.error("自动下单 (订单页): 未找到提交订单按钮！");
                            orderSubmitted = false; // Reset if click failed, to allow timeout or manual intervention
                        }
                        await this.clearAutoOrderStorage([this.storageFlowStateKey]);
                        this.internalState.isAutoFlowActive = false;
                        // console.log("自动下单 (订单页): 流程结束(提交)，flowState已清理。");
                        return;
                    }

                    // 无货处理逻辑
                    if (!hasStock) {
                        orderSubmitted = true; // Mark as action taken to prevent further checks in this cycle
                        if (this.config.retryWithSingleItem && this.internalState.expectedQuantity > 1 && !this.internalState.hasRetried) {
                            // console.log(`自动下单 (订单页): 无货 (原数量: ${this.internalState.expectedQuantity})，且允许重试单件，且尚未重试。准备重试单件购买。`);
                            const nextActionState = {
                                type: 'retrySingle',
                                productId: this.internalState.productId,
                                expectedUnitPrice: this.internalState.expectedUnitPrice,
                                quantity: 1,
                                source: 'autoOrderRetry'
                            };
                            // console.log("自动下单 (订单页): 设置storageNextActionKey (for retry):", nextActionState);
                            chrome.storage.local.set({ [this.storageNextActionKey]: nextActionState }, async () => {
                                if (chrome.runtime.lastError) {
                                    // console.error('自动下单: 保存nextActionState到storage失败:', chrome.runtime.lastError);
                                }
                                await this.clearAutoOrderStorage([this.storageFlowStateKey]);
                                this.internalState.isAutoFlowActive = false;
                                // console.log("自动下单 (订单页): flowState已清理，准备跳转回商品页进行重试。");
                                window.location.href = `https://item.jd.com/${this.internalState.productId}.html`;
                            });
                        } else {
                            if (this.internalState.hasRetried) {
                                // console.log("自动下单 (订单页): 无货，且已经重试过单件购买。流程终止。");
                            } else if (this.internalState.expectedQuantity === 1) {
                                // console.log("自动下单 (订单页): 无货 (原数量: 1)，不进行重试。流程终止。");
                            } else {
                                // console.log("自动下单 (订单页): 无货，但不满足重试条件 (可能禁止重试，或已重试)。流程终止。");
                            }
                            await this.clearAutoOrderStorage([this.storageFlowStateKey, this.storageNextActionKey]);
                            this.internalState.isAutoFlowActive = false;
                            // console.log("自动下单 (订单页): 流程因无货且不重试而终止，状态已清理。");
                        }
                        return; // 结束检查循环
                    }

                    // 超时或价格不匹配（且有货）
                    if (checkAttempts >= maxCheckAttempts) {
                        orderSubmitted = true; // Mark as action taken
                        // console.warn("自动下单 (订单页): 超过最大检查次数。");
                        if (!priceMatch && currentTotalPrice > 0) {
                            // console.log("自动下单 (订单页): 价格不匹配。");
                        } else if (!priceMatch) {
                            // console.log("自动下单 (订单页): 无法获取价格进行匹配。");
                        }
                        await this.clearAutoOrderStorage([this.storageFlowStateKey, this.storageNextActionKey]);
                        this.internalState.isAutoFlowActive = false;
                        // console.log("自动下单 (订单页): 流程因超时或最终条件不满足而终止，状态已清理。");
                        return;
                    }

                    // 如果未提交/重试/终止，且未达到最大尝试次数，则继续下一次检查
                    if (!orderSubmitted) {
                        setTimeout(checkAndSubmit, this.config.checkInterval);
                    }
                };

                // 启动第一次检查
                setTimeout(checkAndSubmit, this.config.checkInterval);
            },

            // 获取当前输入框中的数量
            getCurrentInputQuantity() {
                // 优先从购买数量输入框获取
                const buyNumInput = document.querySelector('#buy-num, .quantity-input, input[type="number"]');
                if (buyNumInput && buyNumInput.value) {
                    const value = parseInt(buyNumInput.value);
                    if (!isNaN(value) && value > 0) {
                        return value;
                    }
                }

                // 如果没有输入框或无效值，返回默认数量
                return this.config.defaultOrderQuantity || 1;
            },

            // 设置手动触发快捷键（仅在商品页）
            setupKeyboardShortcuts() {
                if (!this.isItemPage) {
                    // console.log("自动下单: 非商品页，不设置手动触发快捷键。");
                    return;
                }

                // 避免重复绑定
                if (this.shortcutsSetup) {
                    // console.log("自动下单: 快捷键已设置，跳过重复设置。");
                    return;
                }

                document.addEventListener('keydown', this.handleManualTriggerKeydown.bind(this));
                this.shortcutsSetup = true;
                // console.log("自动下单: 手动触发快捷键监听已设置 - 方向键下: 默认数量购买, Ctrl+X: 最优购买");
            },            // 处理手动触发快捷键
            handleManualTriggerKeydown(event) {
                // console.log('自动下单: 接收到键盘事件:', {
                //     key: event.key,
                //     code: event.code,
                //     ctrlKey: event.ctrlKey,
                //     shiftKey: event.shiftKey,
                //     metaKey: event.metaKey,
                //     target: event.target.tagName + (event.target.id ? '#' + event.target.id : ''),
                //     isItemPage: this.isItemPage
                // });

                // 只在商品页处理
                if (!this.isItemPage) {
                    // console.log('自动下单: 非商品页，忽略快捷键');
                    return;
                }

                // Ctrl + X - 活动价购买（即使在输入框中也允许，因为不会和正常输入冲突）
                if (event.ctrlKey && (event.key === 'x' || event.key === 'X')) {
                    event.preventDefault();
                    // console.log('自动下单: Ctrl+X触发 - 启动最优自动下单流程');
                    this.triggerManualOrder('optimal');
                    return;
                }                // 方向键下 - 使用输入框数量购买（需要特殊处理输入框情况）
                if (event.key === 'ArrowDown' && !event.ctrlKey && !event.shiftKey && !event.metaKey) {
                    event.preventDefault();
                    console.log('自动下单: 方向键下被触发');

                    // 获取当前输入框中的数量
                    let quantity = this.getCurrentInputQuantity();
                    console.log(`自动下单: 方向键下 - 获取到的数量: ${quantity}`);

                    const target = event.target;
                    const isInInput = target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable;
                    console.log(`自动下单: 方向键下 - 目标元素: ${target.tagName}${target.id ? '#' + target.id : ''}, 是否在输入框中: ${isInInput}`);

                    if (isInInput) {
                        // 如果在购买数量输入框中，先失焦再触发快捷键
                        if (target.id === 'buy-num' || target.classList.contains('quantity-input')) {
                            target.blur(); // 失焦输入框
                            console.log(`自动下单: 方向键下触发 - 从购买数量输入框失焦并启动${quantity}件自动下单流程`);
                            this.triggerManualOrder('default', quantity);
                        } else {
                            console.log('自动下单: 方向键下 - 在其他输入框中，不处理');
                        }
                        // 如果在其他输入框中，不处理方向键下
                        return;
                    } else {
                        // 不在输入框中，正常处理
                        console.log(`自动下单: 方向键下触发 - 启动${quantity}件自动下单流程`);
                        this.triggerManualOrder('default', quantity);
                    }
                }
            },

            // 触发手动下单流程
            async triggerManualOrder(type, quantity = null) {
                console.log(`自动下单: 手动触发开始 - 类型: ${type}, 指定数量: ${quantity}`);
                
                // 检查是否已有活动的自动下单流程
                if (this.internalState.isAutoFlowActive) {
                    console.warn("自动下单: 手动触发失败 - 已有活动的自动下单流程正在进行");
                    return;
                }

                const skuId = this.getSkuFromUrl();
                if (!skuId) {
                    console.error("自动下单: 手动触发失败 - 无法从URL获取SKU ID");
                    return;
                }
                console.log(`自动下单: 手动触发 - SKU ID: ${skuId}`);

                // 获取优惠算法模块数据
                const promotionData = this.internalState.currentPromotionData;
                console.log('自动下单: 手动触发 - 当前优惠数据:', promotionData);
                
                let optimalPrice = null;
                if (promotionData && promotionData.results && promotionData.results.optimal) {
                    optimalPrice = promotionData.results.optimal.optimalUnitPrice;
                }
                console.log(`自动下单: 手动触发 - 最优价格: ${optimalPrice}`);

                if (optimalPrice === null || isNaN(optimalPrice)) {
                    console.error("自动下单: 手动触发失败 - 无法获取有效的最优价格");
                    return;
                }

                let finalQuantity = quantity;

                if (type === 'optimal') {
                    // 最优购买 - 获取最优数量
                    if (promotionData && promotionData.results && promotionData.results.optimal && promotionData.results.optimal.optimalQuantity) {
                        finalQuantity = promotionData.results.optimal.optimalQuantity;
                        // console.log(`自动下单: 手动触发(最优购买) - 从优惠算法模块获取到最优数量: ${finalQuantity}`);
                    } else {
                        finalQuantity = this.config.defaultOrderQuantity;
                        // console.log(`自动下单: 手动触发(最优购买) - 未获取到最优数量，使用默认数量: ${finalQuantity}`);
                    }
                } else {
                    // 默认购买 - 使用指定数量
                    finalQuantity = finalQuantity || this.config.defaultOrderQuantity;
                }

                // console.log(`自动下单: 手动触发 - 类型: ${type}, 数量: ${finalQuantity}, 最优价: ${optimalPrice}, SKU: ${skuId}`);
                console.log(`自动下单: 手动触发 - 类型: ${type}, 最终数量: ${finalQuantity}, 最优价: ${optimalPrice}, SKU: ${skuId}`);

                // 暂停自动价格监控，避免冲突
                if (this.internalState.isPriceMonitoringManuallyActive) {
                    this.internalState.isPriceMonitoringManuallyActive = false;
                    if (this.internalState.priceMonitorInterval) {
                        clearInterval(this.internalState.priceMonitorInterval);
                        this.internalState.priceMonitorInterval = null;
                    }
                    console.log("自动下单: 手动触发 - 已停止自动价格监控");
                }

                // 设置自动下单状态
                const flowState = {
                    isActive: true,
                    unitPrice: optimalPrice,
                    quantity: finalQuantity,
                    totalPrice: (type === 'optimal' && promotionData && promotionData.results && promotionData.results.optimal) 
                                ? promotionData.results.optimal.optimalTotalPrice 
                                : (optimalPrice * finalQuantity), // 手动触发时计算总价
                    productId: skuId,
                    hasRetried: false
                };

                // console.log("自动下单: 手动触发 - 设置storageFlowStateKey:", flowState);
                chrome.storage.local.set({ [this.storageFlowStateKey]: flowState }, () => {
                    if (chrome.runtime.lastError) {
                        // console.error('自动下单: 手动触发 - 保存flowState到storage失败:', chrome.runtime.lastError);
                    } else {
                        // console.log(`自动下单: 手动触发 - flowState已保存到storage, 调用一键购买的${type === 'optimal' ? '最优购买' : '默认'}购买流程。`);

                        // 设置必要的 sessionStorage 用于自动下单流程识别
                        sessionStorage.setItem(this.autoOrderFlowKey, 'true');
                        sessionStorage.setItem(this.expectedPriceKey, optimalPrice.toString());
                        sessionStorage.setItem(this.expectedQuantityKey, finalQuantity.toString());
                        sessionStorage.setItem('autoOrderProductId', skuId);

                        // 调用对应的一键购买方法
                        if (type === 'optimal') {
                            // Ctrl+X: 最优购买流程
                            if (window.OneClickBuy && typeof window.OneClickBuy.openOptimalBuyNow === 'function') {
                                console.log('自动下单: 手动触发(Ctrl+X) - 调用 OneClickBuy.openOptimalBuyNow()');
                                window.OneClickBuy.openOptimalBuyNow();
                            } else {
                                console.error("自动下单: 手动触发失败 - 一键购买模块不可用!");
                            }
                        } else {
                            // 方向键下: 使用指定数量的常规购买流程
                            if (window.OneClickBuy && typeof window.OneClickBuy.openBuyNow === 'function') {
                                console.log(`自动下单: 手动触发(方向键下) - 调用 OneClickBuy.openBuyNow(${finalQuantity}) - 使用指定数量`);
                                window.OneClickBuy.openBuyNow(finalQuantity);
                            } else if (window.OneClickBuy && typeof window.OneClickBuy.openOptimalBuyNow === 'function') {
                                console.log(`自动下单: 手动触发(方向键下) - 回退调用 OneClickBuy.openOptimalBuyNow(${finalQuantity}) - 使用指定数量`);
                                window.OneClickBuy.openOptimalBuyNow(finalQuantity);
                            } else {
                                console.error("自动下单: 手动触发失败 - 一键购买模块不可用! 可用方法:", Object.keys(window.OneClickBuy || {}));
                            }
                        }
                    }
                });
            },

            // 检查商品是否无货
            checkProductOutOfStock() {
                try {
                    // 查找无货提示元素
                    const outOfStockElement = document.querySelector('#summary-service .delivery[style*="color: rgb(227, 125, 23)"]');

                    if (outOfStockElement) {
                        const stockText = outOfStockElement.textContent.trim();
                        // console.log(`自动下单: 检测到库存状态文本: "${stockText}"`);

                        // 严格匹配 "此商品暂时售完" 文本
                        if (stockText === "此商品暂时售完") {
                            // console.log("自动下单: 检测到商品无货 - 此商品暂时售完");
                            return true;
                        }
                    }

                    return false;
                } catch (error) {
                    // console.error("自动下单: 无货检测失败:", error);
                    // 检测失败时返回false，不阻止正常流程
                    return false;
                }
            },
        }; // End of AutoOrder object

        // Auto-initialize
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => AutoOrder.init());
        } else {
            AutoOrder.init();
        }

        window.AutoOrder = AutoOrder;
    })(); // End of IIFE
} // End of main else block
